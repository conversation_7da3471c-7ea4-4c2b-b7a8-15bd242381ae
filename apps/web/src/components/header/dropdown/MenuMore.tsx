import React, { useRef } from 'react';
import { includes } from 'lodash';
import { useVieRouter } from '@customHook';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import TrackingApp from '@tracking/functions/TrackingApp';
import { createTimeout } from '@helpers/common';
import { ICON_KEY, PAGE } from '@constants/constants';
import Styles from '../menu/Menu.module.scss';

const MenuMore = ({ subMenu, activeSubMenu, onClickMenu, isMore, isMobile }: any) => {
  const router = useVieRouter();
  const contentEl = useRef<any>(null);
  const clickTimerRef = useRef<any>(null);

  const clickSubMenu = (e: any, item: any) => {
    e.preventDefault();
    clearTimeout(clickTimerRef.current);
    clickTimerRef.current = createTimeout(() => {
      if (item?.urlRedirect) {
        window.open(item?.urlRedirect);
      } else {
        router.push(item?.href || PAGE.HOME, item?.seo?.url);
      }
    }, 500);
    TrackingApp.categorySelected({
      data: {
        ...item,
        isViewMoreMenu: true
      }
    });
    if (typeof onClickMenu === 'function') {
      onClickMenu();
    }
  };

  return (
    <div
      className={`pane pane-stroke is-open pane--more${
        !isMobile
          ? ' for-dark absolute animate-fade-right left width-large-up-178rem'
          : ' size-w-full overflow'
      }`}
      ref={contentEl}
      style={
        isMobile ? (isMore ? { height: contentEl?.current?.scrollHeight } : { height: '0px' }) : {}
      }
      data-hover-child
    >
      <div
        className={`pane-container scrollable-y over-scroll-contain ${
          !isMobile ? 'size-mh-480' : ''
        }`}
      >
        <div className="pane__body">
          <ul className="menu vertical menu--more" id="SUB_MENU">
            {(subMenu || []).map((item: any) => (
              <li
                className={`menu__item relative${!isMobile ? ' hover-light' : ''}`}
                role="none"
                key={item?.id}
              >
                <a
                  className={`${item?.id === activeSubMenu?.id ? 'active' : ''}${
                    item?.dotCode ? ' hot' : ''
                  }`}
                  title={item?.name || ''}
                  role="menuitem"
                  href={item?.seo?.url}
                  onClick={(e) => clickSubMenu(e, item)}
                >
                  {includes(ICON_KEY, item?.iconText) && (
                    <span className={Styles.MenuItemIcon}>
                      <SvgIcon type={item?.iconText} isActive />
                    </span>
                  )}
                  <span className="ml-2 ellipsis text text-12 text-large-up-16 text-gray239">
                    {item?.name || ''}
                  </span>
                  {item?.dotCode && (
                    <i className="dot" style={{ backgroundColor: item?.dotCode }} />
                  )}
                </a>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default React.memo(MenuMore);

//TODO: REMOVE
import React, { useEffect, useMemo, useState } from 'react';
import { useVieRouter } from '@customHook';
import { PAGE } from '@constants/constants';
import { useSelector } from 'react-redux';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';

let isIntro = false;
let adsSlot: any = null;
let oldPathname = '';

const MastheadAds = () => {
  const router = useVieRouter();
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const { masthead } = useSelector((state: any) => state?.App?.outStreamAds) || {};
  const { id, path, size } = masthead || {};
  const [isHide, setHide] = useState(false);
  const [isLoadSuccess, setLoadSuccess] = useState(false);
  const [isLoadFailed, setLoadFailed] = useState(false);
  const inventoryId = useMemo(() => getInventoryId(id), [id]);

  const { asPath, pathname, query } = router || {};
  useEffect(() => {
    controlAds();
  }, [asPath]);

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        setLoadSuccess(true);
        setHide(false);
      } else {
        setLoadFailed(true);
      }
    } else {
      const slot = event?.slot;
      adsSlot = slot;
      const slotId = slot ? slot.getSlotElementId() : '';
      if (event?.isEmpty) {
        onLoadedFailed({ slot: event.slot, slotId });
      } else {
        onLoadedSuccess({ slot: event.slot, slotId });
      }
    }
  };

  const controlAds = () => {
    setLoadFailed(false);
    setHide(false);
    switch (pathname) {
      case PAGE.HOME:
      case PAGE.LIVE_TV:
      case PAGE.LIVE_STREAM:
      case PAGE.SPORT:
      case PAGE.PAGE_MENU:
      case PAGE.CATEGORY:
        if (oldPathname) {
          if (!isIntro) {
            setLoadSuccess(false);
            refreshAds();
          }
        }
        oldPathname = pathname;
        setHide(false);
        break;
      default:
        oldPathname = '';
        setHide(true);
        break;
    }
    isIntro = !!query?.vid;
  };

  const onLoadedSuccess = ({ slot, slotId }: any) => {
    if (slotId === id) {
      adsSlot = slot;
      setHide(false);
      setLoadSuccess(true);
    }
  };

  const onLoadedFailed = ({ slotId }: any) => {
    if (slotId === id) {
      setLoadFailed(true);
    }
  };

  const refreshAds = () => {
    setLoadSuccess(false);
    if (adsSlot) {
      GPT.refresh([adsSlot]);
    }
  };

  const onClose = () => {
    if (adsSlot) GPT.clear([adsSlot]);
    setHide(true);
  };

  let className = 'top-ads m-b2';
  if (!isLoadSuccess) className += ' hide';

  if (isLoadFailed || isHide || !id || isMobile || profile?.isPremium) return null;

  return (
    <div className={className}>
      <div className="banner banner--market flex-box align-center">
        <div className="banner__inner container">
          <div className="banner__body text-center">
            {inventoryId ? (
              <AOutstreamAds inventoryId={inventoryId} onSlotRenderEnded={onSlotRenderEnded} />
            ) : (
              <GPT
                id={id}
                adUnitPath={path}
                slotSize={size}
                renderWhenViewable
                onSlotRenderEnded={onSlotRenderEnded}
              />
            )}
          </div>
          {isLoadSuccess && (
            <button
              className="close button absolute size-square-38 top-right-1"
              data-close=""
              onClick={onClose}
            >
              <span className="icon text-white">
                <i className="vie vie-times-medium" style={{ fontSize: '1rem' }} />
              </span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(MastheadAds);

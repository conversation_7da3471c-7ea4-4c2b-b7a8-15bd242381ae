import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import ButtonBasic from '@components/basic/Buttons/Button';
import InputCode from '@components/InputCode';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import { EL_SIZE_CLASS, EL_THEME_CLASS, HTTP_CODE } from '@constants/constants';
import { TEXT } from '@constants/text';
import { LOGIN_INPUT_TYPE, OTP_TYPE, TYPE_AUTH, TYPE_RECEIVE_OTP } from '@constants/types';
import { EVENT_NAME, trackingAuth } from '@tracking/functions/TrackingAuthentication';
import { formatPhoneNumberVN } from '@helpers/common';
import TextSupport from './TextSupport';
import ButtonBack from './ButtonBack';
import Modal from './index';
import MessageCasesInput from './MessageCasesInput';
import ConfigImage from '@config/ConfigImage';

const OTPVerify = ({
  userName,
  isBindPhoneFlow,
  isPhoneNumber,
  isGlobal,
  flow,
  countryKey,
  dataValidateOTP,
  dataSendOTP,
  dataConfirmOTP,
  onResendOTP,
  onChangeOTP,
  isRestoreAccountFlow,
  isUpdatePasswordFlow,
  isUpdatePhoneNumberFlow,
  handleChangeTypeReceiveOTP,
  typeReceiveOTP,
  onBack,
  profile,
  canBack,
  inputType
}: any) => {
  const refCountdown = useRef<any>(null);
  const [canResendOTP, setCanResendOTP] = useState(false);
  const [timeCountdown, setTimeCountdown] = useState<any>(null);
  const [errorForm, setErrorForm] = useState<any>('');
  const [isDelayWaitingText, setDelayWaitingText] = useState(false);
  const [otpFirstType, setOtpFirstType] = useState<any>('');
  const [resetOTPInput, setResetOTPInput] = useState(false);
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const { emailVerified, phoneVerified } = profile || {};

  const [typeNotifyReceived, setTypeNotifyReceived] = useState<any>('');

  const channel = isPhoneNumber ? 'mobile' : 'email';
  const trackingFlowName = isBindPhoneFlow
    ? TYPE_AUTH.BIND_PHONE_OTP
    : isUpdatePhoneNumberFlow
    ? TYPE_AUTH.LINK_PHONE_NUMBER_OTP
    : isUpdatePasswordFlow
    ? TYPE_AUTH.UPDATE_PASSWORD_OTP
    : isRestoreAccountFlow
    ? TYPE_AUTH.RESTORE_ACCOUNT_OTP
    : flow;

  const method =
    typeNotifyReceived ||
    ((isPhoneNumber && isEmpty(profile) && !isEmpty(countryKey)) ||
    (isPhoneNumber && userName === profile?.mobile)
      ? 'sms'
      : 'mail');

  useEffect(() => {
    if (dataSendOTP?.data?.result?.notificationType?.length > 0) {
      setTypeNotifyReceived(dataSendOTP?.data?.result?.notificationType);
    }
  }, [dataSendOTP]);

  useEffect(() => {
    trackingAuth({
      event: EVENT_NAME.SEND_OTP_SMS,
      typeTrigger: channel,
      isUseFlowAuthenProperty: true,
      method,
      flowName: trackingFlowName
    });
  }, [channel, trackingFlowName, method]);

  const titleModal = useMemo(() => {
    if (isUpdatePhoneNumberFlow) return TEXT.AUTH_UPDATE_PHONE_NUMBER;
    if (isRestoreAccountFlow) return TEXT.RESTORE_ACCOUNT;
    if (isUpdatePasswordFlow) return TEXT.UPDATE_PASSWORD;
    if (flow === TYPE_AUTH.REGISTER || isBindPhoneFlow) return TEXT.REGISTER_ACCOUNT;
    return TEXT.FORGOT_PASSWORD;
  }, [isRestoreAccountFlow, isUpdatePhoneNumberFlow, flow, isUpdatePasswordFlow, isBindPhoneFlow]);

  const handleChangeOTP = useCallback(
    (e: any) => {
      setErrorForm('');
      onChangeOTP?.(e.value);
      setDelayWaitingText(Boolean(e.value.length));
    },
    [onChangeOTP]
  );

  const handleClick = useCallback(
    (type: any) => {
      if (canResendOTP && !(dataSendOTP?.data?.code === HTTP_CODE.OTP_LIMITED)) {
        const method =
          (isPhoneNumber && dataSendOTP?.data?.result?.notificationType) ||
          (isPhoneNumber && !isEmpty(countryKey) ? 'sms' : 'mail') ||
          '';
        setErrorForm('');
        setDelayWaitingText(false);
        setResetOTPInput(true);
        trackingAuth({
          event: EVENT_NAME.RESEND_OTP_SELECTED,
          typeTrigger: channel,
          isUseFlowAuthenProperty: true,
          method,
          flowName: trackingFlowName
        });
        onResendOTP?.(type);
      }
    },
    [canResendOTP, dataSendOTP, onResendOTP, isPhoneNumber, trackingFlowName, channel, countryKey]
  );

  const handleResendOtpOption = useCallback(
    (type: any) => {
      if (typeof handleChangeTypeReceiveOTP === 'function') {
        if (otpFirstType.length === 0) {
          setOtpFirstType(typeNotifyReceived);
        }
        handleClick(type);
      }
    },
    [typeNotifyReceived, handleClick, otpFirstType, handleChangeTypeReceiveOTP]
  );

  const isLimitOTP = useMemo(
    () =>
      !isEmpty(dataSendOTP) &&
      dataSendOTP.success &&
      dataSendOTP.data?.code === HTTP_CODE.OTP_LIMITED,
    [dataSendOTP]
  );

  const handleBack = useCallback(() => {
    onBack?.();
  }, [onBack]);

  const renderButtonGroupResendOtp = useCallback(() => {
    const options = [];

    const isProfileEmpty = isEmpty(profile);
    const isPhoneOTP = typeReceiveOTP === TYPE_RECEIVE_OTP.PHONE;
    const isEmailOTP = typeReceiveOTP === TYPE_RECEIVE_OTP.EMAIL;
    const isZnsAllowed =
      flow === TYPE_AUTH.FORGOT_PASSWORD &&
      ((isProfileEmpty && typeNotifyReceived === OTP_TYPE.SMS && otpFirstType === OTP_TYPE.ZNS) ||
        (phoneVerified && typeNotifyReceived === OTP_TYPE.SMS && otpFirstType === OTP_TYPE.ZNS));

    if (
      (emailVerified && isPhoneOTP && typeNotifyReceived === OTP_TYPE.SMS) ||
      (emailVerified && typeNotifyReceived === OTP_TYPE.SMS)
    ) {
      options.push(OTP_TYPE.MAIL);
    }

    if (
      (isProfileEmpty && typeNotifyReceived === OTP_TYPE.ZNS) ||
      (isProfileEmpty && isPhoneOTP && typeNotifyReceived === OTP_TYPE.ZNS) ||
      (phoneVerified && isPhoneOTP && [OTP_TYPE.ZNS, OTP_TYPE.MAIL].includes(typeNotifyReceived)) ||
      (isEmailOTP && phoneVerified && typeNotifyReceived === OTP_TYPE.MAIL)
    ) {
      options.push(OTP_TYPE.SMS);
    }
    if (isZnsAllowed) {
      options.push(OTP_TYPE.ZNS);
    }

    if (options.length === 0) return null;

    return (
      <>
        <p className="text-[#CCCCCC] text-[14px] leading-[19.6px] font-[400] w-full text-center">
          Hoặc
        </p>
        <div className="flex space-x-2">
          {options.map((type) => {
            const imgName = type === OTP_TYPE.ZNS ? 'zalo' : type;
            return (
              <ButtonBasic
                key={type}
                title={TEXT.RESEND_OTP_VIA}
                theme={EL_THEME_CLASS.PRIMARY_OUT_LINE_GLASS}
                size={isMobile ? EL_SIZE_CLASS.MEDIUM : EL_SIZE_CLASS.LARGE}
                disabled={isLimitOTP || !canResendOTP}
                onClick={() => handleResendOtpOption(type)}
                imgSrcRight={`${ConfigImage.imgSprites}/${imgName}.svg`}
                iCustomizeClassSlotRight="w-5 h-5 md:w-6 md:h-6"
                customizeClass="grow space-x-3 md:space-x-2"
                textClass={isLimitOTP || !canResendOTP ? 'text-[#6f6f6f]' : ''}
              />
            );
          })}
        </div>
      </>
    );
  }, [
    typeReceiveOTP,
    flow,
    typeNotifyReceived,
    canResendOTP,
    handleResendOtpOption,
    isMobile,
    profile,
    isLimitOTP,
    emailVerified,
    otpFirstType,
    phoneVerified
  ]);
  const displayTextWithNotifyType = (type: any) => {
    switch (type) {
      case OTP_TYPE.ZNS:
        return 'Zalo';
      default:
        return OTP_TYPE.SMS.toUpperCase();
    }
  };

  const sendTypeToPhone = useMemo(() => {
    if (!isGlobal && !isEmpty(profile) && countryKey) {
      if (isEmpty(profile?.mobile) && !isEmpty(userName)) {
        return `${countryKey} ${formatPhoneNumberVN(userName)}`;
      }
      return `${countryKey} ${formatPhoneNumberVN(profile?.mobile)}`;
    }

    if (
      (!isGlobal && flow === TYPE_AUTH.FORGOT_PASSWORD && countryKey) ||
      inputType === LOGIN_INPUT_TYPE.PHONE
    ) {
      return `${countryKey} ${formatPhoneNumberVN(userName)}`;
    }

    return `${userName}`;
  }, [flow, userName, profile, countryKey, isGlobal, inputType]);
  useEffect(() => {
    if (resetOTPInput) {
      const timer = setTimeout(() => setResetOTPInput(false), 100);
      return () => clearTimeout(timer);
    }
    return;
  }, [resetOTPInput]);

  useEffect(() => {
    if (
      !isEmpty(dataSendOTP) &&
      dataSendOTP.success &&
      dataSendOTP.data?.code === HTTP_CODE.ACCOUNT_NOT_LINKED &&
      dataSendOTP.data?.result?.expiresIn > 0
    ) {
      setTimeCountdown(dataSendOTP.data?.result?.expiresIn);
      refCountdown.current = setInterval(() => {
        setTimeCountdown((state: any) => state - 1);
      }, 1000);
    }
    return () => {
      if (refCountdown.current) clearInterval(refCountdown.current);
    };
  }, [dataSendOTP]);

  useEffect(() => {
    if (timeCountdown === 0) {
      setCanResendOTP(true);
      if (refCountdown.current) {
        clearInterval(refCountdown.current);
      }
    } else {
      setCanResendOTP(false);
    }
  }, [timeCountdown]);

  useEffect(() => {
    if (!isEmpty(dataValidateOTP) && !dataValidateOTP.success && dataValidateOTP.message) {
      setErrorForm(dataValidateOTP.message);
    }
  }, [dataValidateOTP]);
  useEffect(() => {
    if (!isEmpty(dataValidateOTP) || !isEmpty(dataConfirmOTP)) {
      if (trackingFlowName === TYPE_AUTH.FORGOT_PASSWORD) {
        trackingAuth({
          event: EVENT_NAME.OTP_INPUTTED,
          typeTrigger: channel,
          isUseFlowAuthenProperty: true,
          status: dataConfirmOTP?.success || dataValidateOTP?.success,
          message: dataValidateOTP?.message,
          flowName: trackingFlowName
        });
      }
    }
  }, [dataValidateOTP, dataConfirmOTP, channel, trackingFlowName]);

  useEffect(() => {
    if (isRestoreAccountFlow && !isEmpty(dataConfirmOTP)) {
      const message =
        dataConfirmOTP?.data?.message ||
        dataConfirmOTP?.message ||
        TEXT.ERROR_RETRY_AFTER_SOME_MINUTES_PLEASE;
      if (!dataConfirmOTP.success || get(dataConfirmOTP, 'data.code', '') === HTTP_CODE.FAIL) {
        setErrorForm(message);
      }
    }
  }, [isRestoreAccountFlow, dataConfirmOTP]);
  const config = {
    title: titleModal,
    description:
      (isPhoneNumber && userName !== profile?.email) ||
      typeNotifyReceived === OTP_TYPE.SMS ||
      typeNotifyReceived === OTP_TYPE.ZNS ? (
        <>
          {TEXT.INPUT_OTP_CODE_SENT_TO}{' '}
          {countryKey === '+84' ? (
            <>
              {displayTextWithNotifyType(typeNotifyReceived)}
              <br />
              {'qua '}
            </>
          ) : (
            ''
          )}
          {TEXT.USER_LABEL.PHONE_NUMBER.toLowerCase()}{' '}
          <b className="text-white">{sendTypeToPhone}</b>
        </>
      ) : (
        <>
          {`${TEXT.INPUT_OTP_CODE_SENT_TO} địa chỉ email`} <br />
          <b className="text-white">{userName}</b>
        </>
      ),
    alignType: 'center',
    buttonBack: canBack ? <ButtonBack onClick={handleBack} /> : null,
    inputs: (
      <InputCode
        isNumberInput
        length={4}
        onChange={handleChangeOTP}
        resetValue={errorForm || resetOTPInput}
        inputError={errorForm}
      />
    ),
    messageCasesInput: MessageCasesInput({
      limitOTP: isLimitOTP && dataSendOTP.data?.result?.retryAfterTime,
      error: errorForm,
      waitingText: !canResendOTP && !isLimitOTP && !errorForm && !isDelayWaitingText,
      countdown: timeCountdown,
      systemMessages: dataSendOTP.data?.message
    }),
    buttons: (
      <div className="flex flex-col space-y-[8px] w-full">
        <ButtonBasic
          title={TEXT.RE_SEND_OTP}
          theme={EL_THEME_CLASS.PRIMARY_SUBTLE}
          size={isMobile ? EL_SIZE_CLASS.MEDIUM : EL_SIZE_CLASS.LARGE}
          disabled={isLimitOTP || !canResendOTP}
          onClick={() => handleClick(typeNotifyReceived)}
        />
        {renderButtonGroupResendOtp()}
      </div>
    ),
    textSupport: <TextSupport />
  };

  return <Modal {...config} />;
};

export default OTPVerify;

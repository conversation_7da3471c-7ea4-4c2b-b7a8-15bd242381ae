import React, { useMemo } from 'react';
import { TEXT } from '@constants/text';
import { parseTimeSecondToMinutes } from '@helpers/common';

const MessageCasesInput = ({ limitOTP, error, waitingText, countdown, systemMessages }: any) => {
  const countdownText = useMemo(() => {
    if (typeof countdown === 'number' && countdown > 0) {
      return `${TEXT.IF_YOU_DO_NOT_RECEIVE_OTP_SELECT_RESEND_LATER} (${parseTimeSecondToMinutes(
        countdown
      )})`;
    }
    return '';
  }, [countdown]);

  return (
    <div className="relative min-h-6 text-center w-full flex-col flex justify-end !text-[#9b9b9b]">
      {!limitOTP ? (
        <>
          {error || systemMessages ? (
            <div className="!text-[#E74C3C] !text-[.875rem] lg:text-[1rem]">
              {error || systemMessages}
            </div>
          ) : waitingText ? (
            <div className="!text-[.875rem] lg:text-[1rem]">
              {TEXT.CONFIRM_CODE_MAY_BE_DELAY_WAIT_A_MOMENT}
            </div>
          ) : (
            ''
          )}
          {countdown ? <div className="!text-[.875rem] lg:text-[1rem]">{countdownText}</div> : ''}
        </>
      ) : (
        <div className="text-[.875rem] lg:text-[1rem]">
          Bạn đã hết lượt gửi mã OTP hôm nay, vui lòng thử lại vào ngày mai
        </div>
      )}
    </div>
  );
};

export default MessageCasesInput;

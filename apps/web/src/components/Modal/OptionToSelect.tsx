/* eslint-disable react/react-in-jsx-scope */
import { useMemo } from 'react';
import Button from '@components/Button';
import IconPhone from '@components/Icons/IconPhone';
import IconEmail from '@components/Icons/IconEmail';
import { FLOW_GLOBAL_AUTH, PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';
import { EVENT_NAME, trackingAuth } from '@tracking/functions/TrackingAuthentication';
import { get } from 'lodash';
import Modal from './index';
import SocialPlugin from '../SocialPlugin';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { useVieRouter } from '@customHook';
import TrackingPayment from '@tracking/functions/payment';

const OptionToSelect = ({ trigger, onSelect, notSupportEmail }: any) => {
  const router = useVieRouter();
  const destination = useMemo(() => get(router, 'query.destination', '/'), [router?.query]);

  const title = useMemo(() => (trigger ? TEXT[`AUTH_${trigger}`] : TEXT.AUTH_CONTENT), [trigger]);
  const isRentedContent = useMemo(() => {
    if (destination) {
      return (
        decodeURIComponent(destination).includes(PAGE.RENTAL_CONTENT) ||
        decodeURIComponent(destination).includes(PAGE.PVOD_CONTENT)
      );
    }
    return;
  }, [destination]);

  const isTriggerAuth = useMemo(
    () =>
      trigger === TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE ||
      trigger === TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE_PRE_ORDER ||
      trigger === TYPE_TRIGGER_AUTH.SETTING,
    [trigger]
  );

  const onTrackingAuthButtonSelected = (type: any) => {
    if (type === FLOW_GLOBAL_AUTH.PHONE) {
      trackingAuth({ event: EVENT_NAME.MOBILE_AUTHEN_BUTTON_SELECTED, typeTrigger: trigger });
    } else {
      trackingAuth({ event: EVENT_NAME.EMAIL_AUTHEN_BUTTON_SELECTED, typeTrigger: trigger });
    }
  };

  const handleClickButton = (data: any) => {
    onTrackingAuthButtonSelected(data);
    if (typeof onSelect === 'function') onSelect(data);
  };

  const handleGotoStep2 = () => {
    const path = isRentedContent ? decodeURIComponent(destination) : destination;
    const trackingPayment = new TrackingPayment();

    trackingPayment.laterButtonSelect();
    router.push(path);
  };

  const config = {
    title,
    description:
      isRentedContent || isTriggerAuth ? TEXT.AUTH_RENTED_CONTENT_HINT : TEXT.AUTH_CHOOSE_PLEASE,
    alignType: 'left',
    hasDivider: true,
    textDivider: TEXT.TEXT_DIVIDER,
    buttons: (
      <>
        <Button
          title={TEXT.USER_LABEL.PHONE_NUMBER}
          isPrimary
          icon={<IconPhone />}
          onClick={() => handleClickButton(FLOW_GLOBAL_AUTH.PHONE)}
        />
        {!notSupportEmail && (
          <Button
            title="Email"
            icon={<IconEmail />}
            onClick={() => handleClickButton(FLOW_GLOBAL_AUTH.EMAIL)}
          />
        )}
        {isTriggerAuth && notSupportEmail && <Button title="Để sau" onClick={handleGotoStep2} />}
      </>
    ),
    footer: <SocialPlugin />
  };

  return <Modal {...config} />;
};

export default OptionToSelect;

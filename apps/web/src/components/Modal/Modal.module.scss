.container {
  @apply rounded-lg;
  @apply grid md:flex md:flex-col;
  @apply 3xl:space-y-8 space-y-6 md:space-y-5;
  @apply mt-6 md:mt-0 mb-4;
  @apply px-8 py-4 md:py-6 3xl:pt-8 3xl:pb-10;
  @apply bg-transparent md:bg-[#222];
  @apply shadow-none md:shadow-modalContainer;
  @apply w-full md:w-[594px];

  &.alignLeft {
    @apply text-start;
  }
  &.alignCenter {
    @apply text-center;
  }
  &.hasBackButton {
    @apply pt-0 md:pt-8;
  }
}

// displayPosition
.top {
  &Right {
    @apply md:absolute md:top-[3.02083vw] md:right-[10.52vw];
  }

  &Center {
    @apply md:absolute md:top-[3.02083vw] md:right-1/2 md:translate-y-1/2;
  }

  &Left {
    @apply md:absolute md:top-[3.02083vw] md:left-[10.52vw];
  }
}
.bottom {
  &Right {
    @apply md:absolute md:bottom-[3.02083vw] md:right-[10.52vw];
  }

  &Center {
    @apply md:absolute md:bottom-[3.02083vw] md:right-1/2 md:translate-y-1/2;
  }

  &Left {
    @apply md:absolute md:bottom-[3.02083vw] md:left-[10.52vw];
  }
}

.middle {
  &Right {
    @apply md:absolute md:top-1/2 md:right-[10.52vw] md:-translate-y-1/2;
  }
  &Left {
    @apply md:absolute md:top-1/2 md:left-[10.52vw] md:-translate-y-1/2;
  }
}
.center {
  @apply md:absolute md:top-1/2 md:left-1/2 md:-translate-y-1/2 md:-translate-x-1/2;
}

.buttonBack {
  @apply flex items-center;
  @apply w-[70px] md:w-24 gap-1 md:gap-2;
  @apply text-[#CCCCCC] font-medium text-sm md:text-base leading-5 md:leading-[140%];
  @apply pb-0 md:pb-3;
  & > svg {
    @apply w-4 md:w-auto h-4 md:h-auto;
  }
}

.infoForm {
  @apply flex flex-col gap-2;
}

.title {
  @apply text-white text-[22px] 3xl:text-[28px] font-bold md:font-medium leading-8 md:leading-[140%];
}

.description {
  @apply text-[#ccc] text-sm md:text-base leading-5 md:leading-[140%];
}

.inputsGroup {
  @apply grid md:flex md:flex-col;
  @apply relative gap-4;

  &InputMessage {
    @apply after:content-[attr(data-wait-message)] after:absolute after:w-full after:top-full after:text-[#9B9B9B] after:!text-[1rem] after:text-center after:font-normal after:pt-[6px];
  }
}

.extraInput {
  @apply text-left;
}

.buttonsGroup {
  @apply grid md:flex md:flex-col w-full md:w-[530px] items-start space-y-6 lg:space-y-8;
  &.hasLongError {
    @apply mt-7 #{!important};
  }
}

.infoSupport {
  @apply flex flex-col w-full text-sm text-[#ccc] leading-[140%] font-normal text-center gap-2;
  & > span > strong,
  & > span > a {
    @apply text-white cursor-pointer;
  }
}

.divider {
  @apply flex relative w-full md:w-[530px] items-center gap-3 h-[17px];
  &::before {
    @apply w-full h-[1px] content-[''] block absolute top-1/2 bg-[#454545];
  }
  > span {
    @apply absolute text-[#9b9b9b] text-center py-0 px-3 text-xs font-medium leading-[140%] z-[2];
    @apply left-1/2 md:left-52;
    @apply bg-[#111] md:bg-[#222];
    @apply translate-x-[-50%] md:translate-x-0;
  }
}

.hasQRForm {
  &.divider {
    & + div {
      @apply order-1;
    }
    & + div + div {
      @apply order-3;
    }
    &.order {
      @apply order-2;
    }
  }
}

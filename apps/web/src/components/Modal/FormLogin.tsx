import React from 'react';
import Input from '@components/Input';
import Button from '@components/Button';
import IconPhone from '@components/Icons/IconPhone';
import IconEmail from '@components/Icons/IconEmail';
import IconKey from '@components/Icons/IconKey';
import Checkbox from '@components/Checkbox';
import InputCode from '@components/InputCode';
import { TYPE_INPUT } from '@constants/types';
import { TEXT } from '@constants/text';
import TextSupport from './TextSupport';
import SocialPlugin from '@components/SocialPlugin';
import Modal from './index';

const DATA = [
  {
    country: 'Việt Nam',
    code: '+84',
    isDefault: true
  },
  {
    country: 'Mỹ',
    code: '+1'
  },
  {
    country: 'Hàn Quốc',
    code: '+886'
  }
];

const FormLogin = () => {
  const config = {
    title: 'Đăng ký tài khoản hoặc đăng nhập để tiếp tục thanh toán',
    description: '<PERSON><PERSON> lòng chọn phương thức để bắt đầu',
    alignType: 'left',
    hasDivider: true,
    textDivider: TEXT.TEXT_DIVIDER,
    extraInput: (
      <Checkbox
        label={
          <p>
            Tôi đồng ý với{' '}
            <a
              href="https://vieon.vn"
              rel="noreferrer"
              target="_blank"
              className="text-white font-bold"
            >
              Hợp đồng điện tử
            </a>{' '}
            của VieON và xác nhận rằng tôi trên 18 tuổi
          </p>
        }
      />
    ),
    inputs: (
      <>
        <Input
          id="phoneNumber"
          placeholder="Ví dụ: 555 6667777"
          type={TYPE_INPUT.TEL}
          label="Số điện thoại"
          dropDownData={DATA}
          iconPrefix={<IconPhone size="small" />}
          error=""
        />
        <Input
          id="phoneNumber1"
          placeholder="Ví dụ: 555 6667777"
          type={TYPE_INPUT.PASSWORD}
          label="Mật khẩu"
          iconPrefix={<IconKey />}
          error=""
        />
        <InputCode length={4} />
      </>
    ),
    buttons: (
      <>
        <Button title="Số điện thoại" isHighlight icon={<IconPhone />} />
        <Button title="Email" icon={<IconEmail />} />
      </>
    ),
    buttonSupport: (
      <span className="text-white font-bold cursor-pointer">Gửi về số điện thoại</span>
    ),
    textSupport: <TextSupport />,
    footer: <SocialPlugin />
  };

  return <Modal {...config} />;
};

export default FormLogin;

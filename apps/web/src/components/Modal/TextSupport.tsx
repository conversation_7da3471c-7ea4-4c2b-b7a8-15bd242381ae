import React from 'react';
import { VieON } from '@constants/constants';
import MideskChat from '@script/MideskChat';
import { ENABLE_SDK_MIDESK_CHAT } from '@config/ConfigEnv';

const TextSupport = () => {
  const onClickChat = () => {
    if (!ENABLE_SDK_MIDESK_CHAT || ENABLE_SDK_MIDESK_CHAT === 'false') {
      return;
    }

    MideskChat.init();
    if (MideskChat.isInitialized) {
      MideskChat.show();
    }
  };

  return (
    <span>
      Để được hỗ trợ thêm vui lòng{' '}
      <a onClick={onClickChat}>
        <strong>chat</strong>
      </a>{' '}
      hoặc{' '}
      <a href={`mailto:${VieON.SUPPORT_EMAIL}`}>
        <strong>gửi email</strong>
      </a>{' '}
      cho chúng tôi
    </span>
  );
};

export default TextSupport;

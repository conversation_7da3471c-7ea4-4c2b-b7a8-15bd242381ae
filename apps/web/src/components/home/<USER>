import React, { useEffect, useMemo } from 'react';
import { onOpenPayment } from '@helpers/common';
import { useVieRouter } from '@customHook';
import {
  svodTrialSubscribePackageButtonSelected,
  exclusiveHotContentSubscribePackageButtonSelected,
  exclusiveHotContentSubscribePackageLoaded,
  svodTrialSubscribePackageButtonLoaded
} from '@tracking/functions/TrackingPaymentConversion';
import { VALUE } from '@config/ConfigSegment';
import { useSelector } from 'react-redux';
import BannerTriggerConversion from './BannerTriggerConversion';

const BannerPaymentConversion = ({
  newSubstr,
  regexp,
  isTrialDuration,
  data,
  packageId,
  style,
  contentData
}: any) => {
  const router = useVieRouter();
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const { message, image, imageMobile, button } = data;
  const parseMessage = useMemo(() => message?.replace(regexp, newSubstr), [data, newSubstr]);

  const onClickOpenPayment = () => {
    if (isTrialDuration) {
      svodTrialSubscribePackageButtonSelected({
        triggerFrom: VALUE.SVOD_TRIAL_BANNER,
        userType: userType?.userType,
        contentId: contentData?.id,
        contentName: contentData?.title
      });
    } else {
      exclusiveHotContentSubscribePackageButtonSelected({
        triggerFrom: VALUE.EXCLUSIVE_HOT_CONTENT_BANNER,
        userType: userType?.userType,
        contentId: contentData?.id,
        contentName: contentData?.title
      });
    }
    onOpenPayment(router, {
      returnUrl: window?.location?.href,
      pkg: packageId,
      referralCode: isTrialDuration ? 1 : 4
    });
  };

  useEffect(() => {
    if (!isTrialDuration) {
      exclusiveHotContentSubscribePackageLoaded({
        triggerFrom: VALUE.EXCLUSIVE_HOT_CONTENT_BANNER,
        userType: userType?.userType,
        contentId: contentData?.id,
        contentName: contentData?.title
      });
    } else {
      svodTrialSubscribePackageButtonLoaded({
        triggerFrom: VALUE.SVOD_TRIAL_BANNER,
        userType: userType?.userType,
        contentId: contentData?.id,
        contentName: contentData?.title
      });
    }
  }, [isTrialDuration, contentData]);

  return (
    <BannerTriggerConversion
      message={parseMessage}
      isTrialDuration={isTrialDuration}
      image={image}
      imageMobile={imageMobile}
      onClick={onClickOpenPayment}
      title={button}
      style={style}
    />
  );
};
export default React.memo(BannerPaymentConversion);

import React, { useEffect, useRef, useState } from 'react';
import { useVieRouter } from '@customHook';
import { createTimeout, encodeParamDestination, getAttrLink, onOpenPayment } from '@helpers/common';
import ConfigImage from '@config/ConfigImage';
import { PAGE, PROMOTION_BANNER_FUNC_TYPE, RIBBON_TYPE } from '@constants/constants';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { Swiper, SwiperSlide } from 'swiper/react';
import { isMobile } from 'react-device-detect';
import SwiperCore, { Autoplay, Lazy, Pagination } from 'swiper';
import TrackingApp from '@tracking/functions/TrackingApp';
import { impressionBanner } from '@tracking/functions/TrackingOEM';
import isEmpty from 'lodash/isEmpty';
import { useSelector } from 'react-redux';
import { NextArrow, PrevArrow } from '../basic/Buttons/ButtonArrow';
import Image from '../basic/Image/Image';

SwiperCore.use([Lazy, Pagination, Autoplay]);

const PromotionBanner = ({ data, isRankingBoard }: any) => {
  const ribItemsLength = data?.items?.length;
  const timerMouseLeave = useRef<any>(null);
  const timerMouseEnter = useRef<any>(null);
  const timeClickPrev = useRef<any>(null);
  const timeClickNext = useRef<any>(null);
  const refEl = useRef<any>(null);
  const [state, setState] = useState<any>({
    swiper: null,
    next: false,
    prev: false,
    activeIndex: 0
  });
  const router = useVieRouter();
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { profile } = useSelector((state: any) => state?.Profile || {});

  useEffect(
    () => () => {
      clearTimeout(timeClickPrev.current);
      clearTimeout(timeClickNext.current);
    },
    []
  );

  useEffect(() => {
    const elementRect = refEl.current ? refEl.current.getBoundingClientRect() : {};
    const heightScreen = window.innerHeight;
    if (
      !isEmpty(data) &&
      ((elementRect.top < 0 && elementRect.bottom > 0 && elementRect.bottom <= heightScreen) ||
        (elementRect.top > 0 && elementRect.top <= heightScreen))
    ) {
      impressionBanner({
        bannerName: data?.items?.[state.activeIndex]?.title,
        bannerId: data?.items?.[state.activeIndex]?.id,
        bannerOrder: state.activeIndex
      });
    }
  }, [state.activeIndex]);

  useEffect(() => {
    const isBeginning = !state.activeIndex || state?.swiper?.isBeginning;
    const isEnd = state?.swiper?.isEnd;
    setState((prevState: any) => ({
      ...prevState,
      next: !isEnd,
      prev: !isBeginning
    }));
    return () => {
      clearTimeout(timerMouseEnter.current);
      clearTimeout(timerMouseLeave.current);
    };
  }, [state.activeIndex]);

  const initSwiper = ({ swiper }: any) => {
    setState((prevState: any) => ({
      ...prevState,
      next: ribItemsLength > 1,
      swiper
    }));
  };
  const onClickRibPromotion = (item: any, index: any) => {
    TrackingApp.bannerSelected({ data: item, index });
    const allowClick = item?.allowClick;
    let externalUrl = item?.externalUrl;
    const type = item?.type;
    const seo = item?.seo;
    if (data?.type === RIBBON_TYPE.PROMOTION_BANNER_FUNC) {
      const optionDirect = item?.optionDirect;
      switch (optionDirect) {
        case PROMOTION_BANNER_FUNC_TYPE.buyPackage:
        case PROMOTION_BANNER_FUNC_TYPE.buyVip:
        case PROMOTION_BANNER_FUNC_TYPE.buyVipKPLUS:
        case PROMOTION_BANNER_FUNC_TYPE.buyVipHBO:
        case PROMOTION_BANNER_FUNC_TYPE.buyVipSPORT:
        case PROMOTION_BANNER_FUNC_TYPE.buyVipFAMILY:
        case PROMOTION_BANNER_FUNC_TYPE.buyAllAccess: {
          const optionPackageId = item?.optionDirectPackageId || 0;
          onOpenPayment(router, {
            returnUrl: window?.location?.href,
            pkg: optionPackageId,
            newTriggerPaymentBuyPackage: {
              isGlobal,
              profileId: profile?.id
            }
          });
          break;
        }
        case PROMOTION_BANNER_FUNC_TYPE.Login:
        case PROMOTION_BANNER_FUNC_TYPE.Register: {
          const remakeDestination = encodeParamDestination(router?.asPath);
          router.push(
            `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.PROMOTION}`
          );
          break;
        }
        default:
          break;
      }
    } else if (allowClick === 1) {
      if (externalUrl) {
        externalUrl = externalUrl.match(/^https?:/) ? externalUrl : `//${externalUrl}`;
        // interceptor click to voting
        if (externalUrl.includes('{accesstoken}') && profile?.token) {
          const newLink = externalUrl.replace('{accesstoken}', profile?.token);
          window.open(newLink, '_blank');
        } else {
          window.open(externalUrl, '_blank');
        }
      } else {
        const attrLink = getAttrLink({ as: '', href: '', type });
        router.push(attrLink.href, seo?.url);
      }
    } else if (allowClick === 0) return null;
    return null;
  };
  const onPrev = () => {
    if (!state?.swiper) return;
    const prevIndex = state?.swiper?.activeIndex - 1;
    if (!isEmpty(data)) {
      if (timeClickPrev.current) clearTimeout(timeClickPrev.current);
    }
    const isBeginning = prevIndex <= 0 || state.swiper?.isBeginning;
    state.swiper.slidePrev();
    setState((prevState: any) => ({
      ...prevState,
      next: true,
      prev: !isBeginning
    }));
  };
  const onNext = () => {
    if (!state?.swiper || !ribItemsLength) return;
    if (!isEmpty(data)) {
      if (timeClickNext.current) clearTimeout(timeClickNext.current);
    }
    state.swiper.slideNext();
    setState((prevState: any) => ({
      ...prevState,
      prev: true
    }));
  };
  const onMouseEnter = () => {
    clearTimeout(timerMouseEnter.current);
    timerMouseEnter.current = createTimeout(() => {
      if (state?.swiper) state?.swiper?.autoplay?.stop();
    }, 1000);
  };
  const onMouseLeave = () => {
    clearTimeout(timerMouseLeave.current);
    timerMouseLeave.current = createTimeout(() => {
      if (state?.swiper && state.activeIndex !== ribItemsLength - 1) {
        state?.swiper?.autoplay?.start();
      }
    }, 1000);
  };
  const handleSlideChange = (swiper: any) => {
    if (swiper.realIndex === ribItemsLength - 1) {
      setState((prevState: any) => ({
        ...prevState,
        activeIndex: swiper.realIndex,
        swiper,
        prev: true
      }));
      swiper.autoplay.stop();
    } else {
      setState((prevState: any) => ({
        ...prevState,
        activeIndex: swiper.realIndex,
        prev: true
      }));
    }
  };

  // let imageSrc = item?.images?.promotionBanner
  if (!data) return null;
  const { prev } = state || {};
  const durationTrans = data?.duration_trans;
  let nextClass = '';
  let prevClass = '';
  if (ribItemsLength === 1) nextClass = ' swiper-button-disabled';
  if (!prev) prevClass = ' swiper-button-disabled';
  if (prev && isRankingBoard) prevClass = ' inside';
  return (
    <div className="banner banner--market" ref={refEl}>
      <div className="banner__inner align-center">
        <div
          className="banner__body text-center"
          onMouseLeave={onMouseLeave}
          onMouseEnter={onMouseEnter}
        >
          <Swiper
            className="link slider slider-indicator"
            allowTouchMove={!!(isMobile && ribItemsLength > 1)}
            onSlideChange={(swiper) => handleSlideChange(swiper)}
            spaceBetween={8}
            onSwiper={(swiper) => initSwiper({ swiper })}
            id={data?.id}
            data-item-view={1}
            slidesPerView={1}
            loop={ribItemsLength > 1}
            lazy={{
              //  tell swiper to load images before they appear
              loadPrevNext: true,
              // amount of images to load
              loadPrevNextAmount: 2
            }}
            pagination={{
              modifierClass: `absolute layer-2 ${
                typeof window !== 'undefined' && ribItemsLength <= 1 ? 'hide ' : ''
              }`
            }}
            autoplay={{
              delay: durationTrans ? +durationTrans * 1000 : 5000
            }}
          >
            {(data?.items || []).map((item: any, i: any) => (
              <SwiperSlide
                className="w-full"
                key={item?.id + i}
                onClick={() => onClickRibPromotion(item, i)}
                style={{ cursor: item?.allowClick ? 'pointer' : 'initial' }}
              >
                <Image
                  className="w-full"
                  src={item?.images?.promotionBanner}
                  defaultSrc={ConfigImage.defaultBanner16x9}
                  alt={item?.title || 'Promotion Banner'}
                  title={item?.title || 'Promotion Banner'}
                  isDefault
                />
                <div className="swiper-lazy-preloader" />
              </SwiperSlide>
            ))}
            {!isMobile && ribItemsLength > 1 && (
              <NextArrow onClickNext={onNext} nextClass={nextClass} />
            )}
            {!isMobile && ribItemsLength > 1 && (
              <PrevArrow onClickPrev={onPrev} prevClass={prevClass} />
            )}
          </Swiper>
        </div>
      </div>
    </div>
  );
};

export default PromotionBanner;

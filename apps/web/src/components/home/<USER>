import React, { useEffect, useState } from 'react';
import Button from '@components/basic/Buttons/Button';
import { useVieRouter } from '@customHook';
import classNames from 'classnames';
import { useSelector } from 'react-redux';
import { POSITION_TRIGGER } from '@constants/constants';
import {
  alwaysOnTriggerSelected,
  alwaysOnTriggerClosed,
  alwaysOnTriggerLoaded
} from '@tracking/functions/TrackingTriggerPoint';
import isEmpty from 'lodash/isEmpty';
import { segmentEvent } from '@tracking/TrackingSegment';
import { NAME, PROPERTY } from '@config/ConfigSegment';
import style from './TriggerTouchPoint.module.scss';

const TriggerTouchPoint = ({
  image,
  imageMobile,
  url,
  className,
  positionTrigger,
  closeMenu,
  styleBanner,
  content,
  dataTrigger
}: any) => {
  const router = useVieRouter();
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const [showBanner, setShowBanner] = useState(true);

  const handleBtnClose = () => {
    alwaysOnTriggerClosed({ positionTrigger, content });
    setShowBanner(false);
  };
  const handleClick = () => {
    if (!url) return false;
    alwaysOnTriggerSelected({ positionTrigger, content });
    if (typeof closeMenu === 'function') {
      closeMenu();
    }
    segmentEvent(NAME.CHECK_OUT_STARTED, {
      [PROPERTY.REFERRAL]: window.location.href
    });
    router.push(url);
    return;
  };

  useEffect(() => {
    if (isEmpty(dataTrigger) && positionTrigger === POSITION_TRIGGER.LIVE_TV) setShowBanner(false);
  }, [dataTrigger, positionTrigger]);

  useEffect(() => {
    alwaysOnTriggerLoaded({ positionTrigger, content });
  }, []);

  if (!showBanner) return null;
  return (
    <div
      className={classNames(
        'mask',
        isMobile &&
          positionTrigger !== POSITION_TRIGGER.PROFILE &&
          positionTrigger !== POSITION_TRIGGER.LIVE_TV &&
          'canal-v p-t2',
        className,
        positionTrigger === POSITION_TRIGGER.PROFILE && 'card card--profile card--profile-info'
      )}
      style={styleBanner}
    >
      <div className="relative">
        <Button
          className={classNames(
            'button--darken-glass close button--circle-32 absolute',
            isMobile && `${style['icon-close']}`,
            !isMobile && 'top-2 right-2'
          )}
          iconClass="icon--tiny"
          iconName={`vie-times-medium ${style['image-button']}`}
          onClick={handleBtnClose}
        />
      </div>
      <picture className="on-click" onClick={handleClick}>
        <source media="(min-width: 1024px)" srcSet={image} />
        <source media="(min-width: 320px)" srcSet={imageMobile} />
        <img className="mask__img" srcSet={image} alt="Combo" />
      </picture>
    </div>
  );
};
export default TriggerTouchPoint;

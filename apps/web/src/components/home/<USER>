import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import { controlScroll, handleMasterPlayer, onOpenPayment } from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import PageApi from '@apis/cm/PageApi';
import {
  nearlyExpireFullscreenLoaded,
  nearlyExpireFullscreenButtonSelected,
  nearlyExpireFullscreenContentSelected,
  expiredFullscreenLoaded,
  expiredButtonSelected,
  expiredFullscreenContentSelected
} from '@tracking/functions/TrackingPaymentConversion';
import { handleOffMasterPlayerService } from '@services/handleOffMasterPlayerService';
import { setStatusPaymentConversion } from '@actions/app';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import Backdrops from '@components/basic/Backdrops/Backdrops';
import RibbonFullScreenRepayExpireNearly from './RibbonFullScreenRepayExpireNearly';
import { setStatusRepayConversion } from '@actions/user';

let mounted = true;
const FullScreenBannerUserPackageInfo = () => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { bindAccount, isMobile, offBindAccount, paymentConversion, geoCheck } = useSelector(
    (state: any) => state?.App || {}
  );
  const data = useSelector((state: any) => state?.User?.USER_PACKAGE_INFO);
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const subscriptions = data?.subscriptions;
  const { suggestRibbonId, status } = subscriptions || {};
  const { fullScreenExpired } = paymentConversion || {};
  const { frequencyDay, totalTime } = fullScreenExpired || {};
  const isNotLocal = useMemo(() => geoCheck?.geo_country === 'US', [geoCheck]);
  const [dataRibbon, setDataRibbon] = useState<any>('');
  const [enableBanner, setEnableBanner] = useState(false);

  useEffect(() => {
    if (subscriptions && enableBanner) {
      if (status === 1) {
        nearlyExpireFullscreenLoaded({ subscriptions });
      } else {
        expiredFullscreenLoaded({ subscriptions });
      }
      handleOffMasterPlayerService();
      controlScroll({ isScroll: false });
    } else {
      controlScroll({ isScroll: true });
    }
  }, [subscriptions, enableBanner]);

  useEffect(() => {
    if (suggestRibbonId) {
      PageApi.getDataRibbonsId({ id: suggestRibbonId, page: 0, limit: 18, isGlobal }).then(
        (resp) => {
          if (resp?.success) {
            setDataRibbon(resp?.data);
          }
        }
      );
    }
  }, [suggestRibbonId]);

  useEffect(() => {
    const currentTime = new Date().getTime();
    const offBanner = ConfigLocalStorage.get(LocalStorage.OFF_BANNER_FULL_SCREEN) || 0;
    let timeBannerFifteen: any = ConfigLocalStorage.get(LocalStorage.SKIP_BANNER_FIFTEEN_DAY) || 0;
    let timeBannerOne: any = ConfigLocalStorage.get(LocalStorage.SKIP_BANNER_ONE_DAY) || 0;
    timeBannerFifteen = parseInt(timeBannerFifteen);
    timeBannerOne = parseInt(timeBannerOne);
    if (
      data?.isSubscriptions &&
      offBanner < totalTime &&
      ((!timeBannerFifteen && !timeBannerOne) ||
        (timeBannerFifteen > 0 && currentTime > timeBannerFifteen) ||
        (currentTime > timeBannerOne && timeBannerOne > 0))
    ) {
      if (isMobile) {
        dispatch(setStatusPaymentConversion(true));
      }
      dispatch(setStatusRepayConversion(true));
      setEnableBanner(true);
    }
  }, [data?.isSubscriptions, totalTime]);

  useEffect(() => {
    mounted = true;
    return () => {
      mounted = false;
      controlScroll({ isScroll: true });
      onSkipBanner();
    };
  }, []);

  const onClickOpenPayment = () => {
    if (status === 1) {
      nearlyExpireFullscreenButtonSelected({
        subscriptions,
        buttonStatus: subscriptions?.full?.action || ''
      });
    } else {
      expiredButtonSelected({
        subscriptions,
        buttonStatus: subscriptions?.full?.action || ''
      });
    }

    onOpenPayment(router, {
      pkg: data?.subscriptions?.packageId || data?.subscriptions?.suggestPackageId,
      referralCode: status ? 8 : 9
    });
  };

  const onClickSkip = () => {
    if (status === 1) {
      nearlyExpireFullscreenButtonSelected({
        subscriptions,
        buttonStatus: subscriptions?.full?.dismiss || ''
      });
    } else {
      expiredButtonSelected({
        subscriptions,
        buttonStatus: subscriptions?.full?.dismiss || ''
      });
    }
    onSkipBanner();
  };

  const onSkipBanner = () => {
    const isStatus = status === 1;
    const currentTime = Math.floor(new Date().getTime());
    const timeOneDay = 24 * 3600 * 1000;
    const timeFifteenDay = (frequencyDay || 10) * 24 * 3600 * 1000;
    const offBanner: any = ConfigLocalStorage.get(LocalStorage.OFF_BANNER_FULL_SCREEN) || 0;
    if (isStatus) {
      ConfigLocalStorage.set(LocalStorage.SKIP_BANNER_ONE_DAY, currentTime + timeOneDay);
    } else {
      if (offBanner) {
        ConfigLocalStorage.set(LocalStorage.OFF_BANNER_FULL_SCREEN, parseInt(offBanner) + 1);
      } else {
        ConfigLocalStorage.set(LocalStorage.OFF_BANNER_FULL_SCREEN, 1);
      }
      ConfigLocalStorage.set(LocalStorage.SKIP_BANNER_FIFTEEN_DAY, currentTime + timeFifteenDay);
    }

    if (mounted) {
      handleMasterPlayer();
      setEnableBanner(false);
      dispatch(setStatusRepayConversion(false));
    }
    if (isMobile) {
      dispatch(setStatusPaymentConversion(false));
    }
  };

  const onContentSelected = ({ cardData }: any) => {
    if (status === 1) {
      nearlyExpireFullscreenContentSelected({
        subscriptions,
        cardData,
        profile
      });
    } else {
      expiredFullscreenContentSelected({
        subscriptions,
        cardData,
        profile
      });
    }
    onSkipBanner();
  };

  const { full } = data?.subscriptions || {};
  const { message, action, image_url, dismiss, kv_url } = full || {};

  const dataJson = ConfigLocalStorage.get(LocalStorage.BIND_ACCOUNT);

  if (!isNotLocal && bindAccount?.allowOpenPopup) {
    const userJustLogged = ConfigLocalStorage.get(LocalStorage.USER_JUST_LOGGED);
    if (userJustLogged) {
      if (offBindAccount) {
        if (!dataJson) {
          if (profile?.phoneRequired) return null;
        }
      } else if (profile?.phoneRequired) return null;
    }
  }
  if (!enableBanner) return null;

  return (
    <div className="modal-overlay z-[10000] fixed">
      <div
        className="modal full max-w-full overflow-hidden"
        data-animation-in="fade"
        data-animation-out="fade"
      >
        <Backdrops imgDesktop={image_url} />
        <div className="modal-wrapper max-w-full max-h-full overflow-hidden">
          <div className="modal-body max-w-full max-h-full h-full p-0 scroll-y-auto overflow-x-hidden">
            <RibbonFullScreenRepayExpireNearly
              action={action}
              dismiss={dismiss}
              onClickOpenPayment={onClickOpenPayment}
              onContentSelected={onContentSelected}
              onSkipBanner={onSkipBanner}
              onClickSkip={onClickSkip}
              dataRibbon={dataRibbon}
              message={message}
              isFullscreenPaymentTrigger
              kvUrl={kv_url}
              enableBanner={enableBanner}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
export default React.memo(FullScreenBannerUserPackageInfo);

import { isMobile } from 'react-device-detect';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import { onOpenPayment } from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import {
  nearlyExpireBannerLoaded,
  nearlyExpireBannerSelected,
  expiredBannerLoaded,
  expiredBannerSelected
} from '@tracking/functions/TrackingPaymentConversion';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import ImageMask from '../basic/Image/ImageMask';

const BannerUserPackageInfo = () => {
  const router = useVieRouter();
  const DataUserPackage = useSelector((state: any) => state?.User);
  const data = DataUserPackage?.USER_PACKAGE_INFO;
  const subscriptions = data?.subscriptions;

  const [isShowBanner, setBanner] = useState(true);
  const currentTime: any = new Date().getTime();
  const timeBannerOne: any = ConfigLocalStorage.get(LocalStorage.SKIP_BANNER_ONE) || 0;
  const offBanner: any = ConfigLocalStorage.get(LocalStorage.OFF_BANNER);
  const { banner, status } = data?.subscriptions || {};
  const { message, action, dismiss, image_url, imageUrlRight } = banner || {};
  const isStatus = status === 0;

  useEffect(() => {
    if (subscriptions) {
      if (status === 1) {
        nearlyExpireBannerLoaded({ subscriptions });
      } else {
        expiredBannerLoaded({ subscriptions });
      }
    }
  }, [subscriptions]);

  const onClickOpenPayment = () => {
    if (status === 1) {
      nearlyExpireBannerSelected({
        subscriptions,
        buttonStatus: subscriptions?.banner?.action
      });
    } else {
      expiredBannerSelected({
        subscriptions,
        buttonStatus: subscriptions?.banner?.action
      });
    }

    onOpenPayment(router, {
      pkg: data?.subscriptions?.packageId || data?.subscriptions?.suggestPackageId,
      referralCode: status ? 6 : 7
    });
  };
  const onSkipBanner = () => {
    const currentTime: any = Math.floor(new Date().getTime());
    const timeOneDay = 24 * 3600 * 1000;
    const offBanner: any = ConfigLocalStorage.get(LocalStorage.OFF_BANNER);
    if (offBanner) {
      ConfigLocalStorage.set(LocalStorage.OFF_BANNER, parseInt(offBanner) + 1);
    } else {
      ConfigLocalStorage.set(LocalStorage.OFF_BANNER, 1);
    }
    ConfigLocalStorage.set(LocalStorage.SKIP_BANNER_ONE, currentTime + timeOneDay);
    setBanner(false);

    if (status === 1) {
      nearlyExpireBannerSelected({
        subscriptions,
        buttonStatus: subscriptions?.banner?.dismiss
      });
    } else {
      expiredBannerSelected({
        subscriptions,
        buttonStatus: subscriptions?.banner?.dismiss
      });
    }
  };

  if (!data?.isSubscriptions || !isShowBanner) return null;
  if (currentTime < timeBannerOne) return null;
  if (offBanner > 2) return null;
  return (
    <div className="rocopa rocopa--banner">
      <div className="rocopa__body">
        <div className="banner banner--market">
          <div className="banner__inner">
            <div className="banner__body">
              <div className="mask" onClick={onClickOpenPayment}>
                <ImageMask
                  image={image_url || imageUrlRight}
                  imageMobile={image_url || imageUrlRight}
                />
              </div>
              <div className="banner__content absolute full-x middle-v">
                <div className="grid-x align-middle">
                  <div
                    className="cell auto large-offset-5 xxlarge-offset-5 padding-small-up-8 padding-medium-up-20 padding-xlarge-up-28 padding-xlarge-up-right-56"
                    style={isMobile ? {} : { paddingRight: '14.5vw' }}
                  >
                    <div
                      className="text text-white text-small-up-12 text-medium-up-18 text-large-up-20 text-xxxlarge-up-28 align-center-medium-down margin-small-up-bottom-6 margin-large-up-bottom-12 margin-xxlarge-up-bottom-16"
                      dangerouslySetInnerHTML={{ __html: message || '' }}
                    />
                    <div
                      className={`button-group justify-content-center-medium-down ${
                        isStatus ? 'child-auto-medium-down' : 'child-shrink-medium-down'
                      }`}
                    >
                      <button
                        className={`button button--light button--small-up button--large-up m-b ${
                          dismiss ? '' : 'hollow'
                        }`}
                        title={action}
                        onClick={onClickOpenPayment}
                      >
                        <span className={`text ${dismiss ? '' : 'text-white'}`}>{action}</span>
                      </button>
                      {isStatus && (
                        <button
                          className="button button--light hollow button--small-up button--large-up m-b"
                          title={dismiss}
                          onClick={onSkipBanner}
                        >
                          <span className="text text-white">{dismiss}</span>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default BannerUserPackageInfo;

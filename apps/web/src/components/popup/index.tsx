import React, { memo, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import PopupItem from '@models/PopupItem';
import { POPUP } from '@constants/constants';
import { getConfigPopup, openPopup } from '@actions/popup';
import PopupCatError from '@components/popup/PopupCatError';
import PopupDetailLoyalty from '@components/popup/PopupDetailLoyalty';
import PopupNotification from '@components/popup/PopupNotification';
import PopupLobbyGuide from '@components/popup/PopupLobbyGuide';
import PopupRedeemVoucher from '@components/popup/PopupRedeemVoucher';
import PopupDeleteProfile from '@components/popup/PopupDeleteProfile';
import PopupRecreatePinCode from '@components/popup/PopupRecreatePinCode';
import PopupPinCodeOverInput from '@components/popup/PopupPinCodeOverInput';
import PopupPinCode from '@components/popup/PopupPinCode';
import PopupRestoreAccount from '@components/popup/PopupRestoreAccount';
import PopupReferralCode from '@components/popup/PopupReferralCode';
import PopupDownLoadApp from '@components/popup/PopupDownLoadApp';
import PopupTVodPreOrdered from '@components/popup/PopupTVodPreOrdered';
import PopupTVodPreOrderExpired from '@components/popup/PopupTVodPreOrderExpired';
import PopupTVodLiveCanRental from '@components/popup/PopupTVodLiveCanRental';
import PopupTVodExpired from '@components/popup/PopupTVodExpired';
import PopupWelcomeTVod from '@components/popup/PopupWelcomeTVod';
import PopupTVodEndLiveStream from '@components/popup/PopupTVodEndLiveStream';
import PopupTVodRented from '@components/popup/PopupTVodRented';
import PopupRating from '@components/popup/PopupRating';
import PopupPrePay from '@components/popup/PopupPrePay';
import PopupQRShopeePay from '@components/popup/PopupQRShopeePay';
import PopupFavoriteList from '@components/popup/PopupFavoriteList';
import PopupSortingFavorite from '@components/popup/PopupSortingFavorite';
import PopupSuggestCancelPackage from '@components/popup/PopupSuggestCancelPackage';
import PopupQRZaloPay from '@components/popup/PopupQRZaloPay';
import PopupUserReport from '@components/basic/Player/PlayerControl/Report/PopupUserReport';
import PopupTitleRestrict from '@components/popup/PopupTitleRestrict';
import PopupConcurrentScreenAndLimitCUU from '@components/popup/PopupConcurrentScreenAndLimitCUU';
import PopupEngagement from '@components/popup/PopupEngagement';
import PopupPVodRegister from '@components/popup/PopupPVodRegister';
import PopupPolicyAnnounceConfirm from '@components/header/notification/PopupPolicyAnnounceConfirm';
import PopupPolicyAnnounceSync from '@components/header/notification/PopupPolicyAnnounceSync';
import PopupSurveyPayment from './PopupSurveyPayment';
import PopupLimitRegion from './PopupLimitRegion';
import PopupFirstPay from './PopupFirstPay';
import PopupTSVod from './PopupTSVOD';
import PopupTriggerPaymentSvod from './PopupTriggerPaymentSvod';
import PopupTriggerAuth from './PopupTriggerAuth';
const Popup = memo(({ isUnderConstruction }: any) => {
  const dispatch = useDispatch();
  const popupData = useSelector((state: any) => state?.Popup);
  const popupName = useMemo(() => popupData?.popupName, [popupData]);
  const noControl = useMemo(() => popupData?.noControl, [popupData]);
  const allConfigPopup = useMemo(() => popupData?.configs, [popupData]);
  const { popupTriggerPayment } = useSelector((state: any) => state?.Popup);
  const popupOpenConfig = useMemo(
    () => (popupName ? new PopupItem(allConfigPopup?.[popupName] || {}) : {}),
    [popupName, allConfigPopup]
  );
  const loadingPopup = useMemo(() => !allConfigPopup || isEmpty(allConfigPopup), [allConfigPopup]);
  const newParams = {
    ...popupData,
    ...(popupTriggerPayment ? popupTriggerPayment : popupOpenConfig),
    loadingPopup
  };
  useEffect(() => {
    if (!isUnderConstruction && isEmpty(allConfigPopup)) {
      dispatch(getConfigPopup());
    }
  }, [isUnderConstruction, allConfigPopup]);

  const closePopup = () => {
    dispatch(openPopup({ noControl }));
  };
  if (typeof window === 'undefined' || !popupName) return null;

  switch (popupName) {
    case POPUP.NAME.USER_REPORT:
      return <PopupUserReport {...newParams} />;
    case POPUP.NAME.SUGGEST_CANCEL_PACKAGE:
      return <PopupSuggestCancelPackage closePopup={closePopup} {...newParams} />;
    case POPUP.NAME.SORT_FAVORITE_LIST:
      return <PopupSortingFavorite />;
    case POPUP.NAME.REQUEST_FAVORITE_LIST:
      return <PopupFavoriteList {...newParams} />;
    case POPUP.NAME.QR_ZALO_PAY:
      return <PopupQRZaloPay {...newParams} />;
    case POPUP.NAME.QR_SHOPEE_PAY:
      return <PopupQRShopeePay {...newParams} />;
    case POPUP.NAME.PRE_PAY:
      return <PopupPrePay {...newParams} />;
    case POPUP.NAME.POPUP_RATING:
      return <PopupRating {...newParams} />;
    case POPUP.NAME.END_LIVESTREAM:
      return <PopupTVodEndLiveStream {...newParams} />;
    case POPUP.NAME.TVOD_ON_BOARDING:
      return <PopupWelcomeTVod {...newParams} />;
    case POPUP.NAME.TVOD_RENTED:
      return <PopupTVodRented {...newParams} />;
    case POPUP.NAME.TVOD_EXPIRED:
      return <PopupTVodExpired {...newParams} />;
    case POPUP.NAME.TVOD_LIVE_CAN_RENTAL:
      return <PopupTVodLiveCanRental {...newParams} />;
    case POPUP.NAME.TVOD_LIVE_EXPIRED:
    case POPUP.NAME.TVOD_PRE_ORDER_EXPIRED:
    case POPUP.NAME.TVOD_PRE_ORDER_PROMOTIONAL_PRICE_EXPIRED:
      return <PopupTVodPreOrderExpired {...newParams} />;
    case POPUP.NAME.TVOD_PRE_ORDERED:
      return <PopupTVodPreOrdered {...newParams} />;
    case POPUP.NAME.DOWNLOAD_APP:
      return <PopupDownLoadApp {...newParams} />;
    case POPUP.NAME.REFERRAL_CODE:
      return <PopupReferralCode {...newParams} />;
    case POPUP.NAME.USER_RESTORE:
      return <PopupRestoreAccount {...newParams} />;
    case POPUP.NAME.PIN_CODE:
      return <PopupPinCode {...newParams} />;
    case POPUP.NAME.PIN_CODE_OVER_INPUT:
      return <PopupPinCodeOverInput {...newParams} />;
    case POPUP.NAME.RECREATE_PIN_CODE:
      return <PopupRecreatePinCode {...newParams} />;
    case POPUP.NAME.DELETE_PROFILE:
    case POPUP.NAME.PROFILE_HAS_DELETED:
      return <PopupDeleteProfile {...newParams} />;
    case POPUP.NAME.LOBBY_GUIDE:
      return <PopupLobbyGuide {...newParams} />;
    case POPUP.NAME.REDEEM_VOUCHER:
      return <PopupRedeemVoucher {...newParams} />;
    case POPUP.NAME.DETAIL_LOYALTY:
      return <PopupDetailLoyalty {...newParams} />;
    case POPUP.NAME.TITLE_RESCTRICTION:
      return <PopupTitleRestrict {...newParams} />;
    case POPUP.NAME.CONCURRENT_SCREEN:
    case POPUP.NAME.LIMIT_CCU:
      return <PopupConcurrentScreenAndLimitCUU {...newParams} />;
    case POPUP.NAME.PLAYER_ERROR_VOD:
    case POPUP.NAME.PLAYER_ERROR_LIVETV:
    case POPUP.NAME.PLAYER_ERROR_LIVESTREAM:
    case POPUP.NAME.PLAYER_ERROR_PREMIERE:
      return <PopupCatError {...newParams} />;
    case POPUP.NAME.ENGAGEMENT_DIALOG:
      return <PopupEngagement {...newParams} />;
    case POPUP.NAME.PVOD_REGISTER:
      return <PopupPVodRegister {...newParams} />;
    case POPUP.NAME.POLICY_ANNOUNCE_CONFIRM:
      return <PopupPolicyAnnounceConfirm {...newParams} />;
    case POPUP.NAME.POLICY_ANNOUNCE_SYNC:
      return <PopupPolicyAnnounceSync {...newParams} />;
    case POPUP.NAME.SURVEY_PAYMENT:
      return <PopupSurveyPayment {...newParams} />;
    case POPUP.NAME.LIMIT_REGION:
      return <PopupLimitRegion {...newParams} />;
    case POPUP.NAME.TRIGGER_FIRSTPAY:
      return <PopupFirstPay {...newParams} />;
    case POPUP.NAME.TVOD_VIP_UPSELL:
      return <PopupTSVod closePopup={closePopup} {...newParams} />;
    case POPUP.NAME.SVOD_TRIGGER:
      return <PopupTriggerPaymentSvod {...newParams} />;
    case POPUP.NAME.PLAYER_TRIGGER_AUTH:
      return <PopupTriggerAuth closePopup={closePopup} {...newParams} />;
    default:
      return <PopupNotification {...newParams} />;
  }
});

export default Popup;

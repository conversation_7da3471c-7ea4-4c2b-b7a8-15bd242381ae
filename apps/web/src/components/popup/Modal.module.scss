/* // Modules */
@use 'sass:math';

/* =============== function caculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);
  //
  @return #{$remSize}rem;
}

/* =============== style =============== */
.button {
  @media only screen and (max-width: 375px) {
    height: rem(36);
  }
}

.gray-button {
  background-color: #333333 !important;
}

.textMuted {
  & > span,
  b {
    color: white !important;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }

    @media screen and (min-width: 1024px) {
      font-size: 15px;
    }
  }
}

.counter {
  background: #333;
  background-clip: padding-box;
  border-radius: 100%;
  width: rem(16);
  top: rem(4);

  .step {
    width: rem(16);
  }

  &::after {
    content: '';
    position: absolute;
    top: rem(-1);
    bottom: rem(-1);
    left: rem(-1);
    right: rem(-1);
    background: linear-gradient(to bottom right, #3ac882, #97e98a);
    z-index: -1;
    border-radius: 100%;
    color: #3ac882;
  }
}

.modal-lobby-guide {
  background-color: #000000;
}

.modal-title-restriction {
  @apply md:mx-6 mx-3 relative;
  border-top: 1px solid #454545;

  .profile-item {
    @apply w-full rounded-t-lg relative z-10 py-4;
    border-bottom: 1px solid #454545;

    &.disabled {
      @apply opacity-50 pointer-events-none;
      span {
        @apply border-[#646464];
      }
    }

    .checkbox {
      @apply flex items-center pl-0;
    }

    .label {
      @apply w-full py-3 ml-2 text-sm font-medium cursor-pointer;
    }

    .box {
      @apply relative md:w-[40px] md:h-[40px] h-[32px] w-[32px] rounded-full;
    }

    .icon-kid {
      @apply absolute top-0 right-0 w-[11px] h-[11px] shadow-[0_2px_4px_0_rgba(0,0,0,0.3)] bg-[#3ac882] rounded-full flex items-center justify-center;
    }

    .info {
      @apply pl-4;
      .name {
        @apply font-medium text-white text-[16px] md:text-lg;
      }

      .note {
        @apply text-[#646464] text-xs md:text-sm;
      }
    }
  }

  .warning-text {
    @apply text-[#F1C21B] flex items-center absolute top-[calc(100%+7px)] -left-3 w-[calc(100%+24px)] justify-center;
  }
}

.loyalty {
  &__content {
    background: #222;
    padding: rem(24) rem(16) 0;

    &__info {
      max-height: rem(370);
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: rem(4);
      }

      &::-webkit-scrollbar-track {
        margin-bottom: rem(24);
        background: rgba(100, 100, 100, 0.3);
        border-radius: rem(2);
        border-left: rem(1) solid #222;
        border-right: rem(1) solid #222;
      }

      &::-webkit-scrollbar-thumb {
        visibility: visible;
        background: #606060;
      }
    }
  }
}

@media screen and (min-width: 1024px) {
  .modal-xlarge {
    max-width: rem(896) !important;

    .content {
      max-height: rem(460);
    }
  }
}

.concurrentScreen {
  @apply flex items-center;
  &Point {
    @apply text-white text-2xl;
  }

  &Text {
    @apply text-white text-base font-bold ml-3;
  }
}

.triggerPaymentPopup {
  @apply md:h-auto h-[calc(100%_-_48px)];
  @apply w-full max-w-[1140px] md:w-[80%] xl:w-[65%] lg:w-[70%] 2xl:w-[60%] #{!important};
  & > div {
    @apply bg-cover bg-no-repeat bg-center rounded-tr-2xl rounded-tl-2xl md:rounded-none;
  }
  .body {
    @apply w-full md:flex xl:space-x-[55px] 2xl:space-x-[100px] md:space-x-8 p-3 lg:p-2 md:space-y-0 space-y-7 h-full;
    & > div:first-child {
      @apply md:w-[58.5%];
    }
    & > div:last-child {
      @apply md:w-[41.5%];
    }
    .movieInfo {
      @apply text-center md:text-left;
      .title {
        @apply text-white font-medium pb-0.5;
        @apply text-sm lg:text-xl 2xl:text-2xl #{!important};
      }
      .movieTitle {
        @apply text-[#0AD418] font-bold truncate pb-4 pt-1.5;
        @apply text-2xl lg:text-[32px] 2xl:text-[40px] #{!important};
      }
      .image {
        @apply py-3 md:py-6 lg:py-9 lg:pt-7 rounded-[10px] overflow-hidden;
      }
    }
  }
}

.triggerPaymentPopupSvod {
  @apply md:h-auto max-h-[calc(100vh-48px)];
  @apply w-[375px] md:w-[544px];
  & > div {
    @apply bg-cover bg-no-repeat bg-center rounded-none;
  }
  .body {
    @apply p-4 lg:p-8;
  }
  // =================
}
.triggerPaymentSvod {
  @apply max-w-full space-y-3;

  &Head {
    @apply flex flex-col justify-between w-full;
  }
  &Title {
    @apply text-[#0AD418] font-bold text-[24px] leading-[1.3] lg:text-[28px] line-clamp-2;
  }
  &SubTitle {
    @apply pr-9 md:pr-6;
    @apply text-white font-medium text-[1rem] leading-[1.5] lg:text-xl;
  }
  &Body {
    @apply flex flex-col justify-between w-full space-y-8;
  }
  .image {
    @apply rounded-[10px] overflow-hidden max-w-[343px] md:max-w-full;
  }
  &Button {
    @apply flex w-full min-w-[282px] items-center justify-center space-x-2 py-3 h-[48px] px-3 md:px-5;
    @apply rounded-full bg-gradient-to-r from-[#F1CE92] via-[#e3b664] to-[#F1CE92] shadow-md;

    &Text {
      @apply max-w-[calc(100%_-_32px)] truncate;
      @apply text-black !text-lg lg:text-[21px] font-[500];
    }
  }
  .note {
    @apply text-white font-[400] text-[12px] leading-[18px] tracking-[0.18px] mt-[28px];
  }
}

.triggerAuthPopup {
  @apply overflow-hidden;

  &Container {
    @apply w-full overflow-hidden top-0 translate-y-0;
  }

  &Content {
    @apply md:flex md:justify-end md:items-center md:pr-[6.25vw];
    @apply scale-x-[0.815] scale-y-[0.825];
  }
}

.buttonClose {
  @apply w-5 md:w-6 xl:w-12 h-5 md:h-6 xl:h-12;
  @apply absolute top-4 right-4 md:top-6 md:right-6 xl:top-[4.35vh] xl:right-[6.35vw] flex items-center justify-center z-[2];
}

/* eslint-disable react/destructuring-assignment */
import Head from 'next/head';
import { ROBOTS, DOMAIN_WEB, STATIC_DOMAIN, BUILD_ID } from '@config/ConfigEnv';
import ConfigSeo from '@config/ConfigSeo';
import { getSeoData } from '@services/detailServices';
import { PAGE } from '@constants/constants';
import React from 'react';
import {
  getBreadcrumbJsonLd,
  getContactServiceJsonLd,
  getDetailOpenGraphJsonLd,
  getListItemsJsonLD,
  getLocalBusinessJsonLd,
  getSiteLinksSearchBoxJsonLd,
  getSocialProfileJsonLd
} from './SeoDefaultAllPage';

const SeoNext = (props: any) => {
  const dataMetaTags = getMetaTags(props);
  const dataLinkTags = getLinkTags(props);
  const dataJsonLd = getJsonLD(props);
  const dataListItemsJsonLd = getListItemsJsonLD(props.listArrRibbon);
  const dataBreadcrumbJsonLd = getBreadcrumbJsonLd(props);
  const dataSocialProfileJsonLd = getSocialProfileJsonLd();
  const dataContactServiceJsonLd = getContactServiceJsonLd();
  const dataDetailJsonLd = getDetailOpenGraphJsonLd(props);
  const dataLocalBusinessJsonLd = getLocalBusinessJsonLd(props.listArrRibbon);
  const dataSiteLinksSearchBoxJsonLd =
    props.page === PAGE.HOME ? getSiteLinksSearchBoxJsonLd() : '';
  return (
    <>
      <Head>
        <title>{props.title}</title>
        {dataMetaTags}
        {/* {dataDetailJsonLd} */}
        {dataLinkTags}
        {dataListItemsJsonLd}
        {dataJsonLd}
      </Head>
      <div style={styles.hideSEO}>
        <img
          src={props?.shortenedLink}
          alt="VieON - Không Thể Rời Mắt"
          title="VieON - Không Thể Rời Mắt"
          height="1"
          width="1"
          style={{ border: '0' }}
        />
      </div>
      {dataSocialProfileJsonLd}
      {dataContactServiceJsonLd}
      {props.page === PAGE.HOME && dataLocalBusinessJsonLd}
      {dataSiteLinksSearchBoxJsonLd}
      {dataDetailJsonLd}
      {dataBreadcrumbJsonLd}
    </>
  );
};
const styles: any = {
  hideSEO: {
    position: 'absolute',
    color: 'transparent',
    zIndex: -9999,
    height: 0,
    overflow: 'hidden'
  }
};

const getMetaTags = ({
  title,
  description,
  url,
  contentType,
  img,
  robots,
  deepLink,
  card,
  imgWidth,
  imgHeight,
  thumbnail,
  content
}: any) => {
  card = card || ConfigSeo.seoDefault.card;
  description = description || ConfigSeo.seoDefault.description;
  title = title || ConfigSeo.seoDefault.title;
  url = url || DOMAIN_WEB;
  img = img || ConfigSeo.seoDefault.img;
  deepLink = deepLink || ConfigSeo.seoDefault.deepLink;

  const metaTags = [
    { charset: 'utf-8' },
    { httpEquiv: 'x-ua-compatible', content: 'ie=edge' },
    { httpEquiv: 'Content-Language', content: 'vi' },
    { httpEquiv: 'Content-Type', content: 'text/html; charset=utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
    { name: 'theme-color', content: '#000000' },
    { name: 'mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'description', content: description },
    { name: 'twitter:card', content: card },
    { name: 'twitter:title', content: title },
    { name: 'twitter:description', content: description },
    { name: 'twitter:image', content: img || ConfigSeo.seoDefault.img },
    { name: 'thumbnail', content: img },
    {
      name: 'apple-itunes-app',
      content: `app-id=1357819721, affiliate-data=${deepLink || 'vieonapp://home'}, app-argument=${
        deepLink || 'https://itunes.apple.com/VN/app/id1357819721'
      }`
    },
    { property: 'og:locale', content: 'vi_VN' },
    { property: 'og:title', content: title },
    { property: 'og:type', content: contentType || ConfigSeo.seoDefault.contentType },
    { property: 'og:url', content: DOMAIN_WEB + url },
    { property: 'og:image', content: img },
    { property: 'og:description', content: description },
    { property: 'og:site_name', content: ConfigSeo.seoDefault.nameSite },
    { property: 'al:android:url', content: deepLink },
    { property: 'al:ios:url', content: deepLink },
    { property: 'al:ios:app_store_id', content: ConfigSeo.seoDefault.appIOSID },
    { property: 'al:ios:app_name', content: ConfigSeo.seoDefault.appIOSName },
    { property: 'al:android:app_name', content: ConfigSeo.seoDefault.appAndroidName },
    { property: 'al:android:package', content: ConfigSeo.seoDefault.appAndroidPackage },
    { property: 'fb:app_id', content: ConfigSeo.seoDefault.fbAppId },
    { property: 'fb:pages', content: ConfigSeo.seoDefault.fbPage }
  ];

  if (thumbnail) {
    metaTags.push({ name: 'thumbnail', content: thumbnail });
  }

  if (ROBOTS) {
    metaTags.push({ name: 'robots', content: ROBOTS });
    metaTags.push({ name: 'googlebot', content: `${ROBOTS} ` });
    metaTags.push({ name: 'bingbot', content: `${ROBOTS} ` });
  } else if (robots) {
    metaTags.push({ name: 'robots', content: robots });
    metaTags.push({ name: 'googlebot', content: `${robots} ` });
    metaTags.push({ name: 'bingbot', content: `${robots} ` });
  }

  if (imgWidth) {
    metaTags.push({ property: 'og:image:width', content: imgWidth });
  }

  if (imgHeight) {
    metaTags.push({ property: 'og:image:height', content: imgHeight });
  }
  if (content) {
    const { tags } = content;
    const tagsDetail = (tags || []).map((item: any) => item?.slug);
    metaTags.push({ name: 'topic.tags', content: tagsDetail.toString() });
  }
  const data = [];
  for (let i = 0; i < metaTags.length; i += 1) {
    const meta = <meta {...metaTags[i]} key={i} />;
    data.push(meta);
  }

  return data;
};

const getLinkTags = ({ shortcut, amphtmlUrl, canonical, alternateUrl }: any) => {
  shortcut = shortcut || ConfigSeo.seoDefault.shortcut;

  const linkTags: any = [
    { rel: 'shortcut icon', href: shortcut },
    { rel: 'apple-touch-icon', href: `${STATIC_DOMAIN}icon.png?v=${BUILD_ID}` }
  ];
  if (canonical) {
    linkTags.push({ rel: 'canonical', href: canonical });
  }
  if (alternateUrl) {
    const altUrl = (alternateUrl || '').replace('https://m.', 'https://');
    linkTags.push({ rel: 'alternate', href: altUrl, media: 'only screen and (max-width: 640px)' });
  }
  if (amphtmlUrl) {
    linkTags.push({ rel: 'amphtml', href: amphtmlUrl });
  }
  const data = [];
  for (let i = 0; i < linkTags.length; i += 1) {
    data.push(<link {...linkTags[i]} key={i} />);
  }
  return data;
};
const getJsonLD = ({ description, content, contentEpisode, page }: any) => {
  const dataWebsite = {
    '@context': 'http://schema.org',
    '@type': 'WebSite',
    url: 'https://vieon.vn/',
    name: 'VieON',
    description
  };

  let vodData: any = '';

  if (page === PAGE.VOD) {
    vodData = getSeoData(content, contentEpisode);
  }

  const jsonWebsite = dataWebsite ? JSON.stringify(dataWebsite) : '';
  const jsonVod = vodData ? JSON.stringify(vodData) : '';

  return (
    <>
      {jsonVod && (
        <script
          async
          defer
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: jsonVod }}
        />
      )}
      <script
        async
        defer
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: jsonWebsite }}
      />
    </>
  );
};
export default SeoNext;

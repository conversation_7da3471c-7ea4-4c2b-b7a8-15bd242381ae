import React from 'react';
import { LocalBusinessJsonLd, NextSeo } from 'next-seo';
import Head from 'next/head';
import { ROBOTS, STATIC_DOMAIN, DOMAIN_WEB, BUILD_ID } from '@config/ConfigEnv';
import ConfigSeo from '@config/ConfigSeo';
import { HTTP_CODE, PAGE } from '@constants/constants';
import { getSeoData } from '@services/detailServices';
import { useSelector } from 'react-redux';

const SeoAllPage = React.memo((props: any) => {
  const { seo, listArrRibbon, contentEpisode, content, seo_config, oldData, isSeaSon } =
    props || {};
  if (!seo?.open_graph) return null;
  const defaultTitle = oldData?.title || seo?.open_graph?.page_title;
  const defaultDescription = oldData?.description || seo?.open_graph?.page_description;
  const defaultTitleSEOTag = seo?.title_seo_tag || oldData?.titleSeoTag;
  const defaultMetaDescription = seo?.meta_description || oldData?.description;
  const defaultImageURL = getDefaultImageURL({ arr: seo?.open_graph?.images_url });
  const iosAppId = seo?.open_graph?.ios_app_store_id;
  const fbAppId = seo?.open_graph?.fb_app_id;
  const currentDate = new Date();
  currentDate.setDate(currentDate.getDate() - 1);
  currentDate.setHours(2, 3, 4, 5);
  const { featureFlag } = useSelector((state: any) => state?.App?.webConfig || {});
  const isNoIndexRobots = (ROBOTS || seo?.meta_robots)?.indexOf('noindex') > -1;
  const isNoFollowRobots = (ROBOTS || seo?.meta_robots)?.indexOf('nofollow') > -1;
  const additionalMetaTags: any = [
    {
      name: 'apple-itunes-app',
      content: `app-id=1357819721, affiliate-data=${
        seo?.open_graph?.deep_link || 'vieonapp://home'
      }, app-argument=${
        seo?.open_graph?.deep_link || 'https://itunes.apple.com/VN/app/id1357819721'
      }`
    },
    { name: 'theme-color', content: '#000000' },
    { name: 'mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
    // { name: 'thumbnail', content: seo?.open_graph?.images_url},
    { name: 'twitter:card', content: 'summary' },
    { name: 'twitter:title', content: defaultTitle },
    { name: 'twitter:description', content: defaultDescription },
    // { name: 'twitter:image', content: seo?.open_graph?.images_url},
    { property: 'og:locale', content: 'vi_VN' },
    { property: 'og:type', content: 'website' },
    { property: 'al:android:url', content: seo?.open_graph?.deep_link || seo?.deeplink },
    { property: 'al:ios:url', content: seo?.open_graph?.deep_link || seo?.deeplink },
    { property: 'al:ios:app_store_id', content: iosAppId },
    { property: 'al:ios:app_name', content: seo?.open_graph?.ios_app_name },
    { property: 'al:android:app_name', content: seo?.open_graph?.android_app_name },
    { property: 'al:android:package', content: seo?.open_graph?.android_package },
    { property: 'fb:pages', content: ConfigSeo.seoDefault.fbPage },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '1200' }
    // { property: 'og:image', content: seo?.open_graph?.images_url },
    // { property: 'og:title', content: defaultTitle},
  ];
  const additionalRobotsProps = {
    maxSnippet: -1,
    maxImagePreview: 'large',
    maxVideoPreview: -1
  };

  if (featureFlag?.showMetaUploadDate) {
    additionalMetaTags.push({ itemProp: 'uploadDate', content: currentDate.toISOString() });
  }
  const additionalLinkTags = [
    { rel: 'shortcut icon', content: ConfigSeo.seoDefault.shortcut },
    {
      rel: 'apple-touch-icon',
      content: `${STATIC_DOMAIN}icon.png?v=${BUILD_ID}`
    }
  ];

  const dataBreadcrumbs = parseSnippetJsonLd({
    data: seo?.breadcrumb,
    isBreadCrumbs: true
  });
  const dataLocalBusinessJsonLd = getLocalBusinessJsonLd({
    arrRibbonImage: listArrRibbon,
    slug: seo?.slug
  });
  const dataListItemsJsonLd = getListItemsJsonLD(listArrRibbon);
  const dataJsonLd = getJsonLD({ content, contentEpisode, isSeaSon, seo });
  const dataSnippet =
    !!seo?.snippet && getJsonLD({ content, contentEpisode, isSeaSon, snippet: seo?.snippet, seo });

  const dataSnippetDefault = parseSnippetJsonLd({ data: seo_config });
  return (
    <>
      <Head>
        <title>{defaultTitleSEOTag}</title>
        {defaultImageURL.map((e) => e)}
        {dataListItemsJsonLd}
      </Head>
      <NextSeo
        noindex={!!isNoIndexRobots}
        nofollow={!!isNoFollowRobots}
        robotsProps={additionalRobotsProps as any}
        description={defaultMetaDescription}
        canonical={seo?.canonical_tag}
        mobileAlternate={{
          media: 'only screen and (max-width: 640px)',
          href: seo?.alternate
        }}
        facebook={{
          appId: fbAppId
        }}
        additionalMetaTags={additionalMetaTags}
        additionalLinkTags={additionalLinkTags as any}
        openGraph={{
          title: defaultTitle,
          description: defaultDescription,
          url: seo?.open_graph?.page_url,
          site_name: 'VieON'
        }}
      />
      {/* Schema LD JSON */}
      {dataBreadcrumbs}
      {dataSnippetDefault}
      {dataLocalBusinessJsonLd}
      {dataJsonLd}
      {dataSnippet}
    </>
  );
});

const getDefaultImageURL = ({ arr }: any) => {
  const arrayImage: any = [];
  const data = [];
  if (arr?.length) {
    (arr || []).forEach((el: any, index: any) => {
      if (el.length > 0) {
        if (index === 0) arrayImage.push({ property: 'thumbnail', content: el });
        arrayImage.push({ property: 'og:image', content: el });
        arrayImage.push({ property: 'twitter:image', content: el });
      }
    });

    for (let i = 0; i < arrayImage.length; i += 1) {
      const meta = <meta {...arrayImage[i]} key={i} />;
      data.push(meta);
    }
  }
  return data;
};
const getListItemsJsonLD = (listArrRibbon: any) => {
  if (!listArrRibbon) return '';
  const listItems: any = [];
  listArrRibbon.map((item: any, index: any) =>
    listItems.push({
      '@type': 'ListItem',
      position: index + 1,
      url: DOMAIN_WEB + item.url
    })
  );
  const data = {
    '@context': 'http://schema.org',
    '@type': 'ItemList',
    itemListElement: listItems
  };
  const jsonData = data ? JSON.stringify(data) : '';

  return (
    <script async defer type="application/ld+json" dangerouslySetInnerHTML={{ __html: jsonData }} />
  );
};
const parseSnippetJsonLd = ({ data }: any) => {
  if (data) {
    const jsonDataParsed = data.toString() || '';
    return <div dangerouslySetInnerHTML={{ __html: jsonDataParsed }} />;
  }
  return null;
};
const getJsonLD = ({ content, contentEpisode, isSeaSon, snippet, seo }: any) => {
  if (snippet) return parseSnippetJsonLd({ data: snippet });
  if (!content) return;
  const vodData = getSeoData(content, contentEpisode, isSeaSon, seo);
  const jsonVod = vodData ? JSON.stringify(vodData) : '';

  return (
    <>
      {jsonVod && (
        <script
          async
          defer
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: jsonVod }}
        />
      )}
    </>
  );
};
const getLocalBusinessJsonLd = ({ arrRibbonImage, slug }: any) => {
  if (!arrRibbonImage || slug !== PAGE.HOME_DEFAULT) return;
  const dataImageDefault = [
    ConfigSeo.seoIMG.defaultLogo1x2,
    ConfigSeo.seoIMG.defaultLogo4x3,
    ConfigSeo.seoIMG.defaultLogo16x9
  ];
  arrRibbonImage.map((item: any) => dataImageDefault.push(item.imageURL));
  return (
    <LocalBusinessJsonLd
      type="Media company in Ho Chi Minh City"
      id="https://g.page/r/CfIiZ5JpiEjeEAE"
      name="VieON"
      description="Ứng Dụng Xem Online Phim Hàn, Phim Thái, Trung, Kho Phim Truyện, Show Giải Trí Độc Quyền, Mới Cập Nhật Liên Tục. Các Chương Trình Truyền Hình Trực Tuyến, Vtv, Htv, Thể Thao 24 Giờ Mọi Lúc Mọi Nơi."
      url={DOMAIN_WEB || 'https://vieon.vn/'}
      telephone="1800 599920"
      address={{
        streetAddress: '5th floor, 222, Pasteur, Quận 3',
        addressLocality: 'Hồ Chí Minh',
        postalCode: '700000',
        addressCountry: 'VN'
      }}
      geo={{
        latitude: '10.7842003',
        longitude: '106.6922989'
      }}
      images={dataImageDefault}
      openingHours={[
        {
          opens: '09:00',
          closes: '23:00',
          dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        }
      ]}
      sameAs={[
        'https://vieon.vn/',
        'https://www.facebook.com/vieonofficial',
        'https://www.linkedin.com/company/vieon',
        'https://www.instagram.com/vieon.official/',
        'https://www.youtube.com/@VieONApp',
        'https://www.youtube.com/VieChannelHTV2/',
        'https://en.wikipedia.org/wiki/DatVietVAC',
        'https://vi.wikipedia.org/wiki/VieON',
        'https://wikitia.com/wiki/VieON',
        'https://apps.apple.com/vn/app/id1357819721',
        'https://play.google.com/store/apps/details?id=vieon.phim.tv'
      ]}
    />
  );
};
export const redirectTool = ({ redirect, res }: any) => {
  if (res) {
    if (redirect?.pattern_from !== redirect?.pattern_to) {
      res.writeHead(redirect?.http_status, { Location: redirect?.pattern_to });
      res.end();
    } else if (redirect?.http_status === HTTP_CODE.GONE) res.status(HTTP_CODE.GONE);
  }
};

export default SeoAllPage;

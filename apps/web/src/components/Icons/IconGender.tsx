import React from 'react';

const IconGender = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M14.5052 13.8658C14.5507 13.6908 14.5875 13.5136 14.6152 13.335C14.6248 13.2746 14.635 13.2148 14.6428 13.1541C14.6505 13.0935 14.6573 13.0231 14.6628 12.9575C14.6763 12.8068 14.686 12.6549 14.686 12.5004C14.686 12.3938 14.676 12.2895 14.6701 12.1844C14.5107 9.44921 12.3282 7.26675 9.59305 7.10734C9.48797 7.10149 9.38365 7.09149 9.27706 7.09149C8.81601 7.09319 8.35722 7.15393 7.91163 7.27222C7.07008 7.4918 6.29417 7.91154 5.64994 8.49578C5.62806 8.51597 5.60598 8.53446 5.58391 8.55615C5.49921 8.63519 5.41696 8.71707 5.33754 8.80177C5.30811 8.83346 5.27944 8.86553 5.25057 8.8976C5.17794 8.97929 5.10607 9.06116 5.03834 9.14681C5.00722 9.18548 4.97817 9.22717 4.94798 9.26773C4.88497 9.35168 4.82159 9.43506 4.76367 9.52278C4.7367 9.56334 4.71349 9.60654 4.68652 9.64804C4.59634 9.79198 4.51315 9.94063 4.43731 10.0938C4.39864 10.1742 4.35619 10.2542 4.3213 10.3372C4.29281 10.4028 4.26772 10.4702 4.24131 10.5373C4.20735 10.625 4.17415 10.7128 4.14434 10.8024C4.12246 10.8688 4.10265 10.9354 4.08341 11.0029C4.05549 11.0984 4.03078 11.1961 4.00833 11.293C3.9938 11.3576 3.97909 11.4217 3.96664 11.4862C3.94589 11.596 3.93004 11.7071 3.91608 11.8192C3.90872 11.8752 3.89986 11.9301 3.89439 11.9865C3.87816 12.1561 3.86816 12.3268 3.86816 12.5004C3.86967 15.1875 5.84462 17.4654 8.50436 17.8478V19.4547H7.34531C6.91859 19.4547 6.57261 19.8007 6.57261 20.2274C6.57261 20.6541 6.91859 21.0001 7.34531 21.0001H8.50436V22.5455C8.50436 22.9722 8.85034 23.3182 9.27706 23.3182C9.70378 23.3182 10.0498 22.9722 10.0498 22.5455V21.0001H11.2088C11.6355 21.0001 11.9815 20.6541 11.9815 20.2274C11.9815 19.8007 11.6355 19.4547 11.2088 19.4547H10.0498V17.8502C10.1305 17.8385 10.2112 17.8263 10.2909 17.8116C10.3557 17.7991 10.4199 17.7844 10.484 17.7699C10.5817 17.7474 10.6772 17.7227 10.7742 17.6948C10.8417 17.6756 10.9083 17.6563 10.9747 17.6339C11.0643 17.6041 11.152 17.5709 11.2397 17.5369C11.3069 17.5105 11.3743 17.4854 11.4399 17.4569C11.5229 17.4205 11.6029 17.3796 11.6833 17.3409C11.8378 17.2637 11.9877 17.1798 12.1334 17.089C12.172 17.0643 12.2149 17.0419 12.2543 17.0157C12.342 16.9578 12.4254 16.8944 12.5097 16.8314C12.5482 16.8012 12.5908 16.7723 12.6303 16.741C12.7159 16.6733 12.7978 16.6016 12.8795 16.5288C12.9115 16.4999 12.9436 16.4712 12.9751 16.442C13.0596 16.3624 13.1415 16.2801 13.2209 16.1954C13.2415 16.1734 13.2596 16.1515 13.2811 16.1294C13.8659 15.4848 14.2858 14.7079 14.5052 13.8658ZM5.41356 12.5004C5.41356 12.3806 5.42092 12.2623 5.4313 12.145C5.43488 12.1103 5.43903 12.0757 5.44337 12.0414C5.45337 11.9552 5.4662 11.8699 5.48185 11.7856C5.48808 11.7524 5.49393 11.7188 5.50091 11.6856C5.52487 11.5743 5.55222 11.4638 5.58542 11.3564C5.58901 11.3451 5.59353 11.3344 5.59712 11.3232C5.62806 11.2266 5.66314 11.1301 5.70144 11.0387C5.71578 11.0037 5.73106 10.9693 5.74653 10.935C5.77822 10.8639 5.8118 10.7939 5.84745 10.725C5.86481 10.6918 5.88179 10.6579 5.8999 10.6254C5.95177 10.5319 6.00667 10.4404 6.06572 10.3519C6.08156 10.3283 6.0993 10.3062 6.11552 10.2836C6.16268 10.2161 6.21211 10.1508 6.26361 10.088C6.29172 10.0529 6.32077 10.0189 6.35001 9.98478C6.39415 9.93384 6.43924 9.88442 6.48565 9.83575C6.51658 9.8033 6.54715 9.7701 6.57959 9.73878C6.64864 9.67106 6.72014 9.60578 6.79409 9.54561C6.82993 9.51542 6.86709 9.48769 6.90407 9.45902C6.9567 9.41808 7.00952 9.37752 7.06442 9.33885C7.10611 9.30998 7.14912 9.28169 7.19194 9.25396C7.24344 9.22151 7.29494 9.19019 7.34644 9.16001C7.39021 9.13454 7.43341 9.1087 7.47831 9.08512C7.56622 9.03871 7.65602 8.99494 7.74864 8.95533C9.00748 8.41316 10.4595 8.5786 11.564 9.39016C12.6688 10.2017 13.2607 11.5377 13.1196 12.9011C13.1196 12.9169 13.1143 12.932 13.1128 12.9475C13.0992 13.0633 13.0823 13.1791 13.0587 13.2923C13.0587 13.2982 13.0555 13.3037 13.0543 13.3095C13.001 13.5568 12.9232 13.7985 12.8225 14.0307C12.784 14.1228 12.7391 14.2124 12.6927 14.3012C12.6691 14.3459 12.6433 14.3889 12.6182 14.4325C12.5876 14.484 12.5561 14.5355 12.5239 14.587C12.4961 14.63 12.4678 14.6729 12.439 14.7145C12.4003 14.7694 12.3616 14.8223 12.3188 14.8749C12.2901 14.9117 12.2624 14.949 12.2322 14.9847C12.17 15.0588 12.1047 15.1303 12.039 15.1997C12.0077 15.2318 11.9749 15.262 11.9425 15.2929C11.8938 15.3395 11.844 15.385 11.7931 15.4293C11.7589 15.4582 11.725 15.4872 11.6902 15.5152C11.6263 15.5667 11.5608 15.6155 11.4935 15.6619C11.4708 15.6782 11.4491 15.6955 11.4259 15.711C11.3375 15.7704 11.246 15.8268 11.1524 15.8772C11.12 15.8957 11.086 15.9123 11.0528 15.9296C10.9839 15.9651 10.914 15.9989 10.843 16.0305C10.8085 16.0458 10.7742 16.0613 10.7391 16.0756C10.6459 16.1143 10.5512 16.149 10.4546 16.18C10.4435 16.1835 10.4327 16.1881 10.4214 16.1917C10.3137 16.2249 10.2035 16.2522 10.0922 16.2762C10.059 16.2831 10.0254 16.289 9.99222 16.295C9.9079 16.3109 9.82263 16.3237 9.73642 16.3337C9.70208 16.338 9.66775 16.3422 9.63285 16.3458C9.51702 16.3562 9.39723 16.3635 9.27744 16.3635C7.14365 16.3637 5.41375 14.6342 5.41356 12.5004Z"
      fill="#CCCCCC"
    />
    <path
      d="M20.0953 0.909729H17.0045C16.5777 0.909729 16.2318 1.25571 16.2318 1.68243C16.2318 2.10915 16.5777 2.45513 17.0045 2.45513H18.2299L16.3476 4.28689C14.4905 2.9143 11.9666 2.87317 10.0656 4.18446C8.16482 5.49575 7.30686 7.86987 7.93034 10.0933C8.55382 12.3169 10.521 13.8987 12.8265 14.0305C13.0349 13.5474 13.1419 13.0266 13.141 12.5002C11.1592 12.5006 9.49837 11.0016 9.29671 9.03006C9.09486 7.0585 10.4177 5.25409 12.3585 4.85321C14.2991 4.45215 16.2286 5.5846 16.8247 7.47466C17.4208 9.36472 16.4902 11.3991 14.6705 12.1842C14.6764 12.2893 14.6864 12.3936 14.6864 12.5002C14.6847 12.9613 14.6239 13.4201 14.5056 13.8657C16.2421 13.4163 17.6419 12.1335 18.2405 10.4425C18.8391 8.75161 18.5582 6.87363 17.4908 5.43198L19.3226 3.54777V4.77323C19.3226 5.19995 19.6685 5.54593 20.0953 5.54593C20.522 5.54593 20.868 5.19995 20.868 4.77323V1.68243C20.868 1.25571 20.522 0.909729 20.0953 0.909729Z"
      fill="#CCCCCC"
    />
  </svg>
);

export default IconGender;

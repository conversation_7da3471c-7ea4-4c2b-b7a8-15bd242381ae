import React from 'react';

const IconEmail = ({ size }: any) => (
  <svg
    width={size === 'small' ? 20 : 32}
    height={size === 'small' ? 20 : 32}
    viewBox="0 0 33 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="Icon/Email/S">
      <g id="Group 1662">
        <path
          id="Vector (Stroke)"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.16699 9.91603C3.16699 8.12301 4.66135 6.66602 6.50034 6.66602H26.5003C28.3393 6.66602 29.8337 8.12301 29.8337 9.91603V23.416C29.8337 25.209 28.3393 26.666 26.5003 26.666H6.50034C4.66135 26.666 3.16699 25.209 3.16699 23.416V9.91603ZM6.50034 8.66601C5.79424 8.66601 5.21827 9.22758 5.21827 9.91603V23.416C5.21827 24.1045 5.79424 24.666 6.50034 24.666H26.5003C27.2064 24.666 27.7824 24.1045 27.7824 23.416V9.91603C27.7824 9.22758 27.2064 8.66601 26.5003 8.66601H6.50034Z"
          fill="#DEDEDE"
        />
        <path
          id="Vector 4 (Stroke)"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.867 18.6865L4.5 9.5595L5.75218 8L16.7337 16.8175L27.1472 8.016L28.4383 9.54349L17.6472 18.6641C17.1356 19.0965 16.3893 19.1059 15.867 18.6865Z"
          fill="#DEDEDE"
        />
      </g>
    </g>
  </svg>
);

export default IconEmail;

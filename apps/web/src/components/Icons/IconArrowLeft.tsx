import React from 'react';

const IconArrowLeft = ({ size, className }: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size || 24}
    height={size || 24}
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className || ''}
  >
    <title>Icon chevron right round</title>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.43633 12.0142C9.26172 11.8232 9.26172 11.5305 9.43632 11.3395L16.4008 3.72061C16.5871 3.51679 16.5729 3.20053 16.369 3.01422L15.631 2.33952C15.4271 2.15321 15.1109 2.1674 14.9246 2.37122L7.42308 10.5777C7.42304 10.5777 7.423 10.5777 7.42297 10.5778C7.42285 10.5779 7.42272 10.578 7.4226 10.5782C7.28366 10.7301 7.17765 10.9057 7.10668 11.093C7.03565 11.2804 7 11.4785 7 11.6769C7 11.8752 7.03565 12.0734 7.10668 12.2608C7.17765 12.448 7.28366 12.6236 7.4226 12.7756C7.42272 12.7757 7.42285 12.7758 7.42297 12.776C7.423 12.776 7.42304 12.776 7.42308 12.7761L14.9246 20.9825C15.1109 21.1863 15.4271 21.2005 15.631 21.0142L16.369 20.3395C16.5729 20.1532 16.5871 19.8369 16.4008 19.6331L9.43633 12.0142Z"
      fill="currentColor"
    />
  </svg>
);

export default IconArrowLeft;

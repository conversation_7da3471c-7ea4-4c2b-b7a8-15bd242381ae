import { createTimeout, mobileCheck } from '@helpers/common';
import PaymentApi from '@apis/Payment';
import { TEXT } from '@constants/text';
import { setLoading, setToast } from '@actions/app';
import { createTransactionSuccess, checkLinkZaloPayTransaction } from '@actions/payment';
import { openPopup } from '@actions/popup';
import { PAGE, POPUP, ZALOPAY } from '@constants/constants';
import { DOMAIN_WEB } from '@config/ConfigEnv';
import ConfigCookie from '@config/ConfigCookie';
import TrackingPayment from '@tracking/functions/payment';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';

declare const window: any;
class ZaloPayCheckout {
  dispatch: any;
  inApp: any;
  isMobile: any;
  isPvodContent: any;
  isRentalContent: any;
  paymentInfo: any;
  promotionData: any;
  queryString: any;
  recurring: any;
  router: any;
  selectedMethod: any;
  selectedPackage: any;
  selectedTerm: any;
  totalAmount: any;
  trackingPayment: any;
  transactionData: any;
  valueReferralCode: any;
  zaloPayLinked: any;
  constructor(params: any) {
    this.router = params?.router;
    this.selectedTerm = params?.selectedTerm;
    this.promotionData = params?.promotionData;
    this.recurring = params?.recurring;
    this.inApp = params?.inApp;
    this.dispatch = params?.dispatch;
    this.selectedPackage = params?.selectedPackage;
    this.totalAmount = params?.totalAmount;
    this.selectedMethod = params?.selectedMethod;
    this.zaloPayLinked = params?.zaloPayLinked;
    this.queryString = params?.queryString;
    this.isRentalContent = params?.isRentalContent;
    this.isPvodContent = params?.isPvodContent;
    this.paymentInfo = params?.paymentInfo;
    this.valueReferralCode = params?.valueReferralCode;
    this.transactionData = params?.transactionData;
    this.isMobile = mobileCheck();
    this.trackingPayment = new TrackingPayment();
  }

  handleZaloPay = async () => {
    let transactionData = null;
    if (this.isRentalContent || this.isPvodContent) {
      transactionData = await this.handleTVod();
    } else if (!this.recurring) {
      transactionData = await this.handleNonRecurring();
    } else {
      transactionData = await this.handleRecurring();
    }
    return transactionData;
  };

  handleRecurring = async () => {
    let transactionData: any = null;
    this.zaloPayLinked = await this.dispatch(checkLinkZaloPayTransaction());
    const affNetwork = ConfigCookie.load(ConfigCookie.KEY.AFF_NETWORK) || '';
    const affSId = ConfigCookie.load(ConfigCookie.KEY.AFF_SID) || '';
    const notTrackAccessTrade = true; // BE will check this case in charge-by-token
    if (this.zaloPayLinked) {
      this.dispatch(setLoading(true));
      const transactionChargeZaloPay = await PaymentApi.chargeZaloPay({
        packageId: this.selectedTerm?.id,
        inApp: !!this.inApp,
        promotionCode: this.promotionData?.promotionCode || this.selectedTerm?.giftCode || '',
        utmSource: affNetwork,
        trackingId: affSId
      });
      transactionData = transactionChargeZaloPay?.data;
      this.dispatch(
        createTransactionSuccess({
          data: transactionData,
          notTrackAccessTrade,
          valueReferralCode: this.valueReferralCode
        })
      );
      if (transactionChargeZaloPay?.success) {
        this.gotoResultPageZaloPay(transactionChargeZaloPay?.data);
      } else {
        this.dispatch(setLoading(false));
        this.dispatch(
          setToast({
            message:
              transactionChargeZaloPay?.data?.message ||
              transactionChargeZaloPay?.data?.error_message
          })
        );
      }
    } else {
      let callbackLink = '';
      if (this.isMobile && !this.inApp) {
        callbackLink = DOMAIN_WEB + (this.inApp ? PAGE.ZALOPAY_RESULT : PAGE.PAYMENT_RESULT);
      }
      const linkTransaction = await PaymentApi.linkZaloPayTransaction({
        callbackLink,
        package_id: this.selectedTerm?.id,
        promotion_code: this.promotionData?.promotionCode || this.selectedTerm?.giftCode || '',
        confirm: mobileCheck() || this.inApp ? 1 : 0,
        inApp: this.inApp,
        utm_source: affNetwork
      });
      const zaloPayTransaction = linkTransaction?.data;
      transactionData = zaloPayTransaction;
      this.dispatch(
        createTransactionSuccess({
          data: transactionData,
          notTrackAccessTrade
        })
      );
      if (linkTransaction?.success) {
        const deepLink = linkTransaction?.data?.deep_link;
        const bindingQrLink = linkTransaction?.data?.binding_qr_link;
        if (deepLink && this.isMobile) {
          const zlOrderId = linkTransaction?.data?.txnID;
          if (this.inApp) {
            let deepLink = zaloPayTransaction?.deep_link;
            deepLink = deepLink ? `${deepLink}&exit=1` : deepLink;
            if (deepLink && typeof window.ZaloPay !== 'undefined') {
              window.ZaloPay.launchDeeplink(deepLink, () => {
                createTimeout(() => {
                  this.gotoResultPageZaloPay(zaloPayTransaction);
                }, 3000);
              });
            } else {
              this.dispatch(setToast({ message: TEXT.MSG_ERROR }));
            }
          } else if (zlOrderId) {
            ConfigLocalStorage.set(LocalStorage.ZL_ORDER_ID, zlOrderId);
            setTimeout(() => {
              window.location.href = bindingQrLink;
            }, 1500);
          }
        } else {
          this.dispatch(
            openPopup({
              name: POPUP.NAME.QR_ZALO_PAY,
              qrCodeImg: linkTransaction?.data?.qrCodeImg,
              orderId: linkTransaction?.data?.orderId,
              qrDisplayTime: linkTransaction?.data?.qrDisplayTime,
              selectedPackage: this.selectedPackage,
              selectedTerm: this.selectedTerm,
              cancelTransaction: this.cancelTransaction,
              countDoneAction: () => this.countDoneAction(transactionData),
              totalAmount: this.totalAmount,
              zaloPayTransaction: transactionData,
              handle5Sec: this.handleLinkZaloPayTransaction
            })
          );
        }
      }
    }
    return transactionData;
  };

  handleNonRecurring = async () => {
    const isMobile = mobileCheck();

    this.dispatch(setLoading(true));
    const transactionZaloPay = await PaymentApi.createZaloPayTransaction(
      this.selectedTerm?.id,
      this.promotionData?.promotionCode || this.selectedTerm?.giftCode || '',
      this.inApp
    );
    this.dispatch(setLoading(false));

    const zaloPayTransaction = transactionZaloPay?.data;
    if (!transactionZaloPay?.success) {
      this.dispatch(
        setToast({
          message:
            zaloPayTransaction?.message || zaloPayTransaction?.error_message || TEXT.MSG_ERROR
        })
      );
    } else {
      this.dispatch(
        createTransactionSuccess({
          data: zaloPayTransaction,
          valueReferralCode: this.valueReferralCode
        })
      );
      const deepLink = zaloPayTransaction?.deep_link;
      if (deepLink && isMobile) {
        if (this.inApp && typeof window.ZaloPay !== 'undefined') {
          const appId = zaloPayTransaction?.appId;
          const zpTransToken = zaloPayTransaction?.zpTransToken;
          if (zpTransToken && appId) {
            window.ZaloPay.ready(() => {
              window.ZaloPay.payOrder(
                {
                  appid: appId,
                  zptranstoken: zpTransToken
                },
                () => {
                  createTimeout(() => {
                    this.gotoResultPageZaloPay(zaloPayTransaction);
                  }, 3000);
                }
              );
            });
          }
        } else if (deepLink) {
          setTimeout(() => {
            window.location.href = deepLink;
          }, 1500);
        }
      } else {
        this.dispatch(
          openPopup({
            name: POPUP.NAME.QR_ZALO_PAY,
            qrCodeImg: zaloPayTransaction?.qrCodeImg,
            orderId: zaloPayTransaction?.orderId,
            qrDisplayTime: zaloPayTransaction?.qrDisplayTime,
            zaloPayTransaction,
            selectedPackage: this.selectedPackage,
            selectedTerm: this.selectedTerm,
            cancelTransaction: this.cancelTransaction,
            countDoneAction: () => this.countDoneAction(zaloPayTransaction),
            totalAmount: this.totalAmount,
            handle5Sec: () => this.handleZaloPayTransaction(zaloPayTransaction)
          })
        );
      }
    }
    return zaloPayTransaction;
  };

  handleTVod = async () => {
    const isMobile = mobileCheck();
    if (!this.transactionData?.success) {
      this.dispatch(setToast({ message: this.transactionData?.message || TEXT.MSG_ERROR }));
    } else {
      const deepLink = this.transactionData?.deepLink;
      if (deepLink && isMobile) {
        if (this.inApp && typeof window.ZaloPay !== 'undefined') {
          const appId = this.transactionData?.appId;
          const zpTransToken = this.transactionData?.zpTransToken;
          if (zpTransToken && appId) {
            window.ZaloPay.ready(() => {
              window.ZaloPay.payOrder(
                {
                  appid: appId,
                  zptranstoken: zpTransToken
                },
                () => {
                  createTimeout(() => {
                    this.gotoResultPageZaloPay(this.transactionData);
                  }, 3000);
                }
              );
            });
          }
        } else if (deepLink) {
          setTimeout(() => {
            window.location.href = deepLink;
          }, 1500);
        }
      } else {
        this.dispatch(
          openPopup({
            name: POPUP.NAME.QR_ZALO_PAY,
            qrCodeImg: this.transactionData?.qrCodeImg,
            orderId: this.transactionData?.orderId,
            qrDisplayTime: this.transactionData?.qrDisplayTime,
            zaloPayTransaction: this.transactionData,
            selectedPackage: this.selectedPackage,
            selectedTerm: this.selectedTerm,
            cancelTransaction: this.cancelTransaction,
            countDoneAction: () => this.countDoneAction(this.transactionData),
            totalAmount: this.totalAmount,
            handle5Sec: () => this.handleZaloPayTransaction(this.transactionData)
          })
        );
      }
    }
    return this.transactionData;
  };

  handleLinkZaloPayTransaction = () => {
    PaymentApi.checkLinkZaloPayTransaction()?.then((res: any) => {
      const status = res?.data?.status;
      if (status === 1) {
        if (!this?.selectedTerm?.id) return;
        const affNetwork = ConfigCookie.load(ConfigCookie.KEY.AFF_NETWORK) || '';
        const affSId = ConfigCookie.load(ConfigCookie.KEY.AFF_SID) || '';
        this.cancelTransaction();
        this.dispatch(setLoading(true));
        PaymentApi.chargeZaloPay({
          packageId: this.selectedTerm?.id,
          inApp: this.inApp,
          promotionCode: this.promotionData?.promotionCode || this.selectedTerm?.giftCode || '',
          utmSource: affNetwork,
          trackingId: affSId
        })?.then((res: any) => {
          this.dispatch(setLoading(false));
          if (!res?.success) {
            if (res?.data?.message === ZALOPAY.MSG.NOT_ENOUGH_BALANCE) this.cancelTransaction();
            this.dispatch(setToast({ message: res?.data?.message || TEXT.MSG_ERROR }));
          } else {
            this.dispatch(
              createTransactionSuccess({
                data: res?.data,
                valueReferralCode: this.valueReferralCode
              })
            );
            this.gotoResultPageZaloPay(res?.data);
          }
        });
      }
    });
  };

  cancelTransaction = () => {
    this.dispatch(openPopup());
  };

  countDoneAction = (transaction: any) => {
    this.gotoResultPageZaloPay(transaction);
    this.dispatch(openPopup());
  };

  handleZaloPayTransaction = (transaction: any) => {
    if (this.isRentalContent) {
      PaymentApi.tvodCheckTransaction({ orderId: transaction?.orderId })?.then((res: any) => {
        const status = res?.status;
        if (status !== 0) this.gotoResultPageZaloPay(transaction);
      });
    } else if (this.isPvodContent) {
      PaymentApi.pvodCheckTransaction({ orderId: transaction?.orderId })?.then((res: any) => {
        const status = res?.status;
        if (status !== 0) this.gotoResultPageZaloPay(transaction);
      });
    } else {
      PaymentApi.checkZaloPayTransaction({ zlOrderId: transaction?.orderId })?.then((res: any) => {
        const status = res?.status;
        if (status !== 0) this.gotoResultPageZaloPay(res);
      });
    }
  };

  gotoResultPageZaloPay = (transaction: any) => {
    this.dispatch(setLoading(false));
    const zlOrderId =
      transaction?.txnID ||
      transaction?.txn_ref ||
      transaction?.orderId ||
      transaction?.txnRef ||
      '';
    let url = PAGE.PAYMENT_RESULT;
    if (this.inApp) url = PAGE.ZALOPAY_RESULT;
    url += `${this.queryString}&method=zalopay`;
    if (zlOrderId) url += `&zlOrderId=${zlOrderId}`;
    if (this.recurring) url += '&recurring=1';
    this.router.push(url);
  };
}

export default ZaloPayCheckout;

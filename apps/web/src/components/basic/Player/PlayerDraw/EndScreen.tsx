import React, { useEffect, useState } from 'react';
import { TEXT } from '@constants/text';
import { CONTENT_TYPE, CURRENCY, EL_ID, PAGE, TVOD } from '@constants/constants';
import { PLAYER_TYPE } from '@constants/player';
import { backFromPlayer } from '@functions/functions';
import { VALUE } from '@config/ConfigSegment';
import TrackingApp from '@tracking/functions/TrackingApp';
import { addParamToUrlVieON, createTimeout, numberWithCommas } from '@helpers/common';
import { useVieRouter } from '@customHook';
import PlayerPoster from '../../PlayerPoster/PlayerPoster';
import MiniPlayer from '../../MiniPlayer/MiniPlayer';
import ButtonEndScreenTrailer from './ButtonEndScreenTrailer';
import Button from '../../Buttons/Button';
import Image from '../../Image/Image';

declare const window: any;

let showStateTimer: any = null;
let unmounted = false;

const EndScreen = React.memo((props: any) => {
  const router = useVieRouter();
  const { endScreenData, isOuttro, currentEpisode, content, isOnEnded, contentDetail } = props;
  const { title, images, shortDescription, isPremiumTVod, type, tvod } = endScreenData || {};
  const [startTrailer, setStartTrailer] = useState(false);
  const [animated, setAnimate] = useState(false);
  const [showState, setShowState] = useState(true);

  const showEndScreen =
    showState &&
    (isOnEnded || isOuttro) &&
    !!(currentEpisode?.isEnd || content.type === CONTENT_TYPE.MOVIE) &&
    !!endScreenData?.id;

  useEffect(() => {
    if (window.playerContainerMouseMove) clearTimeout(window.playerContainerMouseMove);
    const playerContainer = document.getElementById(EL_ID.PLAYER_CONTAINER);
    if (playerContainer && playerContainer.style) playerContainer.style.cursor = 'initial';
    unmounted = false;
    return () => {
      unmounted = true;
      hidePlayerControl(false);
    };
  }, []);

  useEffect(() => {
    if (showEndScreen) {
      hidePlayerControl(true);
    }
  }, [showEndScreen]);

  useEffect(() => {
    if (showStateTimer) clearTimeout(showStateTimer);
    showStateTimer = createTimeout(() => {
      if (unmounted) return;
      if (
        (contentDetail?.id === currentEpisode?.id && currentEpisode?.isEnd) ||
        content?.type === CONTENT_TYPE.MOVIE
      ) {
        setShowState(true);
      }
    }, 200);

    return () => {
      clearTimeout(showStateTimer);
    };
  }, [endScreenData?.id]);

  const hidePlayerControl = (isHide: any) => {
    const playerContainer = document.getElementById(EL_ID.PLAYER_CONTAINER);
    if (playerContainer && playerContainer.style) playerContainer.style.cursor = 'initial';

    const playerControl = document.getElementById(EL_ID.PLAYER_CONTROL);
    if (playerControl && playerControl.style) {
      if (isHide) {
        playerControl.classList.add('hide');
      } else {
        playerControl.classList.remove('hide');
      }
    }
  };

  const onPlay = ({ player }: any) => {
    if (player) player.muted = false;
  };

  const onEnded = () => {
    addAnimation(false);
  };
  const onError = () => {
    addAnimation(false);
  };

  const onStartTrailer = () => {
    setStartTrailer(true);
    addAnimation(true);
  };

  const addAnimation = (isAdd: any) => {
    setAnimate(isAdd);
  };

  const onDetail = () => {
    backFromPlayer({ router, newContentId: endScreenData?.id, isIntro: true });
  };

  const onWatchNow = () => {
    TrackingApp.contentSelected({
      data: {
        ...endScreenData,
        ribbonId: VALUE.END_SCREEN_SUGGESTION.ID,
        ribbonName: VALUE.END_SCREEN_SUGGESTION.NAME,
        seasonThumb: endScreenData?.images?.thumbnail,
        seasonGenre: endScreenData?.genreText
      },
      clickType: VALUE.DIRECT
    });
    setStartTrailer(false);
    setShowState(false);
    hidePlayerControl(false);
    if (
      isPremiumTVod &&
      [TVOD.USER_TYPE.EXPIRED, TVOD.USER_TYPE.NONE].includes(tvod?.benefitType)
    ) {
      const queryParams = addParamToUrlVieON(router?.query, {
        type: endScreenData?.type,
        id: endScreenData?.id || 0
      });
      router.push(
        { pathname: PAGE.RENTAL_CONTENT, query: queryParams },
        { pathname: PAGE.RENTAL_CONTENT, query: queryParams }
      );
    } else {
      router.push(PAGE.VOD, endScreenData?.seo?.url);
    }
  };

  const trailerLink = endScreenData?.linkPlay?.hlsLinkPlay;

  const onBack = () => {
    backFromPlayer({ router, contentId: content?.id });
  };

  if (!showEndScreen) return null;

  let drawClass = 'player__drawer player__drawer--suggest absolute';

  if (animated) drawClass += ' playing-preview';

  let primaryButtonLabel = TEXT.WATCH_NOW;
  if (isPremiumTVod) {
    if (tvod?.benefitType === TVOD.USER_TYPE.WATCHED) {
      primaryButtonLabel = TEXT.CONTINUE_WATCH;
    }

    if ([TVOD.USER_TYPE.EXPIRED, TVOD.USER_TYPE.NONE].includes(tvod?.benefitType)) {
      primaryButtonLabel = TEXT.TVOD_NOW;
      if (type === CONTENT_TYPE.SEASON) primaryButtonLabel = TEXT.TVOD_FULL_EPS;
      primaryButtonLabel += ` ${numberWithCommas(endScreenData?.tvod?.price)} ${CURRENCY.VND}`;
    }
  }

  return (
    <div className={drawClass}>
      <div className="player__drawer-wrap">
        <Button
          className="player__controls-button player__controls-button--back shrink absolute top-left layer-999 !text-[22px]"
          iconName="vie-chevron-left-r-medium"
          onClick={onBack}
        />

        {(startTrailer || isOnEnded) && (
          <PlayerPoster
            poster={images?.thumbnailBig || images?.carousel || ''}
            effectStyle="animate-fade-in"
            // style={{ visibility: 'visible', opacity: 1 }}
            alt={title}
          />
        )}
        {trailerLink && startTrailer && (
          <MiniPlayer
            id={PLAYER_TYPE.END_SCREEN_TRAILER}
            linkPlay={trailerLink}
            contentId={endScreenData?.id}
            onPlay={onPlay}
            onEnded={onEnded}
            onError={onError}
            notPauseScrolling
            notCheckScroll
            // style={{ backgroundColor: 'black', visibility: 'visible', opacity: 1 }}
          />
        )}

        <div className="player__card player__card--suggest">
          <div className="player__card__wrap">
            <div className="player__card__thumbnail">
              <div className="player__card__thumbnail-loader overflow ratio-16-9">
                <Image
                  className={`player__card__thumbnail-img${
                    startTrailer || isOnEnded ? ' animate-fade-out' : ''
                  }`}
                  src={images?.thumbnailBig}
                />
              </div>
            </div>
            <div className="player__card__section">
              <div className="player__card__title-img">
                <Image
                  className="player__card__thumbnail-img animate-slide-in-left"
                  src={images?.titleCardLight}
                />
              </div>
              <div className="player__card__content">
                <h4 className="player__card__title line-clamp" data-line-clamp="1">
                  {title}
                </h4>
                <p className="player__card__desc line-clamp" data-line-clamp="3">
                  {shortDescription}
                </p>
              </div>

              <div className="player__button-group">
                <Button
                  className="player__button player__button--light player__button--play p-x3"
                  title={primaryButtonLabel}
                  onClick={onWatchNow}
                  iconName="vie-play-solid-rc"
                />

                <ButtonEndScreenTrailer
                  onStartTrailer={onStartTrailer}
                  onDetail={onDetail}
                  trailerLink={trailerLink}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

export default EndScreen;

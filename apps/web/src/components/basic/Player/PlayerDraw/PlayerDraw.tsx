import React, { useState } from 'react';
import { TEXT } from '@constants/text';
import { useSelector } from 'react-redux';
import { CONTENT_TYPE, PERMISSION } from '@constants/constants';
import { isIOS } from 'react-device-detect';
import isEmpty from 'lodash/isEmpty';
import EndScreen from '@components/basic/Player/EndScreen';
import ButtonNextEpisode from './ButtonNextEpisode';
import Button from '@components/basic/Buttons/Button';

const PlayerDraw = React.memo(
  ({
    isAdPlay,
    isOuttro,
    isOnEnded,
    isMobile,
    contentDetail,
    isFullscreen,
    showController,
    isCollapseVisible,
    setCollapseVisible,
    endScreenData,
    video,
    isShowEndScreenWithTrialContent
  }: any) => {
    const content = useSelector((state: any) => state?.Detail?.GET_CONTENT);
    const nextEpisode = useSelector((state: any) => state?.Episode?.nextEpisode);
    const currentEpisode = useSelector((state: any) => state?.Episode?.currentEpisode);
    const [viewCredits, setViewCredits] = useState(false);

    const isEndScreen =
      (currentEpisode?.isEnd && contentDetail?.id === currentEpisode?.id) ||
      (content?.type === CONTENT_TYPE.MOVIE && isShowEndScreenWithTrialContent) ||
      (content?.type === CONTENT_TYPE.MOVIE && isOnEnded) ||
      (currentEpisode?.id &&
        !currentEpisode?.isPremium &&
        contentDetail?.permission === PERMISSION.NON_LOGIN);

    const onViewCredits = () => {
      setViewCredits(true);
    };

    if (currentEpisode?.type === CONTENT_TYPE.TRAILER || isAdPlay) return null;

    return (
      <>
        <div className="player__button-group absolute bottom-right">
          {(isOuttro || isOnEnded) && (
            <>
              {!viewCredits &&
                !isOnEnded &&
                !currentEpisode?.isEnd &&
                content?.type !== CONTENT_TYPE.MOVIE && (
                  <Button
                    className="player__button large player__button--glass-dark player__button--view-credits"
                    title={TEXT.VIEW_CREDITS}
                    onClick={onViewCredits}
                  />
                )}
              {currentEpisode &&
                (nextEpisode || isIOS) &&
                (!viewCredits || isOnEnded) &&
                !currentEpisode?.isEnd && (
                  <ButtonNextEpisode nextEpisode={nextEpisode} viewCredits={viewCredits} />
                )}
            </>
          )}
        </div>
        {isEndScreen &&
          !isMobile &&
          !isEmpty(endScreenData) &&
          content?.type !== CONTENT_TYPE.TRAILER && (
            <EndScreen
              video={video}
              isShowController={showController}
              isFullscreen={isFullscreen}
              isEndScreenVod
              content={content}
              endScreenData={endScreenData}
              isCollapseVisible={isCollapseVisible}
              setCollapseVisible={setCollapseVisible}
              isOnEnded={isOnEnded}
              isOuttro={isOuttro}
              contentDetail={contentDetail}
              isShowEndScreenWithTrialContent={isShowEndScreenWithTrialContent}
            />
          )}
      </>
    );
  }
);

export default PlayerDraw;

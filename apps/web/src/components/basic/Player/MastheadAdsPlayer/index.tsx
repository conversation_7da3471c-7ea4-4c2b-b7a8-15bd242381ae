import React, { useCallback, useEffect, useRef, useState, memo } from 'react';
import Hls from 'hls.js';
import classNames from 'classnames';
import Styles from './Styles.module.scss';

const HLS_CONFIG = {
  debug: false,
  maxBufferLength: 24,
  maxMaxBufferLength: 200,
  maxBufferSize: 20 * 1000 * 1000,
  maxLoadingDelay: 0,
  manifestLoadingTimeOut: 10000,
  manifestLoadingMaxRetry: 3,
  manifestLoadingRetryDelay: 1000,
  manifestLoadingMaxRetryTimeout: 64000,
  levelLoadingTimeOut: 10000,
  levelLoadingMaxRetry: 3,
  levelLoadingRetryDelay: 1000,
  levelLoadingMaxRetryTimeout: 64000,
  nudgeMaxRetry: 6
};

const INITIAL_DELAY = 0;

const useVideoPlayer = (videoSrc: any, setAdsDuration: any) => {
  const { url, type, vastTracker } = videoSrc;
  const player = useRef<any>(null);
  const playerHls = useRef<any>(null);
  const playerHasStart = useRef(false);
  const firstQuartileTracked = useRef(false);
  const midpointTracked = useRef(false);
  const thirdQuartileTracked = useRef(false);
  const trackingEventsSet = useRef(false);
  const startTracking = useRef(false);
  const [viewCountDown, setViewCountDown] = useState<any>();

  useEffect(() => {
    startTracking.current = false;
    playerHasStart.current = false;
    firstQuartileTracked.current = false;
    midpointTracked.current = false;
    thirdQuartileTracked.current = false;
  }, [videoSrc]);

  const initMp4Video = useCallback((url: any) => {
    if (!player || !player.current) return;
    player.current.src = url;
  }, []);

  const initHLSVideo = useCallback((url: any) => {
    playerHls.current = new Hls(HLS_CONFIG);
    playerHls.current.attachMedia(player.current);
    playerHls.current.on(Hls.Events.MEDIA_ATTACHED, () => {
      playerHls.current.loadSource(url);
      playerHls.current.on(Hls.Events.ERROR, (event: any, data: any) => {
        if (data.fatal) {
          console.error('HLS video initialization error');
        }
      });
    });
  }, []);

  const initVideo = useCallback(() => {
    if (url !== '' && Hls.isSupported()) {
      if (type === 'video/mp4') {
        initMp4Video(url);
      } else {
        initHLSVideo(url);
      }
    }
  }, [url, type, initMp4Video, initHLSVideo]);

  useEffect(() => {
    const timer = setTimeout(() => {
      initVideo();
    }, INITIAL_DELAY);

    return () => {
      clearTimeout(timer);
    };
  }, [initVideo]);

  useEffect(() => {
    if (player?.current) {
      player.current.disablePictureInPicture = true;
      player.current.muted = true;
    }
  }, []);

  useEffect(() => {
    if (playerHasStart.current) {
      if (!startTracking.current) {
        vastTracker?.track('start');
        startTracking.current = true;
      }
    }
  }, [playerHasStart.current]);

  const handleOnTimeUpdate = useCallback(() => {
    if (!player?.current) return;

    const { duration, currentTime } = player.current;
    if (!duration || Number.isNaN(duration)) return;

    if (currentTime && vastTracker) {
      vastTracker.setProgress(currentTime);
    }

    if (!playerHasStart.current) {
      if (!Number.isNaN(duration)) {
        playerHasStart.current = true;
        setAdsDuration(parseInt(duration.toString(), 10));
      }
    } else if (!Number.isNaN(duration)) {
      const countDownTime = duration - currentTime;
      if (countDownTime >= 0) {
        setAdsDuration(parseInt(countDownTime.toString(), 10));
      }
      const newCountDownTime: any = `0:${
        countDownTime > 10
          ? parseInt(countDownTime.toString(), 10)
          : `0${parseInt(countDownTime.toString(), 10)}`
      }`;
      setViewCountDown(newCountDownTime);
    }

    if (duration && currentTime) {
      const percentage = (currentTime / duration) * 100;

      if (percentage >= 25 && !firstQuartileTracked.current) {
        firstQuartileTracked.current = true;
        vastTracker?.track('firstQuartile');
      }

      if (percentage >= 50 && !midpointTracked.current) {
        midpointTracked.current = true;
        vastTracker?.track('midpoint');
      }

      if (percentage >= 75 && !thirdQuartileTracked.current) {
        thirdQuartileTracked.current = true;
        vastTracker?.track('thirdQuartile');
      }
    }
  }, [setAdsDuration, vastTracker]);

  const listTracking = vastTracker?.creative?.Linear?.TrackingEvents?.Tracking || [];

  const trackEvent = (eventName: any) => {
    const eventUrls = listTracking
      .filter((event: any) => event._attributes?.event === eventName)
      .map((event: any) => event._cdata);

    return eventUrls.forEach((url: any) => {
      fetch(url)
        .then(() => {
          if (eventName === 'complete' || eventName === 'skip') {
            playerHasStart.current = false;
            player.current = null;
            if (Hls.isSupported() && playerHls.current) {
              playerHls.current.on(Hls.Events.MEDIA_DETACHED, () => {});
              playerHls.current.detachMedia();
              playerHls.current.destroy();
              playerHls.current = undefined;
            }
            firstQuartileTracked.current = false;
            midpointTracked.current = false;
            thirdQuartileTracked.current = false;
            startTracking.current = false;
          }
          vastTracker.removeAllListeners(eventName).off(eventName, () => {});
        })
        .catch((error) => console.error(`Failed to track ${eventName} URL: ${url}`, error));
    });
  };

  useEffect(() => {
    if (!vastTracker || trackingEventsSet.current) return;

    try {
      vastTracker.removeAllListeners('start').on('start', () => trackEvent('start'));
      vastTracker.removeAllListeners('complete').on('complete', () => trackEvent('complete'));
      vastTracker
        .removeAllListeners('firstQuartile')
        .on('firstQuartile', () => trackEvent('firstQuartile'));
      vastTracker.removeAllListeners('midpoint').on('midpoint', () => trackEvent('midpoint'));
      vastTracker
        .removeAllListeners('thirdQuartile')
        .on('thirdQuartile', () => trackEvent('thirdQuartile'));
      vastTracker.removeAllListeners('skip').on('skip', () => trackEvent('skip'));
      trackingEventsSet.current = true;
    } catch (error) {
      console.error('Error setting up tracking events:', error);
    }
  }, [vastTracker]);

  return { player, handleOnTimeUpdate, viewCountDown };
};

const MastheadAdsPlayer = memo(
  ({ videoSrc, setAdsDuration, countdownClass, statusSkip, handleSkip }: any) => {
    const { player, handleOnTimeUpdate, viewCountDown } = useVideoPlayer(videoSrc, setAdsDuration);
    const { vastTracker } = videoSrc;

    useEffect(() => {
      if (statusSkip) {
        vastTracker?.track('skip');
        player.current = null;
        handleSkip(false);
      }
    }, [statusSkip]);

    return (
      <div className="relative">
        <video
          ref={player}
          autoPlay
          muted
          id="playerMastheadADS"
          height="100%"
          width="100%"
          onTimeUpdate={handleOnTimeUpdate}
        >
          <source src={videoSrc?.url} type={videoSrc?.type} />
        </video>
        {viewCountDown && (
          <div className={classNames(Styles.PlayerCountDown, countdownClass)}>{viewCountDown}</div>
        )}
      </div>
    );
  }
);

MastheadAdsPlayer.displayName = 'MastheadAdsPlayer';

export default MastheadAdsPlayer;

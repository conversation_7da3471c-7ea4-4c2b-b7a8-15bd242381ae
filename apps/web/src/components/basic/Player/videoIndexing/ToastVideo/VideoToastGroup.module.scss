// Modules
@use 'sass:math';

/* =============== function caculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);

  //
  @return #{$remSize}rem;
}

/* =============== style =============== */

.animation-toast {
  left: -100%;
  animation: slide 0.75s forwards;
}
.safe-toast {
  width: max-content;
  padding-left: rem(40);
  bottom: rem(108);
}

.background-icon-eye {
  border-radius: 50%;
  width: rem(50);
  height: rem(50);
  .icon-eye {
    font-size: rem(30);
    top: rem(5);
    left: rem(5);
  }
}
.bg-toast-eye {
  background: rgba(17, 17, 17, 0.6);
  min-width: rem(74);
  height: rem(74);
  border-radius: rem(4) 0 0 rem(4);
  margin-right: rem(2);
}
.bg-toast-item {
  background: rgba(17, 17, 17, 0.6);
  cursor: pointer;
}

.slide {
  width: 0;
  opacity: 0;
  transition: all 0.65s ease;
  &.active {
    width: 100%;
    opacity: 1;
    max-height: rem(500);
    height: auto;
    transition: all 0.55s ease-in;
  }
}
.display-flex {
  display: flex;
  align-items: flex-end;
  width: 100%;
}
.transition {
  display: flex;
  align-items: flex-end;
  transition: all 0.45s ease;
}

.fade-in {
  opacity: 0;
  transition: opacity 0.35s ease-in-out;
}

@-webkit-keyframes slide {
  100% {
    left: 0;
  }
}

@keyframes slide {
  100% {
    left: 0;
  }
}

.title-eye {
  min-width: rem(306);
  background: #111111;
  padding: rem(16) rem(32);
  border: rem(1) solid #333333;
  border-radius: rem(2);
  margin-bottom: rem(12);
  font-size: rem(14);
  bottom: calc(100% - 4px);
}
.eyeCollapsed {
  transition: all 0.8s ease;
  transform: translateY(rem(45));
}
.groupCartBrand {
  height: rem(114);
  width: 100%;
  align-items: flex-end;
  display: flex;
}
.shoppingCart {
  height: rem(74);
  width: rem(74);
  background: rgba(17, 17, 17, 0.6);
  align-items: center;
  display: flex;
  position: relative;
}
.icon-red {
  right: rem(21);
  bottom: rem(43);
  z-index: 10;
  width: rem(12);
  height: rem(12);
  border-radius: 100%;
  background: linear-gradient(90deg, #ed0303 0, #ff6262 100%);
}

.borderNotBrand {
  border-radius: 0 rem(4) rem(4) 0;
}

.brand {
  display: flex;
  align-items: center;
  background: rgba(17, 17, 17, 0.6);
  border-radius: 0 rem(4) rem(4) 0;
  padding-right: rem(12);
  height: rem(74);
  transition: all 0.8s ease;
}
.brandCollapsed {
  border-radius: rem(4) rem(4) rem(4) 0;
  height: rem(114);
}
.groupImageText {
  display: flex;
  background: rgba(17, 17, 17, 0.6);
  border-radius: rem(4);
  animation: animation 0.75s ease;
}
.imageBrand {
  width: rem(50);
  height: rem(50);
  border-radius: rem(4);
  cursor: pointer;
  transition: all 0.8s ease;
}
.widthImgBrandCollapsed {
  width: rem(90);
  height: rem(90);
}
.text {
  color: white;
  font-size: rem(14);
  font-weight: 500;
  padding: 0 rem(12) 0 rem(12);
  display: flex;
  align-items: center;
  transition: all 0.8s ease;
}
.textBrandCollapse {
  font-size: rem(20) !important;
  transition: all 0.8s ease;
}
.iconBrandOther {
  display: flex;
  align-items: center;
  background: #333333;
  border-radius: 50%;
  width: rem(30);
  min-height: rem(30);
  cursor: pointer;
  justify-content: center;
  margin-left: rem(12);
}
.iconBrandOtherCollapse {
  width: rem(28) !important;
  min-height: rem(28) !important;
}
.pdNotCard {
  padding-left: rem(12);
}

@keyframes animation {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

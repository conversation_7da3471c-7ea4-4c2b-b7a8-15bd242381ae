import React, { useEffect, useState, useRef, useMemo } from 'react';
import PlayerTip from '@components/basic/Player/PlayerTip/index';
import { useSelector } from 'react-redux';
import isArray from 'lodash/isArray';
import { calcTopTagForHBO } from '@services/playerServices';
import { ON_OFF, PLAYER_TIP_STATUS } from '@constants/constants';
import ConfigSocket from '@config/ConfigSocket';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';
import { convertTimeRangeToSeconds } from '@/services/contentService';

const ContentTags = ({
  warningTag,
  warningMessage,
  currentTime,
  duration,
  isHBO,
  progress,
  warningLocation,
  isLiveStream,
  positionLiveCCU,
  warningMessageState,
  buttonLive,
  isFullscreen,
  showCCU,
  playerReady,
  showController,
  content,
  contentDetail,
  currentEpisode,
  isSeeking,
  isAdsEnd,
  hasAds,
  isAdError
}: any) => {
  const [collapsed, setCollapsed] = useState(
    progress > 0 || !warningMessage ? PLAYER_TIP_STATUS.INIT : PLAYER_TIP_STATUS.EXPAND
  );
  const [posHBO, setPosHBO] = useState<any>(null);
  const [currentWarningMessage, setCurrentWarningMessage] = useState(warningMessage);
  const [isShowingTimeSpecificWarning, setIsShowingTimeSpecificWarning] = useState(false);
  const [activeWarningId, setActiveWarningId] = useState<any>(null); // Track current active warning

  const defaultWarningTimeoutRef = useRef<any>(null);
  const timeSpecificWarningTimeoutRef = useRef<any>(null);
  const liveStreamIntervalRef = useRef<any>(null);
  const oldTime = useRef<any>(null);
  const contentConfig = useSelector((state: any) => state?.AppConfig?.content) || {};
  const marksPercent = contentConfig?.warningMessage?.marksPercent || [];
  const thirtyMins = 30;
  const warningContentMessages = contentDetail?.warningContentMessage || [];

  useEffect(() => {
    oldTime.current = 0;

    if (isHBO) {
      handlePosHBO();
      window.addEventListener('resize', handlePosHBO);

      return () => {
        window.removeEventListener('resize', handlePosHBO);
      };
    }

    return () => {
      if (defaultWarningTimeoutRef.current) {
        clearTimeout(defaultWarningTimeoutRef.current);
      }
      if (timeSpecificWarningTimeoutRef.current) {
        clearTimeout(timeSpecificWarningTimeoutRef.current);
      }
      if (liveStreamIntervalRef.current) {
        clearInterval(liveStreamIntervalRef.current);
      }
    };
  }, [isHBO]);

  useEffect(() => {
    if (warningMessage && (!hasAds || isAdsEnd || isAdError)) {
      if (playerReady) {
        checkShowMessage();
      }
    }
  }, [currentTime, duration, warningMessage, isAdsEnd, playerReady, hasAds, isAdError]);

  // Handle default warning message display
  useEffect(() => {
    // Only apply the default collapse behavior if we're not showing a time-specific warning
    // and ads have ended
    if (warningMessage && !isShowingTimeSpecificWarning && (!hasAds || isAdsEnd || isAdError)) {
      if (defaultWarningTimeoutRef.current) {
        clearTimeout(defaultWarningTimeoutRef.current);
        defaultWarningTimeoutRef.current = null;
      }

      if (collapsed === PLAYER_TIP_STATUS.EXPAND) {
        defaultWarningTimeoutRef.current = setTimeout(() => {
          setCollapsed(PLAYER_TIP_STATUS.COLLAPSE);
          setCurrentWarningMessage('');
        }, 7000);
      } else if (collapsed !== PLAYER_TIP_STATUS.EXPAND && isLiveStream) {
        if (liveStreamIntervalRef.current) {
          clearInterval(liveStreamIntervalRef.current);
          liveStreamIntervalRef.current = null;
        }

        // Expand after 30 mins
        const expandTime = ConfigLocalStorage.get(LocalStorage.EXPAND_TIME_LIVE_EVENT_TAG)
          ? Number(ConfigLocalStorage.get(LocalStorage.EXPAND_TIME_LIVE_EVENT_TAG))
          : thirtyMins;
        liveStreamIntervalRef.current = setInterval(() => {
          setCollapsed(PLAYER_TIP_STATUS.EXPAND);
        }, expandTime * 60 * 1000);
      }
    }

    return () => {
      // Cleanup only the default warning timeout and livestream interval
      if (defaultWarningTimeoutRef.current) {
        clearTimeout(defaultWarningTimeoutRef.current);
        defaultWarningTimeoutRef.current = null;
      }
      if (liveStreamIntervalRef.current) {
        clearInterval(liveStreamIntervalRef.current);
        liveStreamIntervalRef.current = null;
      }
    };
  }, [
    collapsed,
    warningMessage,
    isShowingTimeSpecificWarning,
    isLiveStream,
    isAdsEnd,
    isAdError,
    hasAds
  ]);

  useEffect(() => {
    if (isHBO) {
      handlePosHBO();
    }
  }, [isHBO]);

  // Effect to check for warning content messages based on current time
  useEffect(() => {
    // Only process warnings if ads have ended
    if (hasAds && !isAdsEnd && !isAdError) return;

    if (isArray(warningContentMessages) && warningContentMessages.length > 0 && currentTime) {
      // If user has seeked, we should check for new warnings
      if (isSeeking || !isShowingTimeSpecificWarning) {
        const matchingWarnings = warningContentMessages.filter((warning) => {
          if (warning.time_range && warning.time_range.length > 0) {
            for (let i = 0; i < warning.time_range.length; i++) {
              const startTimeStr = warning.time_range[i];
              const timecheck = convertTimeRangeToSeconds(startTimeStr);
              // Check if we're at or just passed a time point (within 0.5 seconds)
              if (Math.abs(currentTime - timecheck) <= 0.5) {
                return true;
              }
            }
          }
          return false;
        });

        if (matchingWarnings.length > 0) {
          // If we have an active warning and user hasn't seeked, don't override it
          if (isShowingTimeSpecificWarning && !isSeeking && activeWarningId !== null) {
            return;
          }

          const currentTimeInSeconds = currentTime;
          let closestWarning = null;
          let minTimeDifference = Infinity;

          matchingWarnings.forEach((warning) => {
            if (warning.time_range && warning.time_range.length > 0) {
              for (let i = 0; i < warning.time_range.length; i++) {
                const timeRangeStr = warning.time_range[i];
                const timeInSeconds = convertTimeRangeToSeconds(timeRangeStr);
                const timeDifference = Math.abs(currentTimeInSeconds - timeInSeconds);

                if (timeDifference < minTimeDifference) {
                  minTimeDifference = timeDifference;
                  closestWarning = warning;
                }
              }
            }
          });

          const selectedWarning = closestWarning || matchingWarnings[0];
          const warningId = `${selectedWarning.message}_${currentTime}`;

          if (timeSpecificWarningTimeoutRef.current) {
            clearTimeout(timeSpecificWarningTimeoutRef.current);
            timeSpecificWarningTimeoutRef.current = null;
          }

          setCurrentWarningMessage(selectedWarning.message);
          setCollapsed(PLAYER_TIP_STATUS.EXPAND);
          setIsShowingTimeSpecificWarning(true);
          setActiveWarningId(warningId);

          //If no time config, set default 10s by PO defined
          const displayTimeMs = (selectedWarning.time || 10) * 1000;
          timeSpecificWarningTimeoutRef.current = setTimeout(() => {
            setCollapsed(PLAYER_TIP_STATUS.COLLAPSE);
            setIsShowingTimeSpecificWarning(false); //reset to default message
            setCurrentWarningMessage('');
            setActiveWarningId(null);
            timeSpecificWarningTimeoutRef.current = null;
          }, displayTimeMs);
        } else if (isSeeking && isShowingTimeSpecificWarning) {
          // If user has seeked to a point with no warning and a warning is currently showing,
          // clear the timeout and hide the warning
          if (timeSpecificWarningTimeoutRef.current) {
            clearTimeout(timeSpecificWarningTimeoutRef.current);
            timeSpecificWarningTimeoutRef.current = null;
          }
          setCollapsed(PLAYER_TIP_STATUS.COLLAPSE);
          setCurrentWarningMessage('');
          setIsShowingTimeSpecificWarning(false);
          setActiveWarningId(null);
        }
      }
    }
  }, [currentTime, warningContentMessages, warningMessage, isSeeking, isAdsEnd, hasAds, isAdError]);

  const renderPosition = useMemo(() => {
    if (isHBO) {
      return posHBO;
    }
    if (isLiveStream) {
      if (
        (showCCU === ON_OFF.ON && positionLiveCCU === ConfigSocket.POSITION_LIVE_STREAM.TOP_LEFT) ||
        (buttonLive === ON_OFF.ON && positionLiveCCU === ConfigSocket.POSITION_LIVE_STREAM.TOP_LEFT)
      ) {
        const topValue = isFullscreen ? '62px' : '50px';
        return { top: topValue };
      }
    }

    return {};
  }, [isHBO, posHBO, positionLiveCCU, isLiveStream, isFullscreen]);

  const handlePosHBO = () => {
    setPosHBO(calcTopTagForHBO({ warningLocation }));
  };

  const checkShowMessage = () => {
    if (
      !isLiveStream &&
      collapsed !== PLAYER_TIP_STATUS.EXPAND &&
      isArray(marksPercent) &&
      !isShowingTimeSpecificWarning &&
      (isAdsEnd || !isAdError || !hasAds)
    ) {
      if (oldTime.current !== parseInt(currentTime)) {
        let shouldShowWarning = false;
        marksPercent.forEach((per) => {
          const setupTime: any = (per / 100) * duration;
          if (parseInt(setupTime) === parseInt(currentTime)) {
            shouldShowWarning = true;
          }
        });
        if (shouldShowWarning) {
          const hasTimeSpecificWarning =
            isArray(warningContentMessages) &&
            warningContentMessages.some((warning) => {
              if (warning.time_range && warning.time_range.length > 0) {
                return warning.time_range.some((timeRange: any) => {
                  const timecheck = convertTimeRangeToSeconds(timeRange);
                  return Math.abs(currentTime - timecheck) <= 0.5;
                });
              }
              return false;
            });

          if (!hasTimeSpecificWarning) {
            setCurrentWarningMessage(warningMessage);
            setCollapsed(PLAYER_TIP_STATUS.EXPAND);
          }
        }

        oldTime.current = parseInt(currentTime);
      }
    }
  };

  return (
    <PlayerTip
      posHBO={renderPosition}
      status={collapsed}
      tipType="Danger"
      tipAnimated="Animated"
      tipLabel={warningTag}
      tipDesc={
        isLiveStream ? (warningMessageState ? currentWarningMessage : null) : currentWarningMessage
      }
      isLiveStream={isLiveStream}
      positionLiveCCU={positionLiveCCU}
      playerReady={playerReady}
      showController={showController}
      content={content}
      contentDetail={contentDetail}
      currentEpisode={currentEpisode}
    />
  );
};

export default React.memo(ContentTags);

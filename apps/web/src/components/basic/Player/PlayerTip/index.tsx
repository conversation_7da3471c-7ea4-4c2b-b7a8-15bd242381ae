import React from 'react';
import classNames from 'classnames/bind';
import AgeTag from '@components/basic/Tags/AgeTag';
import { PERMISSION, PLAYER_TIP_STATUS } from '@constants/constants';
import NotifyPVod from '@components/basic/Player/PlayerControl/ControlTop/NotifyPVod';
import style from './PlayerTip.module.scss';

const cx = classNames.bind(style);

const PlayerTip = ({
  tipLabel,
  tipDesc,
  tipType,
  status,
  horizontal,
  posHBO,
  playerReady,
  showController,
  contentDetail,
  currentEpisode
}: any) => {
  const wrapLess = status === PLAYER_TIP_STATUS.COLLAPSE;
  const tipStyles = cx({
    c: true,
    cSpace: true,
    cHorizontal: horizontal
  });
  const tipWrap = cx({
    Wrap: true,
    [style[`${tipType}`] as any]: tipType, // When is set tipType: Danger | Warning | Info this will apply style color.
    WrapSizeMin: wrapLess, // is enabled when wrapLess: true[default] | false.
    WrapSizeMax: !wrapLess // is enabled when wrapLess: true[default] | false.
  });
  const tipStronger = cx({
    Stronger: true
  });
  const tipPara = cx({
    Desc: true,
    fadeIn: !wrapLess, // Add animation fadeOUt (when wrap set wrapLess: true).
    fadeOut: wrapLess // Add animation fadeOUt (when wrap set wrapLess: true).
  });
  return (
    <div className={tipStyles} style={posHBO}>
      {status === PLAYER_TIP_STATUS.INIT ? (
        tipLabel ? (
          <AgeTag text={tipLabel} style={{ width: '47px' }} />
        ) : (
          ''
        )
      ) : tipLabel || tipDesc ? (
        <div className={tipWrap}>
          {tipLabel && <strong className={tipStronger}>{tipLabel}</strong>}
          {tipDesc && <p className={tipPara}>{tipDesc}</p>}
        </div>
      ) : (
        ''
      )}
      {currentEpisode?.hasPVOD && contentDetail?.permission === PERMISSION.CAN_WATCH && (
        <NotifyPVod
          playerReady={playerReady}
          showController={showController}
          contentDetail={contentDetail}
        />
      )}
    </div>
  );
};

export default React.memo(PlayerTip);

import React from 'react';
import { TEXT } from '@constants/text';
import { useDispatch, useSelector } from 'react-redux';
import { ACTION_TYPE, createAction } from '@actions/actionType';
import { PERMISSION } from '@constants/constants';
import UserApi from '@apis/userApi';
import styles from '@components/basic/Player/PlayerControl/ControlBottom/ControlBottom.module.scss';
import { SETTING_KEY, SettingMenu } from './SettingMenu/SettingMenu';
import Button from '../../../Buttons/Button';

const SubAndAudio = React.memo(
  ({ onSetAudio, onSetSubtitle, subtitle, isMobile, onHandleOffAds }: any) => {
    const dispatch = useDispatch();
    const audios = useSelector((state: any) => state?.Player?.audios);
    const subtitles = useSelector((state: any) => state?.Player?.subtitles);
    const audio = useSelector((state: any) => state?.Player?.audio);

    const onClickSubtitle = ({ item }: any) => {
      if (item?.id === 'NONE') return;
      if (typeof onHandleOffAds === 'function') onHandleOffAds(true);
      if (item?.permission === PERMISSION.CAN_WATCH) {
        UserApi.personalAudioSubTitle({
          subtitle_code_name: item?.id === 'OFF' ? 'off' : item?.codeName
        });
      }
      if (typeof onSetSubtitle === 'function') onSetSubtitle(item);
    };

    const onClickAudio = ({ item }: any) => {
      if (item?.id === 'NONE') return;
      if (typeof onHandleOffAds === 'function') onHandleOffAds(true);
      if (item?.permission === PERMISSION.CAN_WATCH) {
        UserApi.personalAudioSubTitle({
          audio_code_name: item?.codeName
        });
        dispatch(createAction(ACTION_TYPE.SET_SETTING_SUCCESS, { audio: item }));
      }
      if (typeof onSetAudio === 'function') onSetAudio(item);
    };

    const onHandleClick = () => {
      if (typeof onHandleOffAds === 'function') onHandleOffAds(true);
    };

    const defaultAudio = (audios || []).find((item: any) => item.isDefault);

    return (
      <div className="player__controls-bottom__item player-subtitle-audio">
        <Button
          className={`${styles.Button}`}
          iconName="vie-comment-o-alt-rc-medium"
          iconClass={styles.ButtonIconI}
          textClass={styles.ButtonText}
          title={TEXT.AUDIO_AND_SUBTITLE}
          onClick={onHandleClick}
        />
        <div className="player__controls-panel player__controls-panel__bottom controls-setting controls-setting-menu controls-setting-menu-propagation  md:!min-w-[20rem] animate-fade-in-up absolute">
          <SettingMenu
            title={TEXT.SUBTITLE}
            settingKey={SETTING_KEY.SUBTITLE}
            data={subtitles}
            activeItem={subtitle}
            onClickItem={onClickSubtitle}
            isMobile={isMobile}
          />
          <SettingMenu
            title={TEXT.AUDIO}
            settingKey={SETTING_KEY.AUDIO}
            data={audios}
            activeItem={audio || defaultAudio}
            onClickItem={onClickAudio}
            isMobile={isMobile}
          />
        </div>
      </div>
    );
  }
);

export default SubAndAudio;

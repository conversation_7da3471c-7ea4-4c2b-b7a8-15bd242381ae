import { TEXT } from '@constants/text';
import React from 'react';
import { useSelector } from 'react-redux';
import { PAGE } from '@constants/constants';
import { useVieRouter } from '@customHook';
import styles from '@components/basic/Player/PlayerControl/ControlBottom/ControlBottom.module.scss';
import Image from '../../../../Image/Image';
import Button from '../../../../Buttons/Button';

const NextEpisode = React.memo(() => {
  const router = useVieRouter();
  const nextEpisode = useSelector((state: any) => state?.Episode?.nextEpisode);
  if (!nextEpisode) return null;
  const onNextEpisode = () => {
    if (nextEpisode?.seo?.url) {
      router.push(PAGE.VOD, nextEpisode?.seo?.url);
    }
  };

  return (
    <div className="player__controls-bottom__item player-episode">
      <Button
        className={`${styles.Button}`}
        iconClass={styles.ButtonIconI}
        textClass={styles.ButtonText}
        iconName="vie-step-forward-s-rc"
        onClick={onNextEpisode}
        title={TEXT.NEXT_EPISODE}
      />
      <div className="player__controls-panel animate-fade-in-up absolute absolute right">
        <div className="player__controls-panel__wrap">
          <div className="player__controls-panel__header">
            <span className="text-view content-left">{TEXT.NEXT_EPISODE}</span>
          </div>
          <div className="player__controls-panel__body" onClick={onNextEpisode}>
            <div className="player__card player__card--episode player__card--episode-next">
              <div className="player__card__wrap">
                <div className="player__card__thumbnail">
                  <div className="player__card__thumbnail-loader overflow ratio-16-9">
                    <Image
                      className="player__card__thumbnail-img"
                      src={nextEpisode?.images?.thumbnail || ''}
                      alt={nextEpisode?.title}
                    />
                  </div>
                  <Button
                    className="player__button player__button--play"
                    iconName="vie-play-solid-rc"
                  />
                </div>
                <div className="player__card__section">
                  {nextEpisode?.title && (
                    <p className="player__card__title line-clamp" data-line-clamp="3">
                      {nextEpisode?.title}
                    </p>
                  )}
                  {nextEpisode?.remainText && (
                    <span className="player__card__duration">{nextEpisode?.remainText}</span>
                  )}
                  {nextEpisode?.shortDescription && (
                    <p className="player__card__desc line-clamp" data-line-clamp="3">
                      {nextEpisode?.shortDescription}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

export default NextEpisode;

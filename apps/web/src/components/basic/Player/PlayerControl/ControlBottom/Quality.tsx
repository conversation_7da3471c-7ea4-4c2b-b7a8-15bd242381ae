import React, { useMemo, useRef, useState } from 'react';
import { TEXT } from '@constants/text';
import { useDispatch, useSelector } from 'react-redux';
import { ACTION_TYPE, createAction } from '@actions/actionType';
import { PERMISSION, VIDEO_QUALITY } from '@constants/constants';
import styles from '@components/basic/Player/PlayerControl/ControlBottom/ControlBottom.module.scss';
import classNames from 'classnames';
import { useOnClickOutside } from '@customHook';
import { SETTING_KEY, SettingMenu } from './SettingMenu/SettingMenu';
import Button from '../../../Buttons/Button';

const Quality = ({ onClickItem, isMobile, onHandleOffAds }: any) => {
  const dispatch = useDispatch();
  const refEL = useRef<any>(null);
  const data = useSelector((state: any) => state?.Player?.qualities);
  const quality = useSelector((state: any) => state?.Player?.quality);
  const [isShowList, setIsShowList] = useState(false);

  if ((data?.length || 0) <= 0) return null;
  const onClickQuality = ({ item }: any) => {
    const { permission } = item || {};
    if (typeof onHandleOffAds === 'function') onHandleOffAds(true);
    if (permission === PERMISSION.CAN_WATCH) {
      dispatch(createAction(ACTION_TYPE.SET_SETTING_SUCCESS, { quality: item }));
    }
    if (typeof onClickItem === 'function') onClickItem(item);
  };
  if (isMobile) useOnClickOutside(refEL, () => setIsShowList(false));
  const onHandleClickButton = () => {
    if (typeof onHandleOffAds === 'function') onHandleOffAds(true);
    if (isMobile) setIsShowList(true);
  };

  const autoQuality = useMemo(
    () => (data || []).find((item: any) => item?.id === VIDEO_QUALITY.AUTO),
    [quality?.id]
  );

  return (
    <div
      ref={refEL}
      className={classNames('player__controls-bottom__item player-quality', isMobile && 'p-l2')}
    >
      <Button
        className={`${styles.Button}`}
        iconName="vie-cog-solid"
        iconClass={styles.ButtonIconI}
        textClass={styles.ButtonText}
        tagData={{
          className: `tags ${styles.ButtonTagQuality}`,
          label: (quality || autoQuality)?.name
        }}
        title={TEXT.QUALITY}
        onClick={onHandleClickButton}
      />
      {((isMobile && isShowList) || !isMobile) && (
        <div className="player__controls-panel player__controls-panel__bottom controls-setting controls-setting-menu controls-setting-menu-propagation animate-fade-in-up absolute">
          <SettingMenu
            settingKey={SETTING_KEY.QUALITY}
            data={data}
            activeItem={quality || autoQuality}
            onClickItem={onClickQuality}
            isMobile={isMobile}
          />
        </div>
      )}
    </div>
  );
};

export default Quality;

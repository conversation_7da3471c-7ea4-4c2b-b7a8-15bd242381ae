import React, { useEffect, useMemo, useRef, useState } from 'react';
import { TEXT } from '@constants/text';
import { useDispatch, useSelector } from 'react-redux';
import { ACTION_TYPE, createAction } from '@actions/actionType';
import { ICON_KEY, VIDEO_PLAYBACK_SPEED } from '@constants/constants';
import { useOnClickOutside } from '@customHook';
import styles from './ControlBottom.module.scss';
import { SETTING_KEY, SettingMenu } from './SettingMenu/SettingMenu';
import Button from '../../../Buttons/Button';

const PlaybackSpeed = ({ isMobile, video, playerReady, onHandleOffAds }: any) => {
  const dispatch = useDispatch();
  const refEL = useRef<any>(null);
  const playbackSpeed = useSelector((state: any) => state?.Player?.playbackSpeed || []);
  const playback = useSelector((state: any) => state?.Player?.playback);
  const [isShowList, setIsShowList] = useState(false);
  const onClickPlayBackSpeed = ({ item }: any) => {
    if (typeof onHandleOffAds === 'function') onHandleOffAds(true);
    dispatch(createAction(ACTION_TYPE.SET_SETTING_SUCCESS, { playback: item }));
    if (video) {
      video.playbackRate = item?.id;
    }
  };
  const defaultPlaybackSpeed = useMemo(
    () => (playbackSpeed || []).find((item: any) => item?.id === VIDEO_PLAYBACK_SPEED.DEFAULT),
    [playbackSpeed?.id]
  );
  if (isMobile) useOnClickOutside(refEL, () => setIsShowList(false));
  const onHandleClickButton = () => {
    if (typeof onHandleOffAds === 'function') onHandleOffAds(true);
    if (isMobile) setIsShowList(true);
  };

  useEffect(() => {
    if (playback?.id && playerReady) {
      video.playbackRate = playback?.id;
    }
  }, [playback?.id, playerReady]);

  // if (isHidePlaybackSpeed || !showController) return false;
  return (
    <div ref={refEL} className="player__controls-bottom__item player-quality">
      <Button
        className={`${styles.Button}`}
        iconClass={styles.ButtonIconI}
        textClass={styles.ButtonText}
        title={TEXT.PLAYBACK_SPEED}
        iconType={ICON_KEY.PLAYBACK_SPEED}
        onClick={onHandleClickButton}
        svgSize={isMobile ? '12' : '22'}
      />
      {((isMobile && isShowList) || !isMobile) && (
        <div className="player__controls-panel player__controls-panel__bottom controls-setting controls-setting-menu controls-setting-menu-propagation animate-fade-in-up absolute">
          <SettingMenu
            isPlaybackSpeed
            title={TEXT.PLAYBACK_SPEED}
            settingKey={SETTING_KEY.PLAYBACK_SPEED}
            data={playbackSpeed}
            activeItem={playback || defaultPlaybackSpeed}
            onClickItem={onClickPlayBackSpeed}
            isMobile={isMobile}
          />
        </div>
      )}
    </div>
  );
};

export default PlaybackSpeed;

import React from 'react';
import classNames from 'classnames';
import styles from '@components/basic/Player/PlayerControl/ControlBottom/ControlBottom.module.scss';
import SettingButton from '../../../../Buttons/SettingButton';

export const SETTING_KEY = {
  REPORT: 'report',
  SUBTITLE: 'subtitle',
  AUDIO: 'audio',
  QUALITY: 'quality',
  PLAYBACK_SPEED: 'playback_speed'
};

export const SettingMenu = React.memo(
  ({ title, data, activeItem, onClickItem, isPlaybackSpeed, isMobile }: any) => {
    if (!data?.length || data?.length === 0) return null;
    return (
      <div
        className={classNames(
          'controls-setting-container',
          isPlaybackSpeed && !isMobile && 'p-l3 p-r3'
        )}
      >
        {title && (
          <div className="controls-setting-header pt-1">
            <span className={`text-view ${styles.CustomClasstitle}`}>{title}</span>
          </div>
        )}
        <div className="controls-setting-body flex flex-col">
          {(data || []).map((item: any, i: any) => (
            <SettingButton
              key={i}
              {...item}
              active={activeItem?.id ? activeItem?.id === item.id : item?.default}
              onClick={() => onClickItem({ item })}
              isMobile={isMobile}
            />
          ))}
        </div>
      </div>
    );
  }
);

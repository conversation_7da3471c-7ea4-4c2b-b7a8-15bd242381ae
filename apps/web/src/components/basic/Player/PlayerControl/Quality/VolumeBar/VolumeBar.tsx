import React from 'react';
import styles from '@components/basic/Player/PlayerControl/ControlBottom/ControlBottom.module.scss';
import CustomProgress from '../../../../CustomProgress/CustomProgress';
import Button from '../../../../Buttons/Button';
import { TEXT } from '@constants/text';

const VolumeBar = React.memo((props: any) => {
  const { volumeValue, onVoluming, isMuted, onClickVolume } = props || {};
  let volumeIcon = 'vie-volume-up-rc';
  let volumeTip = TEXT.VOLUME;
  if (volumeValue === 0 || isMuted) volumeIcon = 'vie-volume-mute-rc';
  else if (volumeValue < 60) volumeIcon = 'vie-volume-rc';
  if (volumeValue === 0 || isMuted) volumeTip = TEXT.VOLUME_UN_MUTE;
  else if (volumeValue < 60) volumeTip = TEXT.VOLUME_MUTE;
  return (
    <div className="player__controls-bottom__item player-volume vertical shrink">
      <Button
        className={`${styles.Button}`}
        iconClass={styles.ButtonIconI}
        textClass={styles.ButtonText}
        iconName={volumeIcon}
        onClick={onClickVolume || (() => {})}
        subTitle={volumeTip}
      />
      <CustomProgress
        className="progress-bar player-volume__bar"
        inputId="inputVolumeBar"
        progressId="progressBarVolume"
        value={volumeValue}
        onControlSeeking={onVoluming}
        isMuted={isMuted}
      />
    </div>
  );
});

export default VolumeBar;

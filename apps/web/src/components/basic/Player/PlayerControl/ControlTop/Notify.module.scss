.notify-warning {
  &::before {
    content: '';
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    position: absolute;
    background-color: #e74c3c;
  }
}

.spanPointer {
  span {
    cursor: pointer;
  }
}

.triggerTrialDuration {
  @apply w-fit max-w-[calc(100%-1.5rem)] md:max-w-[calc(100%-4.16667vw)] transition-all;
  @apply transition-all z-10 px-3 py-2 md:px-4 md:py-3 bg-black/50;

  &WithController {
    @apply top-11 lg:top-[5.5rem];
  }
}

.triggerTrialCountdown {
  @apply cursor-pointer;

  &Text {
    @apply text-white text-xs lg:text-base;
  }
}

import React, { useEffect, useMemo, useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import get from 'lodash/get';
import { CONTENT_TYPE } from '@constants/constants';
import { openAppMobile } from '@helpers/common';
import TrackingMWebToApp from '@tracking/functions/TrackingMWebToApp';
import { usePorTrait } from '@customHook';
import { handleTrackingMWebToApp } from '@services/trackingServices';

const NotifyMWebtoApp = ({ showController, contentDetail, playerReady, content }: any) => {
  const timeOffInfoBox = useRef<any>(null);
  const config = useSelector((state: any) => state?.App?.webConfig);
  const { appDownload } = useSelector((state: any) => state?.App?.webConfig) || {};
  const stringInfoBox = get(config, 'mwebToApp.infoBox.description', '');
  const { trialDuration, numberTrialEpisode } = contentDetail || {};
  const { type } = content || {};
  const [isShow, setShow] = useState(false);
  const parseMessage = useMemo(() => {
    let newSubstr = '';
    if (type === CONTENT_TYPE.MOVIE) newSubstr = `${trialDuration} phút`;
    if (type === CONTENT_TYPE.SEASON || type === CONTENT_TYPE.EPISODE) {
      newSubstr = `${numberTrialEpisode} tập`;
    }
    return stringInfoBox?.replace('{trial_value}', newSubstr);
  }, [trialDuration, numberTrialEpisode, type]);
  const { porTrait } = usePorTrait();
  const { eventNameInfoboxLoad, eventNameInfoboxTouch, flowName } = useMemo(
    () =>
      handleTrackingMWebToApp({
        data: contentDetail,
        porTrait,
        type
      }),
    [contentDetail, porTrait, type]
  );

  useEffect(() => {
    if (!porTrait && eventNameInfoboxLoad && flowName) {
      TrackingMWebToApp.movieMWebToAppInfoBox({ eventName: eventNameInfoboxLoad, flowName });
    }
  }, [porTrait, eventNameInfoboxLoad, flowName]);

  useEffect(
    () => () => {
      clearTimeout(timeOffInfoBox.current);
    },
    []
  );

  useEffect(() => {
    if (isShow && !showController) {
      setShow(false);
    }
  }, [showController, isShow]);

  useEffect(() => {
    if (contentDetail?.id && playerReady) {
      setShow(true);
      if (playerReady) {
        timeOffInfoBox.current = setTimeout(() => {
          setShow(false);
        }, 5000);
      }
    }
  }, [contentDetail?.id, playerReady]);

  const onHandleClick = () => {
    TrackingMWebToApp.movieMWebToAppInfoBox({ eventName: eventNameInfoboxTouch, flowName });
    if (timeOffInfoBox?.current) clearTimeout(timeOffInfoBox.current);
    timeOffInfoBox.current = setTimeout(() => {
      openAppMobile(appDownload);
    }, 200);
  };

  return (
    <div className={`player__controls-wrap${!showController ? ' margin-small-up-top-16' : ''}`}>
      <div className="banner banner--pm-conversion padding-x-medium-down-12 padding-y-medium-down-8 padding-x-medium-up-16 padding-y-medium-up-12 bg-dark-glass-50">
        <div className="banner__inner" onClick={onHandleClick}>
          <div className="banner__body">
            <div className="banner__content">
              <div
                className="text text-white text-large-up-16 text-12"
                dangerouslySetInnerHTML={{ __html: parseMessage || '' }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(NotifyMWebtoApp);

import React, { useEffect, useMemo, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { PAGE, TVOD } from '@constants/constants';
import { formatTimeTVodString, parseTimeExpiredTVod } from '@services/contentService';
import Tags from '@components/basic/Tags/Tags';
import { useVieRouter } from '@customHook';
import PaymentApi from '@/apis/Payment';
import { ACTION_TYPE, createAction } from '@/actions/actionType';

const NotifyTVod = ({ content, contentDetail, playerReady, isWarningScreen }: any) => {
  const timeOffInfoBox = useRef<any>(null);
  const router = useVieRouter();
  const config = useSelector((state: any) => state?.App?.webConfig);
  const expiredStringInfoBox = get(config, 'tVod.text.expiredStringInfoBox', '');
  const { tvod, tVodInfo } = content || {};
  const { bizInfo } = tVodInfo || {};
  const { strTimeInfoBox, benefitType } = tvod || {};
  const [isShow, setShow] = useState(false);
  const { query, pathname } = router || {};
  const [tvodInfo, setTvodInfo] = useState<any>({});
  const dispatch = useDispatch();

  const remainTimeText = useMemo(() => {
    let timeText = '';
    if (benefitType === TVOD.USER_TYPE.RENTED && !isEmpty(bizInfo)) {
      const consumingDurTime = bizInfo?.consumingDur * 60 + Math.floor(Date.now() / 1000);
      const timeExpired = parseTimeExpiredTVod({ expiredTime: consumingDurTime });
      if (timeExpired?.strTimeInfoBox) {
        timeText = formatTimeTVodString({
          strConfig: expiredStringInfoBox,
          strTime: timeExpired?.strTimeInfoBox
        });
      }
    } else if (strTimeInfoBox) {
      timeText = formatTimeTVodString({ strConfig: expiredStringInfoBox, strTime: strTimeInfoBox });
    }
    return timeText;
  }, [expiredStringInfoBox, strTimeInfoBox, bizInfo, benefitType]);

  const textTSvod = () => {
    let timeText = '';
    if (tvodInfo && !isEmpty(tvodInfo?.bizInfo) && !isEmpty(tvodInfo?.benefitInfo)) {
      const { type, endAt } = tvodInfo?.benefitInfo;
      const consumingDurTime =
        type === TVOD.USER_TYPE.WATCHED
          ? endAt
          : tvodInfo?.bizInfo?.consumingDur * 60 + Math.floor(Date.now() / 1000);
      const timeExpired = parseTimeExpiredTVod({ expiredTime: consumingDurTime });
      if (timeExpired?.strTimeInfoBox) {
        timeText = formatTimeTVodString({
          strConfig: expiredStringInfoBox,
          strTime: timeExpired?.strTimeInfoBox
        });
      }
    }
    return timeText;
  };
  useEffect(
    () => () => {
      clearTimeout(timeOffInfoBox.current);
    },
    []
  );

  useEffect(() => {
    const getTvodInfo = async () => {
      const tvodData = await PaymentApi.getTVodInfo({
        contentId: content?.id,
        contentType: content?.type
      });
      setTvodInfo(tvodData);
      dispatch(createAction(ACTION_TYPE.TVOD, tvodData));
    };
    getTvodInfo();
    return () => {
      setTvodInfo({});
      dispatch(createAction(ACTION_TYPE.TVOD, {}));
    };
  }, []);

  useEffect(() => {
    if (!isWarningScreen) {
      setShow(true);
      timeOffInfoBox.current = setTimeout(() => {
        setShow(false);
      }, 5000);
    }
  }, [isWarningScreen]);

  // useEffect(() => {
  //   if (isShow && !showController && benefitType !== TVOD.USER_TYPE.RENTED) {
  //     setShow(false);
  //   }
  // }, [showController, benefitType, isShow]);

  useEffect(() => {
    if (
      contentDetail?.id &&
      !isEmpty(tvod) &&
      (benefitType !== TVOD.USER_TYPE.RENTED ||
        (benefitType === TVOD.USER_TYPE.RENTED && playerReady))
    ) {
      setShow(true);
      if (benefitType === TVOD.USER_TYPE.RENTED && playerReady) {
        timeOffInfoBox.current = setTimeout(() => {
          setShow(false);
        }, 5000);
      }
    }
  }, [contentDetail?.id, tvod, playerReady]);

  if (pathname === PAGE.VOD && query?.rel) {
    return null;
  }

  if (
    !isShow ||
    (contentDetail.isPremiumTVod && !remainTimeText) ||
    (contentDetail.isSvodTvod && !textTSvod()) ||
    tvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.NONE
  ) {
    return null;
  }

  return (
    (tvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.RENTED ||
      tvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.WATCHED) &&
    (contentDetail.isSvodTvod || contentDetail.isPremiumTVod) && (
      <div className="player__controls-wrap">
        <div className="banner banner--pm-conversion padding-x-medium-down-12 padding-y-medium-down-8 padding-x-medium-up-16 padding-y-medium-up-12 bg-dark-glass-50">
          <div className="banner__inner">
            <div className="banner__body">
              <div className="banner__content">
                <Tags
                  description={
                    <span
                      dangerouslySetInnerHTML={{
                        __html: contentDetail?.isSvodTvod ? textTSvod() : remainTimeText
                      }}
                    />
                  }
                  txtClass="text-large-up-16 text-small-up-12 text-medium margin-medium-down-left-8 margin-medium-up-left-12"
                  // spClass="icon--small icon--tiny-xs"
                  iconName="vie-clock-o-rc-medium !text-[#EDC42D]"
                  isNewIcon
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  );
};

export default React.memo(NotifyTVod);

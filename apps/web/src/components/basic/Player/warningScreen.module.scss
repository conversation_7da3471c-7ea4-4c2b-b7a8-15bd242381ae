// Modules
@use 'sass:math';

/* =============== function caculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);

  //
  @return #{$remSize}rem;
}

/* =============== style =============== */

.warning {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  color: white;
  top: 0;
  left: 0;
  background: black;
  flex-direction: column;
  .title {
    font-weight: 700;
    color: #e74c3c;
    font-size: rem(40);
  }
  @media print, screen and (max-width: rem(600)) {
    .title {
      font-size: rem(24);
    }
  }
  &.screenLiveTv {
    @media print, screen and (min-width: 64em) {
      width: 62.23958vw;
    }
  }
  &.delay2500 {
    animation-delay: 4.5s;
  }
}

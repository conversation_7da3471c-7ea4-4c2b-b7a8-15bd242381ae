import React, { memo, useEffect, useMemo, useRef } from 'react';
import { isIOS, isMobile, isSafari } from 'react-device-detect';
import isElement from 'lodash/isElement';
import { KEY_CODE } from '@constants/constants';
import PreviewThumbnail from './PreviewThumbnail';

const CustomProgress = ({
  inputId,
  progressId,
  subProgressId,
  className,
  value,
  isMuted,
  step,
  style,
  noSeeker,
  hoverTimeText,
  thumbMetadata,
  thumbSrc,
  mouseLeft,
  isPreviewThumb,
  isFullscreen,
  isOnEnded,
  onControlSeeking,
  onControlSeekChange,
  onControlMouseMove,
  onControlMouseOver,
  onControlMouseOut,
  onHandleOffAds,
  isDVR
}: any) => {
  const hoverValue = useRef<any>(0);
  const stateMouseDown = useRef(false);
  const inputClass = useMemo(
    () => (noSeeker ? 'progress-bar__holder hide' : 'progress-bar__holder'),
    [noSeeker]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeydown, false);
    return () => {
      document.addEventListener('keydown', handleKeydown, false);
    };
  }, []);

  useEffect(() => {
    const input: any = document.getElementById(inputId || 'input');
    if (isElement(input)) {
      let eventsName = 'mousemove';
      if (isMobile) eventsName = 'touchmove';
      if (isSafari) {
        handleSafari(input);
      }
      input.addEventListener(eventsName, (e: any) => {
        const value = calcSliderPos(e);
        hoverValue.current = value;
        onMouseMove(e.target?.clientWidth);
        if (isSafari) {
          if (
            stateMouseDown.current === true &&
            (inputId === 'inputVolumeBar' || inputId === 'inputSeekBar')
          ) {
            onControlSeeking(+value);
          }
        }
      });
    }
  }, [inputId]);

  const handleSafari = (input: any) => {
    if (input && (inputId === 'inputVolumeBar' || inputId === 'inputSeekBar')) {
      input.addEventListener('mouseup', () => {
        stateMouseDown.current = false;
      });
      input.addEventListener('mousedown', (e: any) => {
        e.preventDefault();
        stateMouseDown.current = true;
        const value = calcSliderPos(e);
        onControlSeeking(+value);
      });
      input.addEventListener('mouseout', () => {
        stateMouseDown.current = false;
      });
    }
  };

  const onSeeking = (e: any) => {
    const value = e?.target?.value;
    if (isIOS) {
      if (typeof onControlSeekChange === 'function') onControlSeekChange(value);
    } else if (typeof onControlSeeking === 'function') onControlSeeking(value);
  };

  const onSeekChange = (e: any) => {
    if (isIOS) return;
    e.preventDefault(); // !!! important
    if (typeof onControlSeekChange === 'function') onControlSeekChange(hoverValue.current);
  };

  const onMouseMove = (clientWidth: any) => {
    if (typeof onControlMouseMove === 'function') {
      onControlMouseMove(hoverValue.current, clientWidth);
    }
  };

  const onMouseDown = (e: any) => {
    if (isSafari && !isIOS) {
      e.preventDefault(); // !!! important
    }
  };

  const onMouseOver = () => {
    if (typeof onControlMouseOver === 'function') onControlMouseOver();
  };

  const handleKeydown = (e: any) => {
    if (
      typeof onHandleOffAds === 'function' &&
      (e?.keyCode === KEY_CODE.ARROW_RIGHT || e?.keyCode === KEY_CODE.ARROW_LEFT)
    ) {
      onHandleOffAds(true);
    }
  };

  const onMouseOut = (e: any) => {
    if (isMobile && !isIOS) {
      if (typeof onControlSeekChange === 'function') onControlSeekChange(e?.target?.value);
    }
    if (typeof onControlMouseOut === 'function') onControlMouseOut();
  };

  const calcSliderPos = (e: any) => {
    let { offsetX } = e;
    if (isMobile) {
      const rect = e.target.getBoundingClientRect();
      offsetX = e.targetTouches[0]?.pageX - rect.left;
    }
    let val = ((offsetX + 1) / e.target.clientWidth) * parseInt(e.target.getAttribute('max'), 10);
    if (Number.isNaN(val)) val = 0;
    val = val < 0 ? 0 : val > 100 ? 100 : val;
    return val.toFixed(2);
  };

  return (
    <div className={className || 'progress-bar auto'} style={style || {}}>
      <div className="progress-bar-wrap">
        <input
          type="range"
          id={inputId || 'input'}
          onChange={onSeeking}
          onMouseUp={onSeekChange}
          onMouseDown={onMouseDown}
          onMouseOver={onMouseOver}
          onFocus={onMouseOver}
          onBlur={onMouseOut}
          onMouseOut={onMouseOut}
          onTouchEnd={onMouseOut}
          onTouchMove={onMouseOver}
          className={inputClass}
          step={step || 0.01}
          min={0}
          max={100}
          value={isMuted ? 0 : isOnEnded ? 100 : value}
        />
        <progress
          id={progressId}
          className="progress-bar__load"
          value={isMuted ? 0 : isOnEnded ? 100 : value}
          max={100}
        />
        {subProgressId && (
          <progress
            id="bufferBar"
            className="progress-bar__buffer"
            value="subValue || 0"
            max={100}
          />
        )}
        {!isDVR && hoverTimeText && !isMobile && (
          <PreviewThumbnail
            isPreviewThumb={isPreviewThumb}
            hoverTimeText={hoverTimeText}
            thumbMetadata={thumbMetadata}
            thumbSrc={thumbSrc}
            mouseLeft={mouseLeft}
            isFullscreen={isFullscreen}
            isMobile={isMobile}
          />
        )}
      </div>
    </div>
  );
};

export default memo(CustomProgress);

import React, { memo, useMemo, useRef } from 'react';
import Icon from '@components/basic/Icon/Icon';
import { isMobile } from 'react-device-detect';
import styles from '@components/basic/Input/Input.module.scss';
import classNames from 'classnames';

const KEY_CODE_ENTER = 13;

const InputGroupLabelSlideUp = ({
  title,
  id,
  placeholder,
  disabled,

  // iconLeft và iconRight là class của icon
  iconLeft,

  iconRight,
  value,
  error,
  hasBtn,
  disabledBtn,
  titleBtn,
  onChange,
  onEnter,
  onSubmit,
  autoComplete,
  onFocus,
  iconTick,
  width,
  forwardRef,
  keyLimit,
  isSuccess,
  isFailed
}: any) => {
  const ref = useRef<any>(null);
  const widthLeft = useMemo(() => {
    const widthInput = ref?.current?.offsetWidth;
    if (width < widthInput) return `${width + 8}px`;
    return '97%';
  }, [width, ref]);
  const onKeyPress = (e: any) => {
    const keyLength = e?.target?.value?.length;
    if (e.charCode === KEY_CODE_ENTER) {
      if (typeof onEnter === 'function') {
        onEnter(e);
      }
      e.preventDefault();
    }
    if (keyLength >= keyLimit) {
      e.preventDefault();
    }
  };
  const onFocusInput = () => {
    if (typeof onFocus === 'function') onFocus();
  };

  return (
    <div
      className={classNames(
        'field field-for-light field-line-text field-label-slide-up field-lg',
        styles.outlineSlideUp,
        isSuccess ? styles.success : isFailed ? styles.failed : ''
      )}
    >
      <div className={classNames('grid-x', styles.outlineSlideUpGroup)}>
        <div className="cell auto">
          <div className={classNames('field__input', styles.outlineSlideUpField)}>
            {iconLeft && (
              <span className="icon absolute-middle-left">
                <i className={`vie ${iconLeft}`} />
              </span>
            )}
            {iconRight && (
              <span className="icon absolute-middle-right">
                <i className={`vie ${iconRight}`} />
              </span>
            )}
            {value && (
              <span
                className="absolute text-16"
                style={{ opacity: 0, zIndex: -100 }}
                ref={forwardRef}
              >
                {value}
              </span>
            )}
            <input
              ref={ref}
              id={id}
              className={classNames('input', isFailed ? styles.outlineSlideUpError : '')}
              type="text"
              maxLength={keyLimit}
              value={value}
              disabled={!!disabled}
              onChange={onChange}
              onKeyPress={onKeyPress}
              placeholder={placeholder}
              autoComplete={autoComplete || 'on'}
              required
              onFocus={onFocusInput}
            />
            <label htmlFor={id} className="field__label">
              {title}
            </label>
            {iconTick && (
              <Icon
                spClass={`icon--tiny absolute right bottom m-b1${isMobile ? ' p-b1' : ''} `}
                iClass="vie-tick-solid-c text-green"
                spStyle={{ left: widthLeft }}
              />
            )}
          </div>
        </div>
        {hasBtn && (
          <div className="cell shrink">
            <button
              className={classNames(
                'button',
                styles.outlineSlideUpBtn,
                disabledBtn
                  ? styles.outlineSlideUpBtnDisabled
                  : isFailed
                  ? styles.outlineSlideUpBtnError
                  : !isFailed
                  ? styles.outlineSlideUpBtnReady
                  : ''
              )}
              title={titleBtn}
              disabled={disabledBtn}
              onClick={onSubmit}
            >
              <span>{titleBtn}</span>
            </button>
          </div>
        )}
      </div>
      <label className={`form-error m-y${error ? ` is-visible` : ''}`}>{error}</label>
    </div>
  );
};

export default memo(InputGroupLabelSlideUp);

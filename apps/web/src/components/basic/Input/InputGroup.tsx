import React, { useEffect, useRef, useState } from 'react';
import { TEXT } from '@constants/text';
import classNames from 'classnames';
import Icon from '../Icon/Icon';

const InputGroup = ({
  label,
  id,
  type,
  icon,
  error,
  value,
  onChange,
  classInput,
  inputTagsClass,
  className,
  placeholder,
  onEnter,
  warning,
  errorOtp,
  onFocus,
  onBlur,
  autoComplete,
  isCVV,
  inputStyle,
  security
}: any) => {
  const refEl = useRef<any>(null);
  const [isFocus, setFocus] = useState(false);
  useEffect(() => {
    if (refEl.current) {
      refEl.current.addEventListener('focus', handleFocus);
      return () => {
        if (refEl.current) refEl.current.removeEventListener('focus', handleFocus);
      };
    }
    return () => {
      if (refEl.current) refEl.current.removeEventListener('focus', handleFocus);
    };
  }, []);

  const handleFocus = () => {
    setFocus(true);
  };

  const onClickInput = () => {
    if (value !== '' && isFocus) {
      setFocus(true);
      return;
    }
    setFocus(true);
  };

  const onBlurInput = (e: any) => {
    if (value !== '' && isFocus) {
      if (typeof onBlur === 'function') onBlur(e);
      setFocus(true);
      return;
    }
    setFocus(false);
  };
  const onFocusInput = () => {
    if (value !== '' && isFocus) {
      if (typeof onFocus === 'function') onFocus();
      setFocus(true);
      return;
    }
    setFocus(false);
  };

  const [isToggleDisplayPW, setToggleDisplayPW] = useState({
    display: false,
    type
  });

  const toggleDisplayPassword = (e: any) => {
    e.preventDefault();
    setToggleDisplayPW({
      display: !isToggleDisplayPW.display,
      type: !isToggleDisplayPW.display ? 'text' : type
    });
  };

  let inputGroupClass = `input-group-custom ${className || ''}`;
  if ((isFocus || value) && id !== 'otpCode') inputGroupClass += ' focus';
  if (error) inputGroupClass += ' error';
  if (warning) inputGroupClass += ' warning';

  const onKeyPress = (e: any) => {
    if (e.charCode === 13) {
      if (typeof onEnter === 'function') onEnter(e);
      e.preventDefault();
    }
  };

  return (
    <div className={inputGroupClass}>
      <div className={`input-group ${classInput || ''}`}>
        {icon && <Icon spClass="absolute left" iClass={icon} />}
        <label htmlFor={id} className="input-group-label pos pos-tl">
          {label}
        </label>
        <input
          style={inputStyle}
          className={classNames(inputTagsClass || 'input-group-field', security)}
          ref={(el) => {
            refEl.current = el;
          }}
          placeholder={placeholder}
          id={id}
          type={isToggleDisplayPW.type}
          value={value}
          onChange={onChange}
          onKeyPress={onKeyPress}
          onClick={onClickInput}
          onBlur={onBlurInput}
          onFocus={onFocusInput}
          autoComplete={autoComplete || 'off'}
        />
        {type === 'password' && !isCVV && (
          <div className="input-group-button action-show-pass">
            <button onClick={toggleDisplayPassword} className="button text-black">
              <i
                className={
                  isToggleDisplayPW.display
                    ? 'vie vie-eye-off-o-rc-medium'
                    : 'vie vie-eye-on-o-rc-medium'
                }
                style={{ fontSize: '1.1875rem' }}
              />
            </button>
          </div>
        )}
      </div>
      {errorOtp && <label className="form-error is-visible">{TEXT.WRONG_CODE_RETRY}</label>}
      {error && <label className="form-error is-visible absolute right">{error}</label>}
      {warning && (
        <label id="capsLock" className="form-warning is-visible">
          {TEXT.CHECK_CAPS_LOCK}
        </label>
      )}
    </div>
  );
};

export default React.memo(InputGroup);

import { useState } from 'react';
import { REGEX } from '@constants/constants';
import { TEXT } from '@constants/text';

const useValidateCharsInput = (initialValue: any) => {
  const [value, setValue] = useState(initialValue);
  const [error, setError] = useState<any>('');
  const reg = REGEX.NOT_CHAR_SPECIAL;

  const handleChange = (event: any) => {
    const value = event.target?.value;
    if (value?.length > 20) {
      setError(TEXT.MAX_LENGTH_VALUE_20);
      return;
    }
    if (value?.length < 1 || value.trim().length === 0) {
      setError(TEXT.MIN_LENGTH_VALUE_1);
    } else if (reg.test(value)) {
      setError(TEXT.ERROR_SPEC_NAME);
      return;
    } else {
      setError('');
    }
    setValue(value);
  };

  const onBlur = (event: any) => setValue(event.target?.value.trim());

  return { value, error, onChange: handleChange, onBlur };
};

export default useValidateCharsInput;

import React from 'react';

const More = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.5911 10.659C14.4698 11.5377 14.4698 12.9623 13.5911 13.841C12.7124 14.7197 11.2878 14.7197 10.4091 13.841C9.53041 12.9623 9.53041 11.5377 10.4091 10.659C11.2878 9.78033 12.7124 9.78033 13.5911 10.659ZM20.3412 10.659C21.2199 11.5377 21.2199 12.9623 20.3412 13.841C19.4625 14.7197 18.0379 14.7197 17.1592 13.841C16.2805 12.9623 16.2805 11.5377 17.1592 10.659C18.0379 9.78033 19.4625 9.78033 20.3412 10.659ZM6.84103 13.8411C7.71971 12.9625 7.71971 11.5378 6.84103 10.6591C5.96234 9.78045 4.5377 9.78045 3.65901 10.6591C2.78033 11.5378 2.78033 12.9625 3.65901 13.8411C4.5377 14.7198 5.96234 14.7198 6.84103 13.8411Z"
          fill="currentColor"
        />
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.5911 10.659C14.4698 11.5377 14.4698 12.9623 13.5911 13.841C12.7124 14.7197 11.2878 14.7197 10.4091 13.841C9.53041 12.9623 9.53041 11.5377 10.4091 10.659C11.2878 9.78033 12.7124 9.78033 13.5911 10.659ZM20.3412 10.659C21.2199 11.5377 21.2199 12.9623 20.3412 13.841C19.4625 14.7197 18.0379 14.7197 17.1592 13.841C16.2805 12.9623 16.2805 11.5377 17.1592 10.659C18.0379 9.78033 19.4625 9.78033 20.3412 10.659ZM6.84103 13.8411C7.71971 12.9625 7.71971 11.5378 6.84103 10.6591C5.96234 9.78045 4.5377 9.78045 3.65901 10.6591C2.78033 11.5378 2.78033 12.9625 3.65901 13.8411C4.5377 14.7198 5.96234 14.7198 6.84103 13.8411Z"
          fill="currentColor"
        />
      </svg>
    );
  }
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.5911 10.659C14.4698 11.5377 14.4698 12.9623 13.5911 13.841C12.7124 14.7197 11.2878 14.7197 10.4091 13.841C9.53041 12.9623 9.53041 11.5377 10.4091 10.659C11.2878 9.78033 12.7124 9.78033 13.5911 10.659ZM20.3412 10.659C21.2199 11.5377 21.2199 12.9623 20.3412 13.841C19.4625 14.7197 18.0379 14.7197 17.1592 13.841C16.2805 12.9623 16.2805 11.5377 17.1592 10.659C18.0379 9.78033 19.4625 9.78033 20.3412 10.659ZM6.84103 13.8411C7.71971 12.9625 7.71971 11.5378 6.84103 10.6591C5.96234 9.78045 4.5377 9.78045 3.65901 10.6591C2.78033 11.5378 2.78033 12.9625 3.65901 13.8411C4.5377 14.7198 5.96234 14.7198 6.84103 13.8411Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default More;

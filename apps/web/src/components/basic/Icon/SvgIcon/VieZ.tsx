import React from 'react';

const VieZ = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={44}
        height={24}
        viewBox="0 0 44 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_32984_633504)">
          <path
            d="M43.2431 12C43.2431 18.6276 37.8664 24 31.2339 24C28.4643 24 25.9141 23.0624 23.8823 21.4896C21.0484 19.2949 19.2266 15.8601 19.2266 12.0019C19.2266 9.26277 20.1441 6.73912 21.6897 4.72024C23.8823 1.85066 27.3405 0 31.2339 0C37.8664 0 43.2431 5.3724 43.2431 12Z"
            fill="url(#paint0_linear_32984_633504)"
          />
          <path
            d="M24.3994 15.0474C25.7785 15.0474 26.8418 16.4046 26.3612 17.8716C26.1644 18.4708 25.6877 18.951 25.0899 19.1475C24.8553 19.2251 24.6226 19.261 24.3975 19.261C23.0183 19.261 21.9552 17.9037 22.4357 16.4368C22.6324 15.8357 23.1092 15.3574 23.7069 15.1608C23.9434 15.0833 24.1761 15.0474 24.3994 15.0474Z"
            fill="#A0FA37"
          />
          <path
            d="M26.538 17.9207C26.3393 18.5275 25.8815 19.0171 25.2988 19.2591H36.9752C37.5786 19.2591 38.0667 18.7714 38.0667 18.1665V16.363H26.5266C26.7025 16.8508 26.71 17.3933 26.538 17.9207Z"
            fill="white"
          />
          <path
            d="M25.492 5.40649C24.8885 5.40649 24.4004 5.8942 24.4004 6.49724V8.30441H38.0688V6.49724C38.0688 5.8942 37.5807 5.40649 36.9773 5.40649H25.492Z"
            fill="white"
          />
          <path
            d="M24.3984 14.7431V14.8547C25.0776 14.8547 25.7246 15.1666 26.1521 15.6959L36.875 11.6259C37.5918 11.3518 38.0649 10.6675 38.0649 9.89814V8.30078L25.5903 13.0134C24.8752 13.2857 24.3984 13.9738 24.3984 14.7431Z"
            fill="white"
          />
          <path
            d="M18.5263 15.0454V13.8148C18.5263 12.5937 18.2746 11.6636 17.7752 11.0435C17.2587 10.4103 16.5342 10.0833 15.6166 10.0833C14.7426 10.0833 14.0502 10.4273 13.5565 11.1059C13.0703 11.7808 12.8281 12.6957 12.8281 13.9131V15.7487C12.8281 16.8734 13.0703 17.7506 13.5413 18.3403C14.0275 18.9528 14.7426 19.261 15.6564 19.261C16.4869 19.261 17.1396 19.019 17.6333 18.5237C18.1063 18.0284 18.3844 17.3025 18.4638 16.3725L18.4714 16.3101H16.9844L16.9769 16.3611C16.922 16.8489 16.7896 17.2326 16.589 17.4897C16.3809 17.7354 16.1123 17.8489 15.7547 17.8489C15.3158 17.8489 15.0151 17.6806 14.8069 17.3139C14.5951 16.9471 14.4929 16.3687 14.4929 15.5445V15.0379L18.5263 15.0454ZM16.9163 13.2893V13.7959H14.4929V13.5539C14.4929 12.8129 14.5951 12.2893 14.8013 11.9642C15.0094 11.6447 15.3102 11.4916 15.7491 11.4916C16.1142 11.4916 16.3942 11.6485 16.6023 11.9642C16.8142 12.2628 16.9163 12.7127 16.9163 13.2893Z"
            fill="white"
          />
          <path
            d="M9.01069 6.777C9.21122 6.777 9.37959 6.61633 9.37959 6.40649V5.777C9.37959 5.57473 9.21877 5.40649 9.01069 5.40649H5.94784C5.74731 5.40649 5.57894 5.56719 5.57894 5.777V6.40649C5.57894 6.60876 5.73976 6.777 5.94784 6.777H6.49647C6.56268 6.777 6.62134 6.79969 6.6535 6.84316C6.68566 6.88665 6.697 6.94903 6.68566 7.0114L4.75033 15.605C4.74275 15.6277 4.73899 15.6447 4.73141 15.656C4.72383 15.6447 4.72007 15.6258 4.71249 15.605L2.80177 7.0114C2.78285 6.94524 2.80177 6.88665 2.83393 6.84316C2.86988 6.79969 2.92475 6.777 2.99472 6.777H3.56607C3.76659 6.777 3.93496 6.61633 3.93496 6.40649V5.777C3.93496 5.57473 3.77415 5.40649 3.56607 5.40649H0.36512C0.160804 5.40839 0 5.57473 0 5.77511V6.4046C0 6.60687 0.160804 6.77511 0.368904 6.77511H0.599702C0.745374 6.77511 0.904285 6.89231 0.936446 7.03977L3.37688 16.656L4.03901 19.2591H5.37841L8.26721 7.03977C8.29937 6.89988 8.4545 6.77511 8.59637 6.77511H9.01069V6.777Z"
            fill="white"
          />
          <path
            d="M9.31836 8.44031C9.31836 8.64258 9.47915 8.81082 9.68725 8.81082H10.5405C10.741 8.81082 10.9094 8.65012 10.9094 8.44031V7.80703C10.9094 7.60476 10.7485 7.43652 10.5405 7.43652H9.68725C9.48673 7.43652 9.31836 7.59719 9.31836 7.80703V8.44031Z"
            fill="white"
          />
          <path
            d="M11.5121 17.9472H11.2189C11.0808 17.9472 10.9597 17.83 10.9597 17.6863V10.7865C10.9597 10.5843 10.7989 10.416 10.5908 10.416H8.75955C8.55899 10.416 8.39062 10.5767 8.39062 10.7865V11.3574C8.39062 11.5597 8.55144 11.7279 8.75955 11.7279H9.05276C9.19276 11.7279 9.31194 11.8451 9.31194 11.9888V17.692C9.31194 17.8319 9.19465 17.9529 9.05276 17.9529C9.05276 17.9529 8.3982 18.1136 8.3982 18.3234V19.261H11.8753V18.3234C11.8848 18.1117 11.7164 17.9472 11.5121 17.9472Z"
            fill="white"
          />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_32984_633504"
            x1="39.8864"
            y1="20.6461"
            x2="22.9163"
            y2="3.66299"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#00FFB4" />
            <stop offset="0.1488" stopColor="#37E3C7" />
            <stop offset="0.3012" stopColor="#68CBD8" />
            <stop offset="0.4527" stopColor="#90B6E6" />
            <stop offset="0.6004" stopColor="#AFA6F1" />
            <stop offset="0.7432" stopColor="#C69BF9" />
            <stop offset="0.8789" stopColor="#D394FD" />
            <stop offset={1} stopColor="#D892FF" />
          </linearGradient>
          <clipPath id="clip0_32984_633504">
            <rect width="43.5" height={24} fill="white" />
          </clipPath>
        </defs>
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={44}
        height={24}
        viewBox="0 0 44 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_32984_634803)">
          <path
            d="M43.2431 12C43.2431 18.6276 37.8664 24 31.2339 24C28.4643 24 25.9141 23.0624 23.8823 21.4896C21.0484 19.2949 19.2266 15.8601 19.2266 12.0019C19.2266 9.26277 20.1441 6.73912 21.6897 4.72024C23.8823 1.85066 27.3405 0 31.2339 0C37.8664 0 43.2431 5.3724 43.2431 12Z"
            fill="#B1B1B1"
          />
          <path
            d="M24.3994 15.0475C25.7785 15.0475 26.8418 16.4048 26.3612 17.8717C26.1644 18.4709 25.6877 18.9511 25.0899 19.1477C24.8553 19.2252 24.6226 19.2611 24.3975 19.2611C23.0183 19.2611 21.9552 17.9038 22.4357 16.4369C22.6324 15.8358 23.1092 15.3575 23.7069 15.1609C23.9434 15.0834 24.1761 15.0475 24.3994 15.0475Z"
            fill="#F2F2F2"
          />
          <path
            d="M26.538 17.9207C26.3393 18.5275 25.8815 19.0171 25.2988 19.2591H36.9752C37.5786 19.2591 38.0667 18.7714 38.0667 18.1665V16.363H26.5266C26.7025 16.8508 26.71 17.3933 26.538 17.9207Z"
            fill="white"
          />
          <path
            d="M25.492 5.40649C24.8885 5.40649 24.4004 5.8942 24.4004 6.49724V8.30441H38.0688V6.49724C38.0688 5.8942 37.5807 5.40649 36.9773 5.40649H25.492Z"
            fill="white"
          />
          <path
            d="M24.3984 14.7431V14.8547C25.0776 14.8547 25.7246 15.1666 26.1521 15.6959L36.875 11.6259C37.5918 11.3518 38.0649 10.6675 38.0649 9.89814V8.30078L25.5903 13.0134C24.8752 13.2857 24.3984 13.9738 24.3984 14.7431Z"
            fill="white"
          />
          <path
            d="M18.5263 15.0454V13.8148C18.5263 12.5937 18.2746 11.6636 17.7752 11.0435C17.2587 10.4103 16.5342 10.0833 15.6166 10.0833C14.7426 10.0833 14.0502 10.4273 13.5565 11.1059C13.0703 11.7808 12.8281 12.6957 12.8281 13.9131V15.7487C12.8281 16.8734 13.0703 17.7506 13.5413 18.3403C14.0275 18.9528 14.7426 19.261 15.6564 19.261C16.4869 19.261 17.1396 19.019 17.6333 18.5237C18.1063 18.0284 18.3844 17.3025 18.4638 16.3725L18.4714 16.3101H16.9844L16.9769 16.3611C16.922 16.8489 16.7896 17.2326 16.589 17.4897C16.3809 17.7354 16.1123 17.8489 15.7547 17.8489C15.3158 17.8489 15.0151 17.6806 14.8069 17.3139C14.5951 16.9471 14.4929 16.3687 14.4929 15.5445V15.0379L18.5263 15.0454ZM16.9163 13.2893V13.7959H14.4929V13.5539C14.4929 12.8129 14.5951 12.2893 14.8013 11.9642C15.0094 11.6447 15.3102 11.4916 15.7491 11.4916C16.1142 11.4916 16.3942 11.6485 16.6023 11.9642C16.8142 12.2628 16.9163 12.7127 16.9163 13.2893Z"
            fill="white"
          />
          <path
            d="M9.01069 6.777C9.21122 6.777 9.37959 6.61633 9.37959 6.40649V5.777C9.37959 5.57473 9.21877 5.40649 9.01069 5.40649H5.94784C5.74731 5.40649 5.57894 5.56719 5.57894 5.777V6.40649C5.57894 6.60876 5.73976 6.777 5.94784 6.777H6.49647C6.56268 6.777 6.62134 6.79969 6.6535 6.84316C6.68566 6.88665 6.697 6.94903 6.68566 7.0114L4.75033 15.605C4.74275 15.6277 4.73899 15.6447 4.73141 15.656C4.72383 15.6447 4.72007 15.6258 4.71249 15.605L2.80177 7.0114C2.78285 6.94524 2.80177 6.88665 2.83393 6.84316C2.86988 6.79969 2.92475 6.777 2.99472 6.777H3.56607C3.76659 6.777 3.93496 6.61633 3.93496 6.40649V5.777C3.93496 5.57473 3.77415 5.40649 3.56607 5.40649H0.36512C0.160804 5.40839 0 5.57473 0 5.77511V6.4046C0 6.60687 0.160804 6.77511 0.368904 6.77511H0.599702C0.745374 6.77511 0.904285 6.89231 0.936446 7.03977L3.37688 16.656L4.03902 19.2591H5.37842L8.26722 7.03977C8.29937 6.89988 8.45451 6.77511 8.59637 6.77511H9.01069V6.777Z"
            fill="white"
          />
          <path
            d="M9.31836 8.44031C9.31836 8.64258 9.47915 8.81082 9.68725 8.81082H10.5405C10.741 8.81082 10.9094 8.65012 10.9094 8.44031V7.80703C10.9094 7.60476 10.7485 7.43652 10.5405 7.43652H9.68725C9.48673 7.43652 9.31836 7.59719 9.31836 7.80703V8.44031Z"
            fill="white"
          />
          <path
            d="M11.5121 17.9472H11.2189C11.0808 17.9472 10.9597 17.83 10.9597 17.6863V10.7865C10.9597 10.5843 10.7989 10.416 10.5908 10.416H8.75955C8.55899 10.416 8.39062 10.5767 8.39062 10.7865V11.3574C8.39062 11.5597 8.55144 11.7279 8.75955 11.7279H9.05276C9.19276 11.7279 9.31194 11.8451 9.31194 11.9888V17.692C9.31194 17.8319 9.19465 17.9529 9.05276 17.9529C9.05276 17.9529 8.3982 18.1136 8.3982 18.3234V19.261H11.8753V18.3234C11.8848 18.1117 11.7164 17.9472 11.5121 17.9472Z"
            fill="white"
          />
        </g>
        <g opacity="0.3">
          <path
            d="M43.243 12C43.243 18.6276 37.8663 24 31.2338 24C28.4642 24 25.914 23.0624 23.8822 21.4896C21.0482 19.2949 19.2264 15.8601 19.2264 12.0019C19.2264 9.26277 20.1439 6.73912 21.6896 4.72024C23.8822 1.85066 27.3404 0 31.2338 0C37.8663 0 43.243 5.3724 43.243 12Z"
            fill="black"
          />
          <path
            d="M24.3986 15.0474C25.7778 15.0474 26.8411 16.4047 26.3604 17.8716C26.1637 18.4709 25.687 18.951 25.0891 19.1476C24.8546 19.2251 24.6219 19.2611 24.3967 19.2611C23.0176 19.2611 21.9544 17.9038 22.4349 16.4368C22.6317 15.8357 23.1084 15.3575 23.7062 15.1609C23.9427 15.0833 24.1754 15.0474 24.3986 15.0474Z"
            fill="black"
          />
          <path
            d="M26.5383 17.9207C26.3396 18.5275 25.8818 19.0171 25.2991 19.2591H36.9755C37.5789 19.2591 38.067 18.7714 38.067 18.1665V16.363H26.5269C26.7029 16.8508 26.7103 17.3933 26.5383 17.9207Z"
            fill="black"
          />
          <path
            d="M25.492 5.40656C24.8886 5.40656 24.4005 5.89426 24.4005 6.4973V8.30447H38.0688V6.4973C38.0688 5.89426 37.5807 5.40656 36.9773 5.40656H25.492Z"
            fill="black"
          />
          <path
            d="M24.3986 14.7431V14.8546C25.0778 14.8546 25.7248 15.1665 26.1523 15.6958L36.8751 11.6259C37.592 11.3518 38.0651 10.6675 38.0651 9.89808V8.30072L25.5904 13.0134C24.8753 13.2856 24.3986 13.9737 24.3986 14.7431Z"
            fill="black"
          />
          <path
            d="M18.5265 15.0454V13.8148C18.5265 12.5937 18.2749 11.6636 17.7754 11.0435C17.259 10.4103 16.5344 10.0833 15.6169 10.0833C14.7429 10.0833 14.0505 10.4273 13.5567 11.1059C13.0705 11.7808 12.8284 12.6957 12.8284 13.9131V15.7487C12.8284 16.8734 13.0705 17.7506 13.5416 18.3403C14.0278 18.9528 14.7429 19.261 15.6566 19.261C16.4871 19.261 17.1398 19.019 17.6335 18.5237C18.1065 18.0284 18.3846 17.3025 18.4641 16.3725L18.4716 16.3101H16.9847L16.9771 16.3611C16.9222 16.8489 16.7898 17.2326 16.5893 17.4897C16.3812 17.7354 16.1125 17.8489 15.755 17.8489C15.3161 17.8489 15.0153 17.6806 14.8072 17.3139C14.5953 16.9471 14.4932 16.3687 14.4932 15.5445V15.0379L18.5265 15.0454ZM16.9166 13.2893V13.7959H14.4932V13.5539C14.4932 12.8129 14.5953 12.2893 14.8015 11.9642C15.0096 11.6447 15.3104 11.4916 15.7493 11.4916C16.1144 11.4916 16.3944 11.6485 16.6025 11.9642C16.8144 12.2628 16.9166 12.7127 16.9166 13.2893Z"
            fill="black"
          />
          <path
            d="M9.01069 6.77706C9.21122 6.77706 9.37959 6.61639 9.37959 6.40656V5.77706C9.37959 5.57479 9.21877 5.40656 9.01069 5.40656H5.94784C5.74731 5.40656 5.57894 5.56725 5.57894 5.77706V6.40656C5.57894 6.60882 5.73976 6.77706 5.94784 6.77706H6.49647C6.56268 6.77706 6.62134 6.79976 6.6535 6.84322C6.68566 6.88671 6.697 6.94909 6.68566 7.01146L4.75033 15.605C4.74275 15.6277 4.73899 15.6447 4.73141 15.6561C4.72383 15.6447 4.72007 15.6258 4.71249 15.605L2.80177 7.01146C2.78285 6.9453 2.80177 6.88671 2.83393 6.84322C2.86988 6.79976 2.92475 6.77706 2.99472 6.77706H3.56607C3.76659 6.77706 3.93496 6.61639 3.93496 6.40656V5.77706C3.93496 5.57479 3.77415 5.40656 3.56607 5.40656H0.36512C0.160804 5.40845 0 5.5748 0 5.77517V6.40466C0 6.60693 0.160804 6.77517 0.368904 6.77517H0.599702C0.745374 6.77517 0.904285 6.89237 0.936446 7.03983L4.03902 19.2591H5.37842L8.26722 7.03983C8.29937 6.89994 8.45451 6.77517 8.59637 6.77517H9.01069V6.77706Z"
            fill="black"
          />
          <path
            d="M9.31897 8.44043C9.31897 8.6427 9.47976 8.81094 9.68787 8.81094H10.5411C10.7416 8.81094 10.91 8.65025 10.91 8.44043V7.80715C10.91 7.60489 10.7492 7.43665 10.5411 7.43665H9.68787C9.48734 7.43665 9.31897 7.59731 9.31897 7.80715V8.44043Z"
            fill="black"
          />
          <path
            d="M11.5116 17.9471H11.2183C11.0802 17.9471 10.9592 17.8299 10.9592 17.6863V10.7865C10.9592 10.5842 10.7983 10.416 10.5903 10.416H8.759C8.55844 10.416 8.39008 10.5766 8.39008 10.7865V11.3573C8.39008 11.5596 8.55089 11.7278 8.759 11.7278H9.05221C9.19221 11.7278 9.3114 11.845 9.3114 11.9887V17.6919C9.3114 17.8318 9.19411 17.9528 9.05221 17.9528C9.05221 17.9528 8.39765 18.1135 8.39765 18.3233V19.2609H11.8748V18.3233C11.8842 18.1116 11.7159 17.9471 11.5116 17.9471Z"
            fill="black"
          />
        </g>
        <defs>
          <clipPath id="clip0_32984_634803">
            <rect width="43.5" height={24} fill="white" />
          </clipPath>
        </defs>
      </svg>
    );
  }
  return (
    <svg width={44} height={24} viewBox="0 0 44 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_32984_634759)">
        <path
          d="M43.2431 12C43.2431 18.6276 37.8664 24 31.2339 24C28.4643 24 25.9141 23.0624 23.8823 21.4896C21.0484 19.2949 19.2266 15.8601 19.2266 12.0019C19.2266 9.26277 20.1441 6.73912 21.6897 4.72024C23.8823 1.85066 27.3405 0 31.2339 0C37.8664 0 43.2431 5.3724 43.2431 12Z"
          fill="#B1B1B1"
        />
        <path
          d="M24.3994 15.0475C25.7785 15.0475 26.8418 16.4048 26.3612 17.8717C26.1644 18.4709 25.6877 18.9511 25.0899 19.1477C24.8553 19.2252 24.6226 19.2611 24.3975 19.2611C23.0183 19.2611 21.9552 17.9038 22.4357 16.4369C22.6324 15.8358 23.1092 15.3575 23.7069 15.1609C23.9434 15.0834 24.1761 15.0475 24.3994 15.0475Z"
          fill="#F2F2F2"
        />
        <path
          d="M26.538 17.9207C26.3393 18.5275 25.8815 19.0171 25.2988 19.2591H36.9752C37.5786 19.2591 38.0667 18.7714 38.0667 18.1665V16.363H26.5266C26.7025 16.8508 26.71 17.3933 26.538 17.9207Z"
          fill="white"
        />
        <path
          d="M25.492 5.40649C24.8885 5.40649 24.4004 5.8942 24.4004 6.49724V8.30441H38.0688V6.49724C38.0688 5.8942 37.5807 5.40649 36.9773 5.40649H25.492Z"
          fill="white"
        />
        <path
          d="M24.3984 14.7431V14.8547C25.0776 14.8547 25.7246 15.1666 26.1521 15.6959L36.875 11.6259C37.5918 11.3518 38.0649 10.6675 38.0649 9.89814V8.30078L25.5903 13.0134C24.8752 13.2857 24.3984 13.9738 24.3984 14.7431Z"
          fill="white"
        />
        <path
          d="M18.5263 15.0454V13.8148C18.5263 12.5937 18.2746 11.6636 17.7752 11.0435C17.2587 10.4103 16.5342 10.0833 15.6166 10.0833C14.7426 10.0833 14.0502 10.4273 13.5565 11.1059C13.0703 11.7808 12.8281 12.6957 12.8281 13.9131V15.7487C12.8281 16.8734 13.0703 17.7506 13.5413 18.3403C14.0275 18.9528 14.7426 19.261 15.6564 19.261C16.4869 19.261 17.1396 19.019 17.6333 18.5237C18.1063 18.0284 18.3844 17.3025 18.4638 16.3725L18.4714 16.3101H16.9844L16.9769 16.3611C16.922 16.8489 16.7896 17.2326 16.589 17.4897C16.3809 17.7354 16.1123 17.8489 15.7547 17.8489C15.3158 17.8489 15.0151 17.6806 14.8069 17.3139C14.5951 16.9471 14.4929 16.3687 14.4929 15.5445V15.0379L18.5263 15.0454ZM16.9163 13.2893V13.7959H14.4929V13.5539C14.4929 12.8129 14.5951 12.2893 14.8013 11.9642C15.0094 11.6447 15.3102 11.4916 15.7491 11.4916C16.1142 11.4916 16.3942 11.6485 16.6023 11.9642C16.8142 12.2628 16.9163 12.7127 16.9163 13.2893Z"
          fill="white"
        />
        <path
          d="M9.01069 6.777C9.21122 6.777 9.37959 6.61633 9.37959 6.40649V5.777C9.37959 5.57473 9.21877 5.40649 9.01069 5.40649H5.94784C5.74731 5.40649 5.57894 5.56719 5.57894 5.777V6.40649C5.57894 6.60876 5.73976 6.777 5.94784 6.777H6.49647C6.56268 6.777 6.62134 6.79969 6.6535 6.84316C6.68566 6.88665 6.697 6.94903 6.68566 7.0114L4.75033 15.605C4.74275 15.6277 4.73899 15.6447 4.73141 15.656C4.72383 15.6447 4.72007 15.6258 4.71249 15.605L2.80177 7.0114C2.78285 6.94524 2.80177 6.88665 2.83393 6.84316C2.86988 6.79969 2.92475 6.777 2.99472 6.777H3.56607C3.76659 6.777 3.93496 6.61633 3.93496 6.40649V5.777C3.93496 5.57473 3.77415 5.40649 3.56607 5.40649H0.36512C0.160804 5.40839 0 5.57473 0 5.77511V6.4046C0 6.60687 0.160804 6.77511 0.368904 6.77511H0.599702C0.745374 6.77511 0.904285 6.89231 0.936446 7.03977L3.37688 16.656L4.03902 19.2591H5.37842L8.26722 7.03977C8.29937 6.89988 8.45451 6.77511 8.59637 6.77511H9.01069V6.777Z"
          fill="white"
        />
        <path
          d="M9.31836 8.44031C9.31836 8.64258 9.47915 8.81082 9.68725 8.81082H10.5405C10.741 8.81082 10.9094 8.65012 10.9094 8.44031V7.80703C10.9094 7.60476 10.7485 7.43652 10.5405 7.43652H9.68725C9.48673 7.43652 9.31836 7.59719 9.31836 7.80703V8.44031Z"
          fill="white"
        />
        <path
          d="M11.5121 17.9472H11.2189C11.0808 17.9472 10.9597 17.83 10.9597 17.6863V10.7865C10.9597 10.5843 10.7989 10.416 10.5908 10.416H8.75955C8.55899 10.416 8.39062 10.5767 8.39062 10.7865V11.3574C8.39062 11.5597 8.55144 11.7279 8.75955 11.7279H9.05276C9.19276 11.7279 9.31194 11.8451 9.31194 11.9888V17.692C9.31194 17.8319 9.19465 17.9529 9.05276 17.9529C9.05276 17.9529 8.3982 18.1136 8.3982 18.3234V19.261H11.8753V18.3234C11.8848 18.1117 11.7164 17.9472 11.5121 17.9472Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_32984_634759">
          <rect width="43.5" height={24} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default VieZ;

import React from 'react';
import CustomImage from '../../Image/Image';
import ConfigImage from '@/config/ConfigImage';

const Exsh = ({ size, isHovering, isActive }: any) => {
  if (isHovering || isActive) {
    return <CustomImage className="w-full max-w-6" src={ConfigImage.exshLogo} alt="exsh" notWebp />;
  }
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size || '29'}
      height="24"
      viewBox="0 0 29 24"
      fill="none"
    >
      <g clipPath="url(#clip0_10336_35680)">
        <path
          d="M7.92659 5.04911C5.57688 7.38561 3.53221 9.42008 3.38287 9.56883L3.11133 9.84094L5.11165 9.83822L7.11197 9.8355L7.20249 9.81555C7.42696 9.76657 7.66319 9.66407 7.83064 9.54434C7.87771 9.50988 7.97184 9.43006 8.03882 9.36475C8.50224 8.92121 11.7924 5.69582 11.834 5.64412C12.0422 5.38652 12.1644 5.0872 12.1942 4.76158C12.2006 4.68992 12.2033 4.05409 12.2015 2.72892L12.1988 0.80058L7.92659 5.04911Z"
          fill="#CCCCCC"
        />
        <path
          d="M8.32089 9.85121C7.91539 9.88749 7.52619 10.0535 7.23745 10.3147C7.20215 10.3464 6.27802 11.2707 5.18373 12.3691L3.19336 14.3673H5.0914C6.37849 14.3673 7.01751 14.3646 7.07725 14.3573C7.44382 14.3174 7.80587 14.1632 8.08193 13.9301C8.15253 13.8703 12.1849 9.86663 12.1912 9.8494C12.1957 9.83942 8.43674 9.84123 8.32089 9.85121Z"
          fill="#CCCCCC"
        />
        <path
          d="M12.4648 11.7515C12.4648 12.9697 12.4685 13.6354 12.4739 13.6971C12.511 14.0599 12.6585 14.4236 12.8857 14.7075C12.9129 14.742 13.8388 15.6989 14.9431 16.8336L16.9497 18.8971L16.9524 17.0014C16.9543 15.3107 16.9534 15.0939 16.9407 14.9905C16.8963 14.6359 16.7678 14.3275 16.5397 14.0318C16.5071 13.9892 16.2193 13.6917 15.8998 13.3697C15.5803 13.0477 14.7639 12.2232 14.085 11.5375C13.4071 10.8517 12.7644 10.2023 12.6585 10.0953L12.4648 9.90027V11.7515Z"
          fill="#CCCCCC"
        />
        <path
          d="M16.9578 11.7914C16.9605 13.8766 16.956 13.717 17.0157 13.9455C17.0782 14.1868 17.2085 14.4381 17.3651 14.6222C17.4629 14.7365 21.077 18.4598 21.1377 18.5088C21.3712 18.6966 21.6364 18.8181 21.9378 18.8771L22.0373 18.897L24.0467 18.8997L26.0561 18.9025L25.9927 18.8408C25.8343 18.6848 17.2583 10.2013 17.1153 10.0598L16.9551 9.9002L16.9578 11.7914Z"
          fill="#CCCCCC"
        />
        <path
          d="M7.8125 14.4435C7.8125 14.4471 11.5389 18.0961 12.101 18.643L12.2024 18.7428L12.2005 16.8652C12.1978 14.7609 12.2042 14.9578 12.1299 14.8072C12.044 14.6312 11.8901 14.507 11.6991 14.458C11.6303 14.4398 11.5905 14.4398 9.7205 14.4398C8.67146 14.4398 7.8125 14.4417 7.8125 14.4435Z"
          fill="#CCCCCC"
        />
        <path
          d="M0.345703 22.5259V23.4283H1.3866H2.42749V23.2968V23.1653H1.51784H0.608189V22.9113V22.6574L1.44814 22.6556L2.28719 22.6528L2.28991 22.5231L2.29262 22.3943H1.44995H0.608189V22.1358V21.8773H1.51784H2.42749V21.7503V21.6234H1.3866H0.345703V22.5259Z"
          fill="#CCCCCC"
        />
        <path
          d="M2.79858 22.5259V23.4283H2.9253H3.05202V22.6519V21.8755L3.1362 21.8791C3.2258 21.8828 3.27468 21.9018 3.32084 21.9517C3.33261 21.9635 3.46385 22.2302 3.6141 22.5449C3.76345 22.8587 3.90283 23.1472 3.92275 23.1853C4.03679 23.4057 4.30652 23.4909 4.52918 23.3776C4.55543 23.3639 4.6025 23.3286 4.63327 23.2986C4.69844 23.2361 4.68486 23.2633 5.06682 22.4578C5.31302 21.9381 5.32297 21.9218 5.40987 21.8927C5.43521 21.8837 5.48228 21.8773 5.52029 21.8773H5.58636V22.6528V23.4283H5.71308H5.8398V22.5259V21.6234H5.63434C5.40715 21.6234 5.36914 21.6297 5.27229 21.6787C5.2035 21.7132 5.11842 21.7884 5.08131 21.8474C5.06592 21.8719 4.92472 22.1603 4.76723 22.4896C4.60974 22.8188 4.47216 23.0982 4.4613 23.1091C4.38798 23.188 4.2332 23.1825 4.17075 23.0982C4.15898 23.0828 4.03679 22.8333 3.89831 22.544C3.55255 21.8202 3.5616 21.8356 3.48829 21.7667C3.4548 21.7349 3.4023 21.6968 3.37243 21.6805C3.2783 21.6297 3.23485 21.6234 3.00495 21.6234H2.79858V22.5259Z"
          fill="#CCCCCC"
        />
        <path
          d="M7.46551 21.6478C7.47456 21.6624 7.61214 21.8646 7.77054 22.0977C7.94342 22.3526 8.05656 22.5268 8.05203 22.5331C8.04841 22.5395 7.91174 22.7408 7.74881 22.9812C7.58589 23.2215 7.45193 23.4211 7.45193 23.4238C7.45193 23.4265 7.51439 23.4283 7.59132 23.4283H7.73071L7.99139 23.0429L8.25297 22.6574L8.62859 22.6601L9.00422 22.6619L9.25766 23.0356C9.39704 23.2415 9.51562 23.4138 9.52105 23.4193C9.52738 23.4256 9.57988 23.4283 9.66858 23.4265L9.80616 23.4238L9.50204 22.9757C9.33459 22.7299 9.19973 22.524 9.20154 22.5177C9.20425 22.5122 9.29024 22.3834 9.39342 22.2311C9.49661 22.0787 9.63238 21.88 9.69393 21.7884L9.80616 21.6234H9.66315L9.52014 21.6243L9.25947 22.0088L8.99879 22.3943L8.62588 22.3925L8.25297 22.3898L7.99501 22.0088L7.73614 21.6279L7.59313 21.6252L7.44922 21.6224L7.46551 21.6478Z"
          fill="#CCCCCC"
        />
        <path
          d="M10.1211 22.5259V23.4283H10.2297H10.3383V22.5259V21.6234H10.2297H10.1211V22.5259Z"
          fill="#CCCCCC"
        />
        <path
          d="M10.7461 22.5259V23.4283H10.8773H11.0086V22.6519V21.8755L11.1652 21.8791C11.338 21.8828 11.3634 21.8891 11.4213 21.9517C11.4385 21.9689 11.5788 22.2311 11.7553 22.5757C11.9237 22.9023 12.0775 23.1916 12.0974 23.2179C12.1427 23.2778 12.2088 23.3304 12.2866 23.3703C12.3898 23.4229 12.4314 23.4283 12.7175 23.4283H12.9727V22.5259V21.6234H12.8415H12.7102V22.3943V23.1653H12.5572C12.4079 23.1653 12.4034 23.1644 12.3572 23.1408C12.331 23.1263 12.2984 23.1009 12.2848 23.0837C12.2712 23.0664 12.1255 22.7898 11.9608 22.4687C11.796 22.1485 11.6485 21.8683 11.634 21.8465C11.5933 21.7875 11.5109 21.7168 11.4421 21.6814C11.3335 21.627 11.3055 21.6234 11.0113 21.6234H10.7461V22.5259Z"
          fill="#CCCCCC"
        />
        <path
          d="M13.3616 22.5259V23.4283H13.4928H13.6241V23.0429V22.6574H14.4613H15.2985V23.0429V23.4283H15.4298H15.561V22.5259V21.6234H15.4298H15.2985V22.0088V22.3943H14.4613H13.6241V22.0088V21.6234H13.4928H13.3616V22.5259Z"
          fill="#CCCCCC"
        />
        <path
          d="M16.9237 21.6345C16.9119 21.668 16.8105 21.9855 16.8105 21.99C16.8105 21.9955 16.9997 21.9982 17.0006 21.9928C17.0015 21.9919 17.0169 21.9084 17.0359 21.8068L17.0703 21.6236H16.9988C16.9499 21.6236 16.9264 21.6272 16.9237 21.6345Z"
          fill="#CCCCCC"
        />
        <path
          d="M17.8188 21.6379C17.5916 21.6923 17.4332 21.8374 17.3925 22.0306C17.3427 22.2692 17.4414 22.4769 17.655 22.5803C17.7998 22.6501 17.798 22.6501 18.4524 22.6565C18.8506 22.661 19.0407 22.6664 19.0615 22.6737C19.1855 22.7163 19.2525 22.807 19.2417 22.9186C19.2335 23.0147 19.1774 23.0882 19.0769 23.1354L19.0226 23.1608L18.2035 23.1653L17.3843 23.1698L17.3816 23.2995L17.3789 23.4283H18.1799C19.0534 23.4283 19.048 23.4283 19.1837 23.3757C19.4335 23.2787 19.5584 23.0465 19.5023 22.7816C19.4851 22.6982 19.4498 22.6365 19.3801 22.5667C19.3123 22.4977 19.2254 22.4488 19.1204 22.4197C19.0525 22.4007 19.0271 22.3998 18.4705 22.3943C17.8984 22.3889 17.8903 22.3889 17.8288 22.368C17.6541 22.31 17.597 22.1286 17.7093 21.9916C17.7265 21.9707 17.76 21.9426 17.7844 21.9299C17.8822 21.8764 17.8668 21.8773 18.6986 21.8773H19.4616V21.7503V21.6234L18.6678 21.6243C18.0125 21.6252 17.8631 21.627 17.8188 21.6379Z"
          fill="#CCCCCC"
        />
        <path
          d="M20.5552 21.6442C20.4475 21.6814 20.3615 21.7531 20.3108 21.8474C20.2982 21.8719 20.1678 22.232 20.023 22.6483C19.8773 23.0646 19.756 23.4111 19.7542 23.4166C19.7506 23.4256 19.7804 23.4283 19.8845 23.4283H20.0194L20.2538 22.7544C20.3823 22.3844 20.4955 22.0642 20.5036 22.0433C20.5254 21.9916 20.5788 21.9354 20.6331 21.9063L20.6792 21.8819H20.9598C21.2259 21.8819 21.2422 21.8828 21.282 21.9009C21.3391 21.9272 21.387 21.9726 21.4115 22.0215C21.4232 22.0442 21.5391 22.3707 21.6703 22.7454L21.9084 23.4283H22.0423H22.1763L22.1148 23.2488C21.845 22.4732 21.6387 21.8918 21.6178 21.851C21.5717 21.7585 21.483 21.6841 21.3716 21.6442C21.3137 21.6243 21.3047 21.6234 20.9643 21.6234C20.6249 21.6243 20.6141 21.6243 20.5552 21.6442Z"
          fill="#CCCCCC"
        />
        <path
          d="M22.0628 21.6342C22.0764 21.6696 22.6185 22.5485 22.643 22.5739C22.691 22.6247 22.7525 22.6492 22.8512 22.6546L22.9381 22.6601V23.0438V23.4283H23.0693H23.2005V23.0438V22.6601L23.2874 22.6546C23.3861 22.6492 23.4476 22.6247 23.4965 22.5739C23.52 22.5485 24.0785 21.6415 24.0785 21.6279C24.0785 21.6252 24.0142 21.6243 23.9364 21.6252L23.7943 21.6279L23.5626 22.0043C23.4105 22.251 23.3236 22.3844 23.3092 22.3925C23.292 22.4007 23.2286 22.4034 23.0584 22.4016C22.8439 22.3989 22.8285 22.398 22.8122 22.3807C22.8023 22.3707 22.6928 22.1975 22.5688 21.9952L22.3425 21.6279L22.2004 21.6252C22.0872 21.6234 22.0592 21.6252 22.0628 21.6342Z"
          fill="#CCCCCC"
        />
        <path
          d="M25.3359 22.5259V23.4283H25.4627H25.5894V23.0429V22.6574H26.4266H27.2639V23.0429V23.4283H27.3951H27.5263V22.5259V21.6234H27.3951H27.2639V22.0088V22.3943H26.4266H25.5894V22.0088V21.6234H25.4627H25.3359V22.5259Z"
          fill="#CCCCCC"
        />
        <path
          d="M27.9609 22.5259V23.4283H28.0741H28.1872V22.5259V21.6234H28.0741H27.9609V22.5259Z"
          fill="#CCCCCC"
        />
        <path
          d="M28.5864 21.7884C28.5701 21.88 28.5547 21.9635 28.552 21.9744C28.5475 21.9943 28.5493 21.9952 28.619 21.9952H28.6905L28.7122 21.929C28.724 21.8937 28.7502 21.8093 28.772 21.7431L28.81 21.6234H28.7131H28.6163L28.5864 21.7884Z"
          fill="#CCCCCC"
        />
      </g>
      <defs>
        <clipPath id="clip0_10336_35680">
          <rect width={size || '29'} height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Exsh;

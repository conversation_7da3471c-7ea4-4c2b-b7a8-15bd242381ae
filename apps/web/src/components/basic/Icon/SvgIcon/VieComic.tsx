import React from 'react';

const VieComic = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={32}
        height={24}
        viewBox="0 0 32 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.9002 14.6921V13.4991C17.9002 12.319 17.6646 11.4137 17.1838 10.8123C16.9334 10.5039 16.6144 10.2585 16.2524 10.0957C15.8904 9.93284 15.4954 9.85716 15.0989 9.87466C14.7116 9.85681 14.326 9.93619 13.9772 10.1056C13.6283 10.275 13.3272 10.529 13.1012 10.8446C12.6236 11.4913 12.388 12.3836 12.3848 13.5638V15.342C12.3848 16.4316 12.6075 17.2819 13.0625 17.8542C13.3095 18.154 13.6233 18.3916 13.9787 18.5477C14.3341 18.7038 14.7211 18.7741 15.1086 18.753C15.4588 18.7732 15.8094 18.7211 16.1386 18.5998C16.4678 18.4786 16.7686 18.2908 17.0224 18.0482C17.5254 17.4703 17.8128 16.7356 17.8357 15.9693V15.9111H16.3963V15.9596C16.3697 16.3495 16.2405 16.7254 16.0219 17.0491C15.9235 17.1673 15.7984 17.2603 15.657 17.3205C15.5156 17.3806 15.3619 17.4062 15.2087 17.3951C15.0224 17.4066 14.8367 17.3638 14.6741 17.272C14.5115 17.1803 14.3788 17.0433 14.2921 16.8778C14.0556 16.3382 13.9548 15.7487 13.9984 15.161V14.6663L17.9002 14.6921ZM16.3511 12.9882V13.4765H14.0081V13.2404C13.9745 12.7076 14.086 12.1756 14.3308 11.7014C14.4292 11.5491 14.567 11.4266 14.7297 11.3468C14.8923 11.2671 15.0735 11.2332 15.2538 11.2488C15.4198 11.2445 15.5839 11.2853 15.7287 11.3666C15.8736 11.448 15.9938 11.567 16.0768 11.7111C16.2905 12.101 16.3858 12.5449 16.3511 12.9882Z"
          fill="white"
        />
        <path
          d="M8.72985 6.68833C8.8243 6.68834 8.91493 6.65097 8.98202 6.58437C9.04911 6.51776 9.08723 6.4273 9.08808 6.33268V5.72161C9.08851 5.67463 9.07964 5.62803 9.06199 5.58451C9.04434 5.54099 9.01826 5.5014 8.98525 5.46803C8.95224 5.43466 8.91296 5.40817 8.86968 5.3901C8.8264 5.37202 8.77997 5.36272 8.73308 5.36272H5.76719C5.67273 5.36272 5.5821 5.40008 5.51501 5.46669C5.44793 5.5333 5.40981 5.62375 5.40896 5.71837V6.31652C5.40895 6.41114 5.44625 6.50194 5.51274 6.56915C5.57922 6.63636 5.66951 6.67455 5.76396 6.6754H6.29324C6.32386 6.67335 6.35453 6.67875 6.38263 6.69113C6.41073 6.70352 6.43542 6.72252 6.4546 6.74653C6.47159 6.76939 6.48306 6.79589 6.4881 6.82394C6.49314 6.85199 6.49161 6.88083 6.48365 6.90819L4.56663 15.2272C4.56775 15.2445 4.56775 15.2617 4.56663 15.279C4.56551 15.2617 4.56551 15.2445 4.56663 15.2272L2.71739 6.90819C2.71054 6.88001 2.70987 6.85066 2.71545 6.82219C2.72102 6.79373 2.73269 6.76681 2.74966 6.7433C2.76733 6.71882 2.79065 6.69898 2.81762 6.68547C2.84459 6.67196 2.87442 6.66518 2.90457 6.6657H3.45644C3.50333 6.66613 3.54984 6.65725 3.59328 6.63956C3.63673 6.62188 3.67625 6.59575 3.70955 6.56268C3.74286 6.52962 3.7693 6.49027 3.78734 6.4469C3.80538 6.40354 3.81467 6.35703 3.81467 6.31005V5.69897C3.81467 5.60435 3.77737 5.51355 3.71089 5.44634C3.64441 5.37913 3.55412 5.34094 3.45967 5.34009H0.35823C0.265953 5.33999 0.177198 5.37558 0.110462 5.43942C0.0437259 5.50327 0.00415661 5.59046 2.95402e-10 5.68281L2.95402e-10 6.29388C-3.83275e-06 6.38851 0.0372949 6.47931 0.10378 6.54652C0.170264 6.61373 0.260555 6.65192 0.355003 6.65277H0.580914C0.654518 6.65652 0.725132 6.68312 0.782962 6.72889C0.840792 6.77466 0.88297 6.83732 0.903644 6.90819L3.2273 16.2392L3.87276 18.7644H5.16368L8.00693 6.94052C8.02761 6.86966 8.06979 6.80699 8.12762 6.76122C8.18545 6.71545 8.25606 6.68885 8.32966 6.6851L8.72985 6.68833Z"
          fill="white"
        />
        <path
          d="M11.1086 17.494H10.8279C10.7617 17.4931 10.6985 17.4662 10.652 17.419C10.6054 17.3718 10.5794 17.3081 10.5794 17.2418L10.6084 10.5555C10.6076 10.4606 10.5696 10.3698 10.5026 10.3027C10.4356 10.2356 10.3449 10.1975 10.2502 10.1967H8.48162C8.38717 10.1967 8.29654 10.234 8.22945 10.3006C8.16236 10.3672 8.12424 10.4577 8.12339 10.5523V11.1117C8.12296 11.1586 8.13183 11.2052 8.14948 11.2487C8.16713 11.2923 8.19321 11.3319 8.22622 11.3652C8.25923 11.3986 8.29851 11.4251 8.34179 11.4432C8.38507 11.4612 8.4315 11.4705 8.47839 11.4705H8.75917C8.82508 11.4705 8.88828 11.4968 8.93489 11.5435C8.98149 11.5901 9.00767 11.6535 9.00767 11.7195L8.98508 17.245C8.98508 17.278 8.97855 17.3106 8.96586 17.3411C8.95317 17.3715 8.93458 17.3991 8.91116 17.4222C8.88774 17.4454 8.85996 17.4637 8.82944 17.476C8.79892 17.4883 8.76626 17.4944 8.73335 17.494C8.73335 17.494 8.08789 17.6524 8.08789 17.8497V18.7743H11.4604V17.8529C11.4604 17.7588 11.4236 17.6685 11.3578 17.6014C11.292 17.5343 11.2025 17.4957 11.1086 17.494Z"
          fill="white"
        />
        <path
          d="M30.7849 18.5178C30.7849 17.7831 31.3291 17.1866 32.0005 17.1866H21.3723C20.7009 17.1866 20.1562 17.7831 20.1562 18.5178C20.1562 19.2536 20.7009 19.85 21.3723 19.85H32.0005C31.3291 19.85 30.7849 19.2536 30.7849 18.5178Z"
          fill="#BABBBC"
        />
        <path
          d="M21.3714 17.4508H31.9996V4H21.3714C20.7 4 19.9409 4.5954 19.9409 5.33114L19.9141 18.5719L19.9153 18.57C19.9406 19.4261 20.584 20.1141 21.3714 20.1141H31.9996V19.5858H21.3714C20.8338 19.5858 20.3965 19.107 20.3965 18.5177C20.3965 17.9296 20.8338 17.4508 21.3714 17.4508Z"
          fill="#5D8B3F"
        />
        <path d="M21.3711 4H31.9993V16.9224H21.3711V4Z" fill="url(#paint0_linear_33011_630662)" />
        <defs>
          <linearGradient
            id="paint0_linear_33011_630662"
            x1="21.4244"
            y1={4}
            x2="31.9993"
            y2="17.5963"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#BFDD69" />
            <stop offset={1} stopColor="#8AC64B" />
          </linearGradient>
        </defs>
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={32}
        height={24}
        viewBox="0 0 32 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.9002 14.692V13.499C17.9002 12.3188 17.6646 11.4135 17.1838 10.8122C16.9334 10.5038 16.6144 10.2583 16.2524 10.0955C15.8904 9.93272 15.4954 9.85704 15.0989 9.87454C14.7116 9.85669 14.326 9.93607 13.9772 10.1055C13.6283 10.2749 13.3272 10.5289 13.1012 10.8445C12.6236 11.4911 12.388 12.3835 12.3848 13.5636V15.3419C12.3848 16.4315 12.6075 17.2818 13.0625 17.8541C13.3095 18.1539 13.6233 18.3915 13.9787 18.5476C14.3341 18.7037 14.7211 18.774 15.1086 18.7529C15.4588 18.7731 15.8094 18.7209 16.1386 18.5997C16.4678 18.4784 16.7686 18.2907 17.0224 18.0481C17.5254 17.4702 17.8128 16.7355 17.8357 15.9691V15.9109H16.3963V15.9594C16.3697 16.3494 16.2405 16.7253 16.0219 17.049C15.9235 17.1672 15.7984 17.2602 15.657 17.3204C15.5156 17.3805 15.3619 17.4061 15.2087 17.395C15.0224 17.4064 14.8367 17.3637 14.6741 17.2719C14.5115 17.1801 14.3788 17.0432 14.2921 16.8777C14.0556 16.338 13.9548 15.7486 13.9984 15.1608V14.6662L17.9002 14.692ZM16.3511 12.9881V13.4763H14.0081V13.2403C13.9745 12.7075 14.086 12.1755 14.3308 11.7013C14.4292 11.549 14.567 11.4264 14.7297 11.3467C14.8923 11.267 15.0735 11.2331 15.2538 11.2487C15.4198 11.2444 15.5839 11.2851 15.7287 11.3665C15.8736 11.4479 15.9938 11.5669 16.0768 11.711C16.2905 12.1008 16.3858 12.5447 16.3511 12.9881Z"
          fill="#CCCCCC"
        />
        <path
          d="M8.72985 6.68821C8.8243 6.68822 8.91493 6.65085 8.98202 6.58424C9.04911 6.51764 9.08723 6.42718 9.08808 6.33256V5.72148C9.08851 5.67451 9.07964 5.62791 9.06199 5.58439C9.04434 5.54087 9.01826 5.50128 8.98525 5.46791C8.95224 5.43454 8.91296 5.40805 8.86968 5.38998C8.8264 5.3719 8.77997 5.3626 8.73308 5.3626H5.76719C5.67273 5.36259 5.5821 5.39996 5.51501 5.46657C5.44793 5.53317 5.40981 5.62363 5.40896 5.71825V6.31639C5.40895 6.41102 5.44625 6.50182 5.51274 6.56903C5.57922 6.63624 5.66951 6.67443 5.76396 6.67528H6.29324C6.32386 6.67323 6.35453 6.67862 6.38263 6.69101C6.41073 6.70339 6.43542 6.7224 6.4546 6.74641C6.47159 6.76927 6.48306 6.79577 6.4881 6.82382C6.49314 6.85187 6.49161 6.88071 6.48365 6.90807L4.56663 15.2271C4.56775 15.2443 4.56775 15.2616 4.56663 15.2788C4.56551 15.2616 4.56551 15.2443 4.56663 15.2271L2.71739 6.90807C2.71054 6.87988 2.70987 6.85054 2.71545 6.82207C2.72102 6.7936 2.73269 6.76669 2.74966 6.74318C2.76733 6.7187 2.79065 6.69886 2.81762 6.68535C2.84459 6.67184 2.87442 6.66506 2.90457 6.66558H3.45644C3.50333 6.66601 3.54984 6.65712 3.59328 6.63944C3.63673 6.62176 3.67625 6.59563 3.70955 6.56256C3.74286 6.52949 3.7693 6.49014 3.78734 6.44678C3.80538 6.40342 3.81467 6.35691 3.81467 6.30993V5.69885C3.81467 5.60423 3.77737 5.51343 3.71089 5.44622C3.64441 5.37901 3.55412 5.34082 3.45967 5.33997H0.35823C0.265953 5.33987 0.177198 5.37546 0.110462 5.4393C0.0437259 5.50315 0.00415661 5.59033 2.95402e-10 5.68269L2.95402e-10 6.29376C-3.83275e-06 6.38839 0.0372949 6.47918 0.10378 6.5464C0.170264 6.61361 0.260555 6.6518 0.355003 6.65265H0.580914C0.654518 6.65639 0.725132 6.683 0.782962 6.72877C0.840792 6.77454 0.88297 6.8372 0.903644 6.90807L3.2273 16.2391L3.87276 18.7642H5.16368L8.00693 6.9404C8.02761 6.86953 8.06979 6.80687 8.12762 6.7611C8.18545 6.71533 8.25606 6.68873 8.32966 6.68498L8.72985 6.68821Z"
          fill="#CCCCCC"
        />
        <path
          d="M11.1086 17.494H10.8279C10.7617 17.4931 10.6985 17.4662 10.652 17.419C10.6054 17.3718 10.5794 17.3081 10.5794 17.2418L10.6084 10.5555C10.6076 10.4606 10.5696 10.3698 10.5026 10.3027C10.4356 10.2356 10.3449 10.1975 10.2502 10.1967H8.48162C8.38717 10.1967 8.29654 10.234 8.22945 10.3006C8.16236 10.3672 8.12424 10.4577 8.12339 10.5523V11.1117C8.12296 11.1586 8.13183 11.2052 8.14948 11.2487C8.16713 11.2923 8.19321 11.3319 8.22622 11.3652C8.25923 11.3986 8.29851 11.4251 8.34179 11.4432C8.38507 11.4612 8.4315 11.4705 8.47839 11.4705H8.75917C8.82508 11.4705 8.88828 11.4968 8.93489 11.5435C8.98149 11.5901 9.00767 11.6535 9.00767 11.7195L8.98508 17.245C8.98508 17.278 8.97855 17.3106 8.96586 17.3411C8.95317 17.3715 8.93458 17.3991 8.91116 17.4222C8.88774 17.4454 8.85996 17.4637 8.82944 17.476C8.79892 17.4883 8.76626 17.4944 8.73335 17.494C8.73335 17.494 8.08789 17.6524 8.08789 17.8497V18.7743H11.4604V17.8529C11.4604 17.7588 11.4236 17.6685 11.3578 17.6014C11.292 17.5343 11.2025 17.4957 11.1086 17.494Z"
          fill="#CCCCCC"
        />
        <path
          d="M30.7829 18.5178C30.7829 17.7831 31.3272 17.1866 31.9986 17.1866H21.3704C20.699 17.1866 20.1543 17.7831 20.1543 18.5178C20.1543 19.2536 20.699 19.85 21.3704 19.85H31.9986C31.3272 19.85 30.7829 19.2536 30.7829 18.5178Z"
          fill="#BABBBC"
        />
        <path
          d="M21.3714 17.4508H31.9996V4H21.3714C20.7 4 19.9409 4.5954 19.9409 5.33114L19.9141 18.5719L19.9153 18.57C19.9406 19.4261 20.584 20.1141 21.3714 20.1141H31.9996V19.5858H21.3714C20.8338 19.5858 20.3965 19.107 20.3965 18.5177C20.3965 17.9296 20.8338 17.4508 21.3714 17.4508Z"
          fill="#8C8C8C"
        />
        <path d="M21.3711 4H31.9993V16.9224H21.3711V4Z" fill="url(#paint0_linear_33011_635441)" />
        <g opacity="0.3">
          <path
            d="M17.9 14.6921V13.4991C17.9 12.3189 17.6644 11.4136 17.1835 10.8123C16.9331 10.5039 16.6142 10.2584 16.2521 10.0956C15.8901 9.93281 15.4951 9.85713 15.0987 9.87463C14.7114 9.85678 14.3258 9.93616 13.9769 10.1056C13.628 10.2749 13.3269 10.529 13.101 10.8446C12.6233 11.4912 12.3877 12.3836 12.3845 13.5637V15.342C12.3845 16.4316 12.6072 17.2819 13.0622 17.8542C13.3093 18.154 13.6231 18.3915 13.9785 18.5477C14.3339 18.7038 14.7209 18.7741 15.1084 18.753C15.4585 18.7732 15.8091 18.721 16.1383 18.5998C16.4676 18.4785 16.7684 18.2908 17.0221 18.0482C17.5251 17.4703 17.8125 16.7356 17.8354 15.9692V15.911H16.396V15.9595C16.3694 16.3495 16.2402 16.7254 16.0217 17.0491C15.9232 17.1673 15.7981 17.2603 15.6567 17.3205C15.5153 17.3806 15.3616 17.4062 15.2084 17.3951C15.0221 17.4065 14.8365 17.3638 14.6739 17.272C14.5113 17.1802 14.3786 17.0433 14.2918 16.8778C14.0553 16.3381 13.9545 15.7486 13.9982 15.1609V14.6662L17.9 14.6921ZM16.3509 12.9882V13.4764H14.0078V13.2404C13.9742 12.7076 14.0858 12.1756 14.3306 11.7014C14.4289 11.5491 14.5668 11.4265 14.7294 11.3468C14.892 11.267 15.0732 11.2331 15.2536 11.2487C15.4196 11.2445 15.5837 11.2852 15.7285 11.3666C15.8733 11.448 15.9936 11.567 16.0765 11.7111C16.2902 12.1009 16.3856 12.5448 16.3509 12.9882Z"
            fill="black"
          />
          <path
            d="M8.72985 6.68826C8.8243 6.68826 8.91493 6.6509 8.98202 6.58429C9.04911 6.51768 9.08723 6.42723 9.08808 6.33261V5.72153C9.08851 5.67456 9.07964 5.62796 9.06199 5.58444C9.04434 5.54091 9.01826 5.50132 8.98525 5.46795C8.95224 5.43459 8.91296 5.4081 8.86968 5.39002C8.8264 5.37195 8.77997 5.36264 8.73308 5.36265H5.76719C5.67274 5.36264 5.5821 5.40001 5.51501 5.46662C5.44793 5.53322 5.40981 5.62368 5.40896 5.7183V6.31644C5.40895 6.41107 5.44625 6.50186 5.51274 6.56908C5.57922 6.63629 5.66951 6.67448 5.76396 6.67533H6.29324C6.32386 6.67328 6.35453 6.67867 6.38263 6.69106C6.41073 6.70344 6.43542 6.72245 6.4546 6.74646C6.47159 6.76932 6.48306 6.79582 6.4881 6.82387C6.49314 6.85192 6.49161 6.88076 6.48365 6.90812L4.56663 15.2272C4.56775 15.2444 4.56775 15.2617 4.56663 15.2789C4.56551 15.2617 4.56551 15.2444 4.56663 15.2272L2.71739 6.90812C2.71054 6.87993 2.70987 6.85059 2.71545 6.82212C2.72102 6.79365 2.73269 6.76673 2.74966 6.74322C2.76733 6.71875 2.79065 6.69891 2.81762 6.6854C2.84459 6.67189 2.87442 6.6651 2.90457 6.66563H3.45644C3.50333 6.66605 3.54984 6.65717 3.59328 6.63949C3.63673 6.62181 3.67625 6.59568 3.70955 6.56261C3.74286 6.52954 3.7693 6.49019 3.78734 6.44683C3.80538 6.40347 3.81467 6.35695 3.81467 6.30998V5.6989C3.81467 5.60427 3.77737 5.51348 3.71089 5.44627C3.64441 5.37905 3.55412 5.34087 3.45967 5.34001H0.35823C0.265953 5.33992 0.177198 5.3755 0.110462 5.43935C0.0437259 5.5032 0.00415661 5.59038 2.94676e-10 5.68273V6.29381C-3.83275e-06 6.38843 0.0372949 6.47923 0.10378 6.54644C0.170264 6.61366 0.260555 6.65184 0.355003 6.6527H0.580914C0.654518 6.65644 0.725132 6.68305 0.782962 6.72882C0.840792 6.77458 0.88297 6.83725 0.903644 6.90812L3.2273 16.2392L3.87276 18.7643H5.16368L8.00693 6.94045C8.02761 6.86958 8.06979 6.80692 8.12762 6.76115C8.18545 6.71538 8.25606 6.68877 8.32966 6.68503L8.72985 6.68826Z"
            fill="black"
          />
          <path
            d="M11.1092 17.4941H10.8285C10.7623 17.4932 10.6991 17.4663 10.6525 17.4191C10.606 17.3719 10.58 17.3082 10.58 17.2419L10.609 10.5556C10.6082 10.4607 10.5702 10.3699 10.5032 10.3028C10.4362 10.2356 10.3455 10.1976 10.2508 10.1967H8.48222C8.38776 10.1967 8.29713 10.2341 8.23004 10.3007C8.16295 10.3673 8.12484 10.4578 8.12399 10.5524V11.1117C8.12356 11.1587 8.13243 11.2053 8.15008 11.2488C8.16773 11.2923 8.19381 11.3319 8.22682 11.3653C8.25982 11.3987 8.2991 11.4251 8.34239 11.4432C8.38567 11.4613 8.4321 11.4706 8.47899 11.4706H8.75976C8.82567 11.4706 8.88888 11.4968 8.93548 11.5435C8.98209 11.5902 9.00827 11.6535 9.00827 11.7196L8.98567 17.2451C8.98568 17.2781 8.97914 17.3107 8.96645 17.3411C8.95376 17.3715 8.93517 17.3991 8.91175 17.4223C8.88834 17.4455 8.86056 17.4637 8.83003 17.476C8.79951 17.4884 8.76685 17.4945 8.73394 17.4941C8.73394 17.4941 8.08848 17.6525 8.08848 17.8497V18.7744H11.461V17.8529C11.461 17.7589 11.4242 17.6686 11.3584 17.6014C11.2926 17.5343 11.2031 17.4958 11.1092 17.4941Z"
            fill="black"
          />
          <path
            d="M30.7842 18.5178C30.7842 17.7831 31.3284 17.1867 31.9998 17.1867H21.3716C20.7002 17.1867 20.1555 17.7831 20.1555 18.5178C20.1555 19.2536 20.7002 19.85 21.3716 19.85H31.9998C31.3284 19.85 30.7842 19.2536 30.7842 18.5178Z"
            fill="black"
          />
          <path
            d="M21.3717 17.4508H31.9999V4H21.3717C20.7003 4 19.9412 4.5954 19.9412 5.33114L19.9144 18.5719L19.9156 18.57C19.941 19.4261 20.5844 20.1141 21.3717 20.1141H31.9999V19.5858H21.3717C20.8341 19.5858 20.3969 19.107 20.3969 18.5177C20.3969 17.9296 20.8341 17.4508 21.3717 17.4508Z"
            fill="black"
          />
          <path d="M21.3717 4H31.9999L32 16.9224H21.3717L21.3717 4Z" fill="black" />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_33011_635441"
            x1="21.4244"
            y1={4}
            x2="31.9993"
            y2="17.5963"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#D9D9D9" />
            <stop offset={1} stopColor="#C0C0C0" />
          </linearGradient>
        </defs>
      </svg>
    );
  }
  return (
    <svg width={32} height={24} viewBox="0 0 32 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M17.9002 14.692V13.499C17.9002 12.3188 17.6646 11.4135 17.1838 10.8122C16.9334 10.5038 16.6144 10.2583 16.2524 10.0955C15.8904 9.93272 15.4954 9.85704 15.0989 9.87454C14.7116 9.85669 14.326 9.93607 13.9772 10.1055C13.6283 10.2749 13.3272 10.5289 13.1012 10.8445C12.6236 11.4911 12.388 12.3835 12.3848 13.5636V15.3419C12.3848 16.4315 12.6075 17.2818 13.0625 17.8541C13.3095 18.1539 13.6233 18.3915 13.9787 18.5476C14.3341 18.7037 14.7211 18.774 15.1086 18.7529C15.4588 18.7731 15.8094 18.7209 16.1386 18.5997C16.4678 18.4784 16.7686 18.2907 17.0224 18.0481C17.5254 17.4702 17.8128 16.7355 17.8357 15.9691V15.9109H16.3963V15.9594C16.3697 16.3494 16.2405 16.7253 16.0219 17.049C15.9235 17.1672 15.7984 17.2602 15.657 17.3204C15.5156 17.3805 15.3619 17.4061 15.2087 17.395C15.0224 17.4064 14.8367 17.3637 14.6741 17.2719C14.5115 17.1801 14.3788 17.0432 14.2921 16.8777C14.0556 16.338 13.9548 15.7486 13.9984 15.1608V14.6662L17.9002 14.692ZM16.3511 12.9881V13.4763H14.0081V13.2403C13.9745 12.7075 14.086 12.1755 14.3308 11.7013C14.4292 11.549 14.567 11.4264 14.7297 11.3467C14.8923 11.267 15.0735 11.2331 15.2538 11.2487C15.4198 11.2444 15.5839 11.2851 15.7287 11.3665C15.8736 11.4479 15.9938 11.5669 16.0768 11.711C16.2905 12.1008 16.3858 12.5447 16.3511 12.9881Z"
        fill="#CCCCCC"
      />
      <path
        d="M8.72985 6.68821C8.8243 6.68822 8.91493 6.65085 8.98202 6.58424C9.04911 6.51764 9.08723 6.42718 9.08808 6.33256V5.72148C9.08851 5.67451 9.07964 5.62791 9.06199 5.58439C9.04434 5.54087 9.01826 5.50128 8.98525 5.46791C8.95224 5.43454 8.91296 5.40805 8.86968 5.38998C8.8264 5.3719 8.77997 5.3626 8.73308 5.3626H5.76719C5.67273 5.36259 5.5821 5.39996 5.51501 5.46657C5.44793 5.53317 5.40981 5.62363 5.40896 5.71825V6.31639C5.40895 6.41102 5.44625 6.50182 5.51274 6.56903C5.57922 6.63624 5.66951 6.67443 5.76396 6.67528H6.29324C6.32386 6.67323 6.35453 6.67862 6.38263 6.69101C6.41073 6.70339 6.43542 6.7224 6.4546 6.74641C6.47159 6.76927 6.48306 6.79577 6.4881 6.82382C6.49314 6.85187 6.49161 6.88071 6.48365 6.90807L4.56663 15.2271C4.56775 15.2443 4.56775 15.2616 4.56663 15.2788C4.56551 15.2616 4.56551 15.2443 4.56663 15.2271L2.71739 6.90807C2.71054 6.87988 2.70987 6.85054 2.71545 6.82207C2.72102 6.7936 2.73269 6.76669 2.74966 6.74318C2.76733 6.7187 2.79065 6.69886 2.81762 6.68535C2.84459 6.67184 2.87442 6.66506 2.90457 6.66558H3.45644C3.50333 6.66601 3.54984 6.65712 3.59328 6.63944C3.63673 6.62176 3.67625 6.59563 3.70955 6.56256C3.74286 6.52949 3.7693 6.49014 3.78734 6.44678C3.80538 6.40342 3.81467 6.35691 3.81467 6.30993V5.69885C3.81467 5.60423 3.77737 5.51343 3.71089 5.44622C3.64441 5.37901 3.55412 5.34082 3.45967 5.33997H0.35823C0.265953 5.33987 0.177198 5.37546 0.110462 5.4393C0.0437259 5.50315 0.00415661 5.59033 2.95402e-10 5.68269L2.95402e-10 6.29376C-3.83275e-06 6.38839 0.0372949 6.47918 0.10378 6.5464C0.170264 6.61361 0.260555 6.6518 0.355003 6.65265H0.580914C0.654518 6.65639 0.725132 6.683 0.782962 6.72877C0.840792 6.77454 0.88297 6.8372 0.903644 6.90807L3.2273 16.2391L3.87276 18.7642H5.16368L8.00693 6.9404C8.02761 6.86953 8.06979 6.80687 8.12762 6.7611C8.18545 6.71533 8.25606 6.68873 8.32966 6.68498L8.72985 6.68821Z"
        fill="#CCCCCC"
      />
      <path
        d="M11.1086 17.494H10.8279C10.7617 17.4931 10.6985 17.4662 10.652 17.419C10.6054 17.3718 10.5794 17.3081 10.5794 17.2418L10.6084 10.5555C10.6076 10.4606 10.5696 10.3698 10.5026 10.3027C10.4356 10.2356 10.3449 10.1975 10.2502 10.1967H8.48162C8.38717 10.1967 8.29654 10.234 8.22945 10.3006C8.16236 10.3672 8.12424 10.4577 8.12339 10.5523V11.1117C8.12296 11.1586 8.13183 11.2052 8.14948 11.2487C8.16713 11.2923 8.19321 11.3319 8.22622 11.3652C8.25923 11.3986 8.29851 11.4251 8.34179 11.4432C8.38507 11.4612 8.4315 11.4705 8.47839 11.4705H8.75917C8.82508 11.4705 8.88828 11.4968 8.93489 11.5435C8.98149 11.5901 9.00767 11.6535 9.00767 11.7195L8.98508 17.245C8.98508 17.278 8.97855 17.3106 8.96586 17.3411C8.95317 17.3715 8.93458 17.3991 8.91116 17.4222C8.88774 17.4454 8.85996 17.4637 8.82944 17.476C8.79892 17.4883 8.76626 17.4944 8.73335 17.494C8.73335 17.494 8.08789 17.6524 8.08789 17.8497V18.7743H11.4604V17.8529C11.4604 17.7588 11.4236 17.6685 11.3578 17.6014C11.292 17.5343 11.2025 17.4957 11.1086 17.494Z"
        fill="#CCCCCC"
      />
      <path
        d="M30.7829 18.5178C30.7829 17.7831 31.3272 17.1866 31.9986 17.1866H21.3704C20.699 17.1866 20.1543 17.7831 20.1543 18.5178C20.1543 19.2536 20.699 19.85 21.3704 19.85H31.9986C31.3272 19.85 30.7829 19.2536 30.7829 18.5178Z"
        fill="#BABBBC"
      />
      <path
        d="M21.3714 17.4508H31.9996V4H21.3714C20.7 4 19.9409 4.5954 19.9409 5.33114L19.9141 18.5719L19.9153 18.57C19.9406 19.4261 20.584 20.1141 21.3714 20.1141H31.9996V19.5858H21.3714C20.8338 19.5858 20.3965 19.107 20.3965 18.5177C20.3965 17.9296 20.8338 17.4508 21.3714 17.4508Z"
        fill="#8C8C8C"
      />
      <path d="M21.3711 4H31.9993V16.9224H21.3711V4Z" fill="url(#paint0_linear_33011_631221)" />
      <defs>
        <linearGradient
          id="paint0_linear_33011_631221"
          x1="21.4244"
          y1={4}
          x2="31.9993"
          y2="17.5963"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#D9D9D9" />
          <stop offset={1} stopColor="#C0C0C0" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default VieComic;

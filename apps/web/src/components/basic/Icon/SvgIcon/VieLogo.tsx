import React from 'react';

const VieLogo = () => (
  <svg width={93} height={29} viewBox="0 0 93 29" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Logo - flat" clipPath="url(#clip0_4681_87224)">
      <g id="Group">
        <g id="Group_2">
          <path
            id="Vector"
            d="M48.491 0.767309C44.2511 2.1097 40.8505 5.41242 39.4515 9.54615C38.8704 11.2508 38.6121 12.9554 38.6121 14.5748C38.5906 18.1332 39.9034 21.5425 42.4431 24.206C42.5077 24.2699 42.5508 24.3338 42.6153 24.3977C42.6368 24.419 42.6584 24.4617 42.7014 24.483C42.7229 24.5043 42.7445 24.5256 42.766 24.5256C46.4894 28.3184 52.2791 30.1295 58.37 28.2118C59.2094 27.9348 60.0273 27.5939 60.7806 27.1891C63.9014 25.6123 66.355 22.8423 67.4526 19.4756C67.9907 17.8562 68.2275 16.2794 68.2275 14.7453C68.3996 5.0715 58.865 -2.49279 48.491 0.767309Z"
            fill="white"
          />
          <path
            id="Vector_2"
            d="M89.7931 3.47278L89.707 25.0789C89.707 27.572 90.5464 25.9313 90.8908 25.0363C92.2037 21.7762 93 18.4948 93 14.2972C93.0216 10.0995 92.2683 6.79681 90.9769 3.5367C90.6325 2.62047 89.7931 0.425757 89.7931 3.47278Z"
            fill="white"
          />
          <path
            id="Vector_3"
            d="M71.6498 23.1199C71.6498 26.7209 66.2476 26.8701 66.2476 26.8701L79.7639 26.9127C80.7324 26.9127 82.1744 26.9553 82.6264 26.7422C83.4228 26.3587 83.4228 25.7621 83.0353 25.2933L71.6928 11.6562C71.6928 14.2558 71.6713 18.006 71.6498 20.2007C71.6713 20.5842 71.6498 20.9891 71.6498 23.1199Z"
            fill="white"
          />
          <path
            id="Vector_4"
            d="M67.1509 0.958984C67.1509 0.958984 70.3147 0.980292 72.5101 3.62247C73.0912 4.32563 82.5182 15.6401 82.5182 15.6401C83.0132 16.2367 83.9817 15.8958 83.9817 15.1287L84.0247 3.19631C84.0247 2.28007 83.3791 1.47037 82.4536 1.2786C81.6142 1.10814 80.7533 1.0016 79.8709 1.0016L67.1509 0.958984Z"
            fill="white"
          />
          <path
            id="Vector_5"
            d="M55.5408 21.5849C48.9549 23.6944 42.9931 17.7282 45.1669 11.2293C45.9417 8.92805 47.7926 7.09557 50.1386 6.3498C56.7246 4.24032 62.6864 10.2065 60.5126 16.7054C59.7163 19.0067 57.8653 20.8391 55.5408 21.5849Z"
            fill="url(#paint0_linear_4681_87224)"
          />
          <path
            id="Vector_6"
            d="M48.491 0.767309C44.2511 2.1097 40.8505 5.41242 39.4515 9.54615C38.8704 11.2508 38.6121 12.9554 38.6121 14.5748C38.5906 18.1332 39.9034 21.5425 42.4431 24.206C42.5077 24.2699 42.5508 24.3338 42.6153 24.3977C42.6368 24.419 42.6584 24.4617 42.7014 24.483C42.7229 24.5043 42.7445 24.5256 42.766 24.5256C46.4894 28.3184 52.2791 30.1295 58.37 28.2118C59.2094 27.9348 60.0273 27.5939 60.7806 27.1891C63.9014 25.6123 66.355 22.8423 67.4526 19.4756C67.9907 17.8562 68.2275 16.2794 68.2275 14.7453C68.3996 5.0715 58.865 -2.49279 48.491 0.767309ZM57.2724 27.5726C55.6797 28.084 54.087 28.3184 52.5158 28.2971C48.9861 28.2118 45.65 26.8268 43.0888 24.2699C39.3439 20.5197 38.1386 15.1927 39.8819 10.0149C41.1948 6.11558 44.3371 3.02594 48.2973 1.76878C49.8255 1.2787 51.3536 1.04431 52.8602 1.04431C56.4545 1.06562 59.8766 2.47194 62.4809 5.0715C66.2258 8.82168 67.4311 14.1486 65.6878 19.3051C64.3749 23.2258 61.2325 26.3154 57.2724 27.5726Z"
            fill="url(#paint1_linear_4681_87224)"
          />
        </g>
        <g id="Group_3">
          <g id="Group_4">
            <g id="Group_5">
              <path
                id="Vector_7"
                d="M35.3622 18.8147V16.4922C35.3622 14.1909 34.8887 12.4224 33.9417 11.2504C32.9732 10.0359 31.5742 9.41797 29.8309 9.41797C28.1521 9.41797 26.8392 10.0572 25.8922 11.3357C24.9452 12.6141 24.4932 14.3401 24.4717 16.6413L24.4502 20.1145C24.4502 22.2453 24.9022 23.886 25.7846 25.0153C26.7101 26.1659 28.066 26.7626 29.8309 26.7626C31.4236 26.7626 32.6504 26.3151 33.6189 25.3776C34.5229 24.44 35.0609 23.0763 35.2331 21.3291V21.2225L32.3921 21.2012L32.3706 21.2864C32.263 22.2027 32.0047 22.9272 31.6173 23.4172C31.2083 23.886 30.7133 24.0991 30.0246 24.0991C29.1852 24.0991 28.6041 23.7795 28.2167 23.0976C27.8078 22.4158 27.6356 21.3078 27.6356 19.7523V18.7934L35.3622 18.8147ZM32.306 15.4694V16.4283L27.6786 16.4069V15.9382C27.6786 14.5319 27.8723 13.5517 28.2812 12.9338C28.6902 12.3158 29.2713 12.0388 30.1107 12.0388C30.8209 12.0388 31.3375 12.3371 31.7464 12.9338C32.1123 13.5304 32.306 14.3827 32.306 15.4694Z"
                fill="white"
              />
              <path
                id="Vector_8"
                d="M17.2398 3.06778C17.6272 3.06778 17.95 2.74816 17.95 2.36462V1.17138C17.95 0.787838 17.6272 0.46822 17.2398 0.46822L11.3856 0.446913C10.9981 0.446913 10.6753 0.766531 10.6753 1.15007V2.34331C10.6753 2.72685 10.9981 3.04647 11.3856 3.04647H12.4402C12.5693 3.04647 12.6769 3.08909 12.7415 3.17432C12.8061 3.25955 12.8276 3.36609 12.8061 3.49394L9.01805 19.7305C9.01805 19.7731 8.99653 19.7945 8.99653 19.8371C8.99653 19.8158 8.97501 19.7731 8.97501 19.7305L5.35918 3.47263C5.33765 3.34478 5.35918 3.23824 5.42374 3.15301C5.48831 3.06778 5.59593 3.02516 5.72506 3.02516H6.82273C7.21014 3.02516 7.53298 2.70555 7.53298 2.322V1.12876C7.53298 0.745223 7.23166 0.425605 6.84425 0.425605L0.710252 0.404297C0.322842 0.404297 0 0.723915 0 1.10746V2.3007C0 2.68424 0.322842 3.00386 0.710252 3.00386H1.16223C1.44203 3.00386 1.74335 3.23824 1.80791 3.49394L6.41379 21.7122L7.66211 26.6343H10.2233L15.8193 3.55786C15.8838 3.28086 16.1851 3.06778 16.4649 3.06778H17.2398Z"
                fill="white"
              />
              <path
                id="Vector_9"
                d="M17.8208 6.37101C17.8208 6.75455 18.1436 7.07417 18.5311 7.07417H20.1668C20.5542 7.07417 20.877 6.75455 20.877 6.37101V5.17777C20.877 4.79423 20.5542 4.47461 20.1668 4.47461H18.5311C18.1436 4.47461 17.8208 4.79423 17.8208 5.17777V6.37101Z"
                fill="white"
              />
              <path
                id="Vector_10"
                d="M21.9315 24.3544H21.3719C21.0922 24.3544 20.8769 24.1413 20.8769 23.8643L20.92 10.8026C20.92 10.4191 20.5971 10.0994 20.2097 10.0994L16.723 10.0781C16.3356 10.0781 16.0128 10.3977 16.0128 10.7813V11.868C16.0128 12.2515 16.3356 12.5711 16.723 12.5711H17.2826C17.5624 12.5711 17.7776 12.7842 17.7776 13.0612L17.7346 23.843C17.7346 24.12 17.5194 24.3331 17.2396 24.3331C17.2396 24.3331 15.9697 24.6314 15.9697 25.0362V26.8048L22.6203 26.8261V25.0576C22.6418 24.674 22.3189 24.3544 21.9315 24.3544Z"
                fill="white"
              />
            </g>
          </g>
        </g>
      </g>
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_4681_87224"
        x1="44.7089"
        y1="13.9722"
        x2="60.9498"
        y2="13.9722"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0.2083" stopColor="#85FC6E" />
        <stop offset={1} stopColor="#0AD418" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_4681_87224"
        x1="38.6196"
        y1="14.4964"
        x2="68.2361"
        y2="14.4964"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0.2083" stopColor="#85FC6E" />
        <stop offset={1} stopColor="#0AD418" />
      </linearGradient>
      <clipPath id="clip0_4681_87224">
        <rect width={93} height={29} fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export default VieLogo;

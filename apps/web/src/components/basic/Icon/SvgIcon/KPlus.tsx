import React from 'react';

const KPlus = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width="29"
        height="24"
        viewBox="0 0 29 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M20.3201 2H0V22.3198H20.3201V2Z" fill="black" />
        <path d="M29 2H20.3201V22.3198H29V2Z" fill="#D41317" />
        <path
          d="M11.5833 19.8138L6.95966 13.8739C6.78707 13.6521 6.43156 13.7742 6.43156 14.0551V19.6325C6.43156 19.7956 6.29943 19.9277 6.13646 19.9277H2.68728C2.52431 19.9277 2.39218 19.7956 2.39218 19.6325V4.6873C2.39218 4.52433 2.52431 4.3921 2.68728 4.3921H6.13646C6.29943 4.3921 6.43156 4.52433 6.43156 4.6873V9.92319C6.43156 10.2052 6.78927 10.3266 6.96094 10.1028L11.2527 4.50766C11.3086 4.43487 11.3952 4.3921 11.487 4.3921H15.7007C15.9514 4.3921 16.088 4.685 15.9267 4.87708L10.3187 11.5577C10.2272 11.6667 10.2265 11.8255 10.3171 11.9353L16.5137 19.4447C16.6726 19.6372 16.5356 19.9277 16.2861 19.9277H11.8162C11.7251 19.9277 11.6393 19.8856 11.5833 19.8138Z"
          fill="white"
        />
        <path
          d="M26.2519 10.3271H22.1486V6.22376C22.1486 6.02718 21.9893 5.86789 21.7928 5.86789H18.8473C18.6508 5.86789 18.4914 6.02718 18.4914 6.22376V10.3271H14.3882C14.1916 10.3271 14.0322 10.4864 14.0322 10.683V13.6283C14.0322 13.825 14.1916 13.9843 14.3882 13.9843H18.4914V18.0876C18.4914 18.2841 18.6508 18.4435 18.8473 18.4435H21.7928C21.9893 18.4435 22.1486 18.2841 22.1486 18.0876V13.9843H26.2519C26.4484 13.9843 26.6078 13.825 26.6078 13.6283V10.683C26.6078 10.4864 26.4484 10.3271 26.2519 10.3271Z"
          fill="white"
        />
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width="29"
        height="24"
        viewBox="0 0 29 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M20.3201 2H0V22.3198H20.3201V2Z" fill="black" />
        <path d="M29 2H20.3201V22.3198H29V2Z" fill="#D41317" />
        <path
          d="M11.5833 19.8138L6.95966 13.8739C6.78707 13.6521 6.43156 13.7742 6.43156 14.0551V19.6325C6.43156 19.7956 6.29943 19.9277 6.13646 19.9277H2.68728C2.52431 19.9277 2.39218 19.7956 2.39218 19.6325V4.6873C2.39218 4.52433 2.52431 4.3921 2.68728 4.3921H6.13646C6.29943 4.3921 6.43156 4.52433 6.43156 4.6873V9.92319C6.43156 10.2052 6.78927 10.3266 6.96094 10.1028L11.2527 4.50766C11.3086 4.43487 11.3952 4.3921 11.487 4.3921H15.7007C15.9514 4.3921 16.088 4.685 15.9267 4.87708L10.3187 11.5577C10.2272 11.6667 10.2265 11.8255 10.3171 11.9353L16.5137 19.4447C16.6726 19.6372 16.5356 19.9277 16.2861 19.9277H11.8162C11.7251 19.9277 11.6393 19.8856 11.5833 19.8138Z"
          fill="white"
        />
        <path
          d="M26.2519 10.3271H22.1486V6.22376C22.1486 6.02718 21.9893 5.86789 21.7928 5.86789H18.8473C18.6508 5.86789 18.4914 6.02718 18.4914 6.22376V10.3271H14.3882C14.1916 10.3271 14.0322 10.4864 14.0322 10.683V13.6283C14.0322 13.825 14.1916 13.9843 14.3882 13.9843H18.4914V18.0876C18.4914 18.2841 18.6508 18.4435 18.8473 18.4435H21.7928C21.9893 18.4435 22.1486 18.2841 22.1486 18.0876V13.9843H26.2519C26.4484 13.9843 26.6078 13.825 26.6078 13.6283V10.683C26.6078 10.4864 26.4484 10.3271 26.2519 10.3271Z"
          fill="white"
        />
      </svg>
    );
  }
  return (
    <svg width="29" height="24" viewBox="0 0 29 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M20.3201 2H0V22.3198H20.3201V2Z" fill="#595959" />
      <path d="M29 2H20.3201V22.3198H29V2Z" fill="#848383" />
      <path
        d="M11.5833 19.8138L6.95966 13.8739C6.78707 13.6521 6.43156 13.7742 6.43156 14.0551V19.6325C6.43156 19.7956 6.29943 19.9277 6.13646 19.9277H2.68728C2.52431 19.9277 2.39218 19.7956 2.39218 19.6325V4.6873C2.39218 4.52433 2.52431 4.3921 2.68728 4.3921H6.13646C6.29943 4.3921 6.43156 4.52433 6.43156 4.6873V9.92319C6.43156 10.2052 6.78927 10.3266 6.96094 10.1028L11.2527 4.50766C11.3086 4.43487 11.3952 4.3921 11.487 4.3921H15.7007C15.9514 4.3921 16.088 4.685 15.9267 4.87708L10.3187 11.5577C10.2272 11.6667 10.2265 11.8255 10.3171 11.9353L16.5137 19.4447C16.6726 19.6372 16.5356 19.9277 16.2861 19.9277H11.8162C11.7251 19.9277 11.6393 19.8856 11.5833 19.8138Z"
        fill="#CCCCCC"
      />
      <path
        d="M26.2519 10.3271H22.1486V6.22376C22.1486 6.02718 21.9893 5.86789 21.7928 5.86789H18.8473C18.6508 5.86789 18.4914 6.02718 18.4914 6.22376V10.3271H14.3882C14.1916 10.3271 14.0322 10.4864 14.0322 10.683V13.6283C14.0322 13.825 14.1916 13.9843 14.3882 13.9843H18.4914V18.0876C18.4914 18.2841 18.6508 18.4435 18.8473 18.4435H21.7928C21.9893 18.4435 22.1486 18.2841 22.1486 18.0876V13.9843H26.2519C26.4484 13.9843 26.6078 13.825 26.6078 13.6283V10.683C26.6078 10.4864 26.4484 10.3271 26.2519 10.3271Z"
        fill="#CCCCCC"
      />
    </svg>
  );
};

export default KPlus;

import React from 'react';

const InfoStroke = () => (
  <svg
    width="100%"
    height="100%"
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.6667 5.5C11.8427 5.5 5.5 11.8427 5.5 19.6667C5.5 27.4907 11.8426 33.8333 19.6667 33.8333C27.4907 33.8333 33.8333 27.4907 33.8333 19.6667C33.8333 11.8426 27.4907 5.5 19.6667 5.5ZM3 19.6667C3 10.4619 10.4619 3 19.6667 3C28.8715 3 36.3333 10.462 36.3333 19.6667C36.3333 28.8715 28.8715 36.3333 19.6667 36.3333C10.462 36.3333 3 28.8715 3 19.6667Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M22.3439 14.5305C22.7824 14.0952 23.0007 13.5658 23.0007 12.9459C23.0007 12.3274 22.7828 11.797 22.3439 11.3562C21.9065 10.9165 21.3793 10.696 20.7629 10.696C20.1445 10.696 19.6152 10.916 19.1736 11.3562C18.732 11.797 18.5107 12.3272 18.5107 12.9459C18.5107 13.5658 18.732 14.095 19.1736 14.5305C19.616 14.9674 20.1444 15.186 20.7629 15.186C21.3795 15.186 21.9065 14.9674 22.3439 14.5305ZM22.7283 28.0585L22.9839 27.0137C22.8516 27.0759 22.6383 27.1469 22.3459 27.2277C22.0526 27.3085 21.7887 27.3496 21.5567 27.3496C21.0626 27.3496 20.7147 27.2687 20.5128 27.1059C20.3123 26.9431 20.2123 26.6369 20.2123 26.1883C20.2123 26.0106 20.2424 25.7457 20.3055 25.399C20.3667 25.05 20.437 24.7398 20.5152 24.4685L21.4693 21.0906C21.5628 20.7806 21.6269 20.4398 21.6613 20.0678C21.6965 19.6967 21.7129 19.437 21.7129 19.2896C21.7129 18.5773 21.4633 17.9993 20.9637 17.5534C20.4642 17.1078 19.7529 16.885 18.8311 16.885C18.3182 16.885 17.7759 16.9762 17.2019 17.1583C16.628 17.3399 16.028 17.5588 15.4003 17.8144L15.144 18.8599C15.3311 18.7908 15.5539 18.7163 15.8145 18.6393C16.074 18.5626 16.3286 18.5229 16.5763 18.5229C17.0821 18.5229 17.4227 18.6091 17.6013 18.779C17.7798 18.9493 17.8695 19.2522 17.8695 19.6854C17.8695 19.9249 17.8411 20.1909 17.7823 20.4801C17.7242 20.7711 17.6518 21.0788 17.5665 21.4036L16.6083 24.795C16.5231 25.1514 16.4608 25.4703 16.4216 25.7534C16.3827 26.0362 16.364 26.3137 16.364 26.5836C16.364 27.2806 16.6216 27.8552 17.1365 28.3087C17.6514 28.7603 18.3734 28.9877 19.3016 28.9877C19.906 28.9877 20.4365 28.9087 20.8931 28.7498C21.3492 28.5915 21.9616 28.3611 22.7283 28.0585Z"
      fill="currentColor"
    />
  </svg>
);

export default InfoStroke;

import React from 'react';
import { useSelector } from 'react-redux';

const PlaybackSpeed = () => {
  const { isMobile, isTablet, size }: any = useSelector((state: any) => state?.App || {});
  return (
    <svg
      width={size || (isTablet ? '24' : isMobile ? '16' : '28')}
      height={size || (isTablet ? '24' : isMobile ? '16' : '28')}
      viewBox="0 0 32 32"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>Play back speed</title>
      <path
        d="M29 16.5C29 19.1228 28.2469 21.6662 26.8222 23.8552C26.6167 24.1708 26.1944 24.2601 25.8788 24.0547C25.5632 23.8492 25.4739 23.4269 25.6793 23.1113C26.9596 21.1443 27.6364 18.8581 27.6364 16.5C27.6364 9.80798 22.192 4.36364 15.5 4.36364C8.80798 4.36364 3.36364 9.80798 3.36364 16.5C3.36364 23.192 8.80798 28.6364 15.5 28.6364C17.8581 28.6364 20.1443 27.9596 22.1113 26.6793C22.4268 26.4739 22.8492 26.5632 23.0547 26.8788C23.2601 27.1944 23.1708 27.6167 22.8552 27.8222C20.6663 29.2469 18.1229 30 15.5 30C11.894 30 8.50389 28.5957 5.95405 26.0459C3.40422 23.4962 2 20.106 2 16.5C2 12.894 3.40427 9.50389 5.95405 6.95405C8.50384 4.40422 11.894 3 15.5 3C19.106 3 22.4961 4.40427 25.0459 6.95405C27.5958 9.50384 29 12.894 29 16.5ZM24.0955 25.0679L24.0679 25.0955C23.8013 25.3614 23.8007 25.7931 24.0666 26.0597C24.1298 26.1234 24.2051 26.1738 24.2879 26.2082C24.3708 26.2426 24.4597 26.2602 24.5494 26.2601C24.7235 26.2601 24.8977 26.1938 25.0308 26.0611L25.0611 26.0308C25.327 25.7642 25.3264 25.3324 25.0598 25.0665C24.7931 24.8006 24.3614 24.8013 24.0955 25.0679ZM21.6118 15.491C21.9588 15.7178 22.1578 16.0855 22.1578 16.5C22.1578 16.9145 21.9588 17.2823 21.6118 17.509L13.4969 22.8129C13.2942 22.9454 13.0671 23.0121 12.8389 23.0121C12.6431 23.0121 12.4465 22.963 12.2638 22.8641C11.8681 22.6501 11.632 22.2537 11.632 21.8039V11.1961C11.632 10.7463 11.8681 10.3499 12.2638 10.1359C12.6594 9.92176 13.1204 9.94091 13.4969 10.1871L21.6118 15.491ZM20.6632 16.5L12.9955 11.4885V21.5115L20.6632 16.5Z"
        fill="currentColor"
        stroke="currentColor"
      />
    </svg>
  );
};
export default PlaybackSpeed;

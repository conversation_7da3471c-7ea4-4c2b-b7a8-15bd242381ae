import React from 'react';
import { ICON_KEY } from '@constants/constants';
import dynamic from 'next/dynamic';
import InfoStroke from '@components/basic/Icon/SvgIcon/Info';
import PhonePayment from '@components/basic/Icon/SvgIcon/PhonePayment';
import PasswordPayment from '@components/basic/Icon/SvgIcon/PasswordPayment';
import IconRegisterNext from '@components/basic/Icon/SvgIcon/IconRegisterNext';
import ChatSolid from '@components/basic/Icon/SvgIcon/ChatSolid';
import IconVoucher from '@components/basic/Icon/SvgIcon/IconVoucher';

const IconHomeSvg = dynamic(() => import('./Home'));
const IconRapVietSvg = dynamic(() => import('./RapViet'));
const IconRunningManSvg = dynamic(() => import('./RunningMan'));
const IconHBOSvg = dynamic(() => import('./HBO'));
const IconKPlusSvg = dynamic(() => import('./KPlus'));
const IconKidSvg = dynamic(() => import('./Kid'));
const IconMoreSvg = dynamic(() => import('./More'));
const IconScheduleSvg = dynamic(() => import('./Schedule'));
const IconMusicSvg = dynamic(() => import('./Music'));
const IconNalaSvg = dynamic(() => import('./Nala'));
const IconNewsSvg = dynamic(() => import('./News'));
const IconPodcastSvg = dynamic(() => import('./Podcast'));
const IconReadingSvg = dynamic(() => import('./Reading'));
const IconSportSvg = dynamic(() => import('./Sport'));
const IconTelevisionSvg = dynamic(() => import('./Television'));
const IconTennisSvg = dynamic(() => import('./Tennis'));
const IconVipSvg = dynamic(() => import('./Vip'));
const IconVtvCabSvg = dynamic(() => import('./VtvCab'));
const IconVieZSvg = dynamic(() => import('./VieZ'));
const IconVTVZSvg = dynamic(() => import('./VTV'));
const IconGameSvg = dynamic(() => import('./Game'));
const IconVieComicSvg = dynamic(() => import('./VieComic'));
const IconLiveStreamSvg = dynamic(() => import('./LiveStream'));
const IconBlankFileSvg = dynamic(() => import('./BlankFile'));
const IconLaptopSvg = dynamic(() => import('./Laptop'));
const IconSmartPhoneSvg = dynamic(() => import('./SmartPhone'));
const IconEmptyInboxSvg = dynamic(() => import('./EmptyInbox'));
const IconTabletSvg = dynamic(() => import('./Tablet'));
const IconSmartTvSvg = dynamic(() => import('./SmartTv'));
const IconCircleTick = dynamic(() => import('./CircleTick'));
const IconCopy = dynamic(() => import('./Copy'));
const IconPlus = dynamic(() => import('./Plus'));
const IconEdit = dynamic(() => import('./Edit'));
const IconBack = dynamic(() => import('./Back'));
const IconKidSolid = dynamic(() => import('./KidSolid'));
const IconClock = dynamic(() => import('./Clock'));
const IconCheckAll = dynamic(() => import('./CheckAll'));
const IconCheckedAll = dynamic(() => import('./AllChecked'));
const IconLogout = dynamic(() => import('./Logout'));
const IconPhone = dynamic(() => import('./Phone'));
const IconCart = dynamic(() => import('./Cart'));
const IconRedirect = dynamic(() => import('./Redirect'));
const IconCartIndicator = dynamic(() => import('./CartIndicator'));
const IconShoppingCart = dynamic(() => import('./ShoppingCart'));
const IconList = dynamic(() => import('./List'));
const IconFAQ = dynamic(() => import('./FAQ'));
const IconCheck = dynamic(() => import('./Check'));
const IconLock = dynamic(() => import('./Lock'));
const PlaybackSpeed = dynamic(() => import('./PlaybackSpeed'));
const IconEmail = dynamic(() => import('./Email'));
const IconWarning = dynamic(() => import('./Warning'));
const IconVisa = dynamic(() => import('./Visa'));
const IconExpand = dynamic(() => import('./Expand'));
const IconCollapse = dynamic(() => import('./Collapse'));
const IconNCT = dynamic(() => import('./NCT'));
const ClockCountdown = dynamic(() => import('./ClockCountdown'));
const Pentagram = dynamic(() => import('./Pentagram'));
const Headphones = dynamic(() => import('./Headphones'));
const Exsh = dynamic(() => import('./Exsh'));

const Index = ({ type, isActive, isHovering, size, error, title }: any) => {
  switch (type) {
    case ICON_KEY.HOME:
      return <IconHomeSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.RUNNING_MAN:
      return <IconRunningManSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.RAPVIET:
      return <IconRapVietSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.HBO:
      return <IconHBOSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.K_PLUS:
      return <IconKPlusSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.KID:
      return <IconKidSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.MORE:
      return <IconMoreSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.SCHEDULE:
      return <IconScheduleSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.MUSIC:
      return <IconMusicSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.NALA:
      return <IconNalaSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.NEWS:
      return <IconNewsSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.PODCAST:
      return <IconPodcastSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.READING:
      return <IconReadingSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.SPORT:
      return <IconSportSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.TELEVISION:
      return <IconTelevisionSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.TENNIS:
      return <IconTennisSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.VIP:
      return <IconVipSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.VTVCAB:
      return <IconVtvCabSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.VIEZ:
      return <IconVieZSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.VTV:
      return <IconVTVZSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.GAME:
      return <IconGameSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.VIECOMIC:
      return <IconVieComicSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.LIVESTREAM:
      return <IconLiveStreamSvg isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.DESKTOP:
      return <IconLaptopSvg />;
    case ICON_KEY.OTHER:
      return <IconBlankFileSvg />;
    case ICON_KEY.EMPTY_BOX:
      return <IconEmptyInboxSvg />;
    case ICON_KEY.APP:
      return <IconSmartPhoneSvg />;
    case ICON_KEY.SMART_TV:
      return <IconSmartTvSvg />;
    case ICON_KEY.TABLET:
      return <IconTabletSvg />;
    case ICON_KEY.TICK_CIRCLE:
      return <IconCircleTick isActive={isActive} />;
    case ICON_KEY.COPY:
      return <IconCopy />;
    case ICON_KEY.PLUS:
      return <IconPlus />;
    case ICON_KEY.EDIT:
      return <IconEdit />;
    case ICON_KEY.BACK:
      return <IconBack />;
    case ICON_KEY.KID_SOLID:
      return <IconKidSolid isSmall={isActive} />;
    case ICON_KEY.CLOCK:
      return <IconClock />;
    case ICON_KEY.CHECK_ALL:
      return <IconCheckAll />;
    case ICON_KEY.CHECKED_ALL:
      return <IconCheckedAll />;
    case ICON_KEY.LOG_OUT:
      return <IconLogout />;
    case ICON_KEY.PHONE:
      return <IconPhone />;
    case ICON_KEY.CART:
      return <IconCart />;
    case ICON_KEY.REDIRECT:
      return <IconRedirect />;
    case ICON_KEY.CART_INDICATOR:
      return <IconCartIndicator isActive={isActive} />;
    case ICON_KEY.SHOPPING_CART:
      return <IconShoppingCart />;
    case ICON_KEY.LIST:
      return <IconList />;
    case ICON_KEY.FAQ:
      return <IconFAQ />;
    case ICON_KEY.CHECK:
      return <IconCheck />;
    case ICON_KEY.LOCK:
      return <IconLock />;
    case ICON_KEY.PLAYBACK_SPEED:
      return <PlaybackSpeed />;
    case ICON_KEY.EMAIL:
      return <IconEmail />;
    case ICON_KEY.WARNING:
      return <IconWarning isActive={isActive} />;
    case ICON_KEY.VISA:
      return <IconVisa />;
    case ICON_KEY.EXPAND:
      return <IconExpand />;
    case ICON_KEY.COLLAPSE:
      return <IconCollapse />;
    case ICON_KEY.INFO:
      return <InfoStroke />;
    case ICON_KEY.PHONE_PAYMENT:
      return <PhonePayment error={error} />;
    case ICON_KEY.PASSWORD_PAYMENT:
      return <PasswordPayment error={error} />;
    case ICON_KEY.REGISTER_PAYMENT:
      return <IconRegisterNext />;
    case ICON_KEY.CHAT_SOLID:
      return <ChatSolid size={size} title={title} />;
    case ICON_KEY.NCT:
      return <IconNCT isActive={isActive} isHovering={isHovering} />;
    case ICON_KEY.VOUCHER_PAYMENT:
      return <IconVoucher error={error} isActive={isActive} />;
    case ICON_KEY.CLOCK_COUNTDOWN:
      return <ClockCountdown size={size} title={title} />;
    case ICON_KEY.PENTAGRAM:
      return <Pentagram size={size} title={title} />;
    case ICON_KEY.HEADPHONES:
      return <Headphones size={size} title={title} />;
    case ICON_KEY.EXSH:
      return <Exsh size={size} isActive={isActive} isHovering={isHovering} />;
    default:
      return null;
  }
};

export default Index;

import React from 'react';

const ChatSolid = ({ size, title }: any) => (
  <svg
    width={size || '32'}
    height={size ? 'auto' : '22'}
    viewBox="0 0 32 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>{title}</title>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7 0C3.13401 0 0 3.13401 0 7.00001V19.3333C0 20.4379 0.895431 21.3333 2 21.3333H25C28.866 21.3333 32 18.1993 32 14.3333V7C32 3.13401 28.866 0 25 0H7ZM6.40003 7.33331C6.40003 6.71966 6.89749 6.2222 7.51114 6.2222H25.4032C26.0169 6.2222 26.5143 6.71966 26.5143 7.33331C26.5143 7.94696 26.0169 8.44443 25.4032 8.44443H7.51114C6.89749 8.44443 6.40003 7.94696 6.40003 7.33331ZM6.4 13.7777C6.4 13.0414 6.99695 12.4444 7.73333 12.4444H17.8667C18.6031 12.4444 19.2 13.0414 19.2 13.7777C19.2 14.5141 18.6031 15.1111 17.8667 15.1111H7.73333C6.99695 15.1111 6.4 14.5141 6.4 13.7777Z"
      fill="currentColor"
    />
  </svg>
);

export default ChatSolid;

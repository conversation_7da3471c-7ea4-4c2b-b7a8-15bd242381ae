import React from 'react';

const Podcast = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M11.9238 15.9226C14.1001 15.9226 15.8708 14.1519 15.8708 11.9756L15.8706 5.44701C15.8706 3.27065 14.0999 1.5 11.9236 1.5C9.74721 1.5 7.97656 3.27065 7.97656 5.44701V11.9756C7.97675 14.1519 9.7474 15.9226 11.9238 15.9226H11.9238ZM9.14282 5.44711C9.14282 3.91376 10.3904 2.66617 11.9238 2.66617C13.4571 2.66617 14.7047 3.91376 14.7047 5.44711V11.9756C14.7047 13.509 13.4571 14.7566 11.9238 14.7566C10.3904 14.7566 9.14282 13.509 9.14282 11.9756V5.44711Z"
          fill="white"
        />
        <rect x={9} y="2.25" width={6} height="12.75" rx={3} fill="white" />
        <path
          d="M17.2655 8.69629C16.9435 8.69629 16.6825 8.95726 16.6825 9.27923V12.3201C16.6825 14.9437 14.5479 17.0784 11.9242 17.0784C9.30051 17.0784 7.16588 14.9438 7.16588 12.3201V9.27923C7.16588 8.95726 6.90491 8.69629 6.58294 8.69629C6.26097 8.69629 6 8.95726 6 9.27923V12.3201C6 15.3902 8.34723 17.9217 11.3414 18.2158V21.3341H9.79678C9.47481 21.3341 9.21384 21.595 9.21384 21.917C9.21384 22.239 9.47481 22.5 9.79678 22.5H14.0523C14.3743 22.5 14.6353 22.239 14.6353 21.917C14.6353 21.595 14.3743 21.3341 14.0523 21.3341H12.5078V18.2158C15.5019 17.9219 17.8491 15.3903 17.8491 12.3201L17.8489 9.27923C17.8489 8.95726 17.5876 8.69629 17.2656 8.69629H17.2655Z"
          fill="white"
        />
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M11.9238 15.9226C14.1001 15.9226 15.8708 14.1519 15.8708 11.9756L15.8706 5.44701C15.8706 3.27065 14.0999 1.5 11.9236 1.5C9.74721 1.5 7.97656 3.27065 7.97656 5.44701V11.9756C7.97675 14.1519 9.7474 15.9226 11.9238 15.9226H11.9238ZM9.14282 5.44711C9.14282 3.91376 10.3904 2.66617 11.9238 2.66617C13.4571 2.66617 14.7047 3.91376 14.7047 5.44711V11.9756C14.7047 13.509 13.4571 14.7566 11.9238 14.7566C10.3904 14.7566 9.14282 13.509 9.14282 11.9756V5.44711Z"
          fill="#999999"
        />
        <path
          d="M17.2655 8.69629C16.9435 8.69629 16.6825 8.95726 16.6825 9.27923V12.3201C16.6825 14.9437 14.5479 17.0784 11.9242 17.0784C9.30051 17.0784 7.16588 14.9438 7.16588 12.3201V9.27923C7.16588 8.95726 6.90491 8.69629 6.58294 8.69629C6.26097 8.69629 6 8.95726 6 9.27923V12.3201C6 15.3902 8.34723 17.9217 11.3414 18.2158V21.3341H9.79678C9.47481 21.3341 9.21384 21.595 9.21384 21.917C9.21384 22.239 9.47481 22.5 9.79678 22.5H14.0523C14.3743 22.5 14.6353 22.239 14.6353 21.917C14.6353 21.595 14.3743 21.3341 14.0523 21.3341H12.5078V18.2158C15.5019 17.9219 17.8491 15.3903 17.8491 12.3201L17.8489 9.27923C17.8489 8.95726 17.5876 8.69629 17.2656 8.69629H17.2655Z"
          fill="#999999"
        />
      </svg>
    );
  }
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M11.9238 15.9226C14.1001 15.9226 15.8708 14.1519 15.8708 11.9756L15.8706 5.44701C15.8706 3.27065 14.0999 1.5 11.9236 1.5C9.74721 1.5 7.97656 3.27065 7.97656 5.44701V11.9756C7.97675 14.1519 9.7474 15.9226 11.9238 15.9226H11.9238ZM9.14282 5.44711C9.14282 3.91376 10.3904 2.66617 11.9238 2.66617C13.4571 2.66617 14.7047 3.91376 14.7047 5.44711V11.9756C14.7047 13.509 13.4571 14.7566 11.9238 14.7566C10.3904 14.7566 9.14282 13.509 9.14282 11.9756V5.44711Z"
        fill="#CCCCCC"
      />
      <path
        d="M17.2655 8.69629C16.9435 8.69629 16.6825 8.95726 16.6825 9.27923V12.3201C16.6825 14.9437 14.5479 17.0784 11.9242 17.0784C9.30051 17.0784 7.16588 14.9438 7.16588 12.3201V9.27923C7.16588 8.95726 6.90491 8.69629 6.58294 8.69629C6.26097 8.69629 6 8.95726 6 9.27923V12.3201C6 15.3902 8.34723 17.9217 11.3414 18.2158V21.3341H9.79678C9.47481 21.3341 9.21384 21.595 9.21384 21.917C9.21384 22.239 9.47481 22.5 9.79678 22.5H14.0523C14.3743 22.5 14.6353 22.239 14.6353 21.917C14.6353 21.595 14.3743 21.3341 14.0523 21.3341H12.5078V18.2158C15.5019 17.9219 17.8491 15.3903 17.8491 12.3201L17.8489 9.27923C17.8489 8.95726 17.5876 8.69629 17.2656 8.69629H17.2655Z"
        fill="#CCCCCC"
      />
    </svg>
  );
};

export default Podcast;

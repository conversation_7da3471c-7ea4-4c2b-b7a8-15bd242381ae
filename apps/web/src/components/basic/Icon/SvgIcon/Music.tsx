import React from 'react';

const Music = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.7012 4.62156C13.5402 5.88012 14.7525 7.08154 16.4073 8.29459C17.363 8.99515 17.9336 10.1201 17.9336 11.3039C17.9336 13.3602 16.2607 15.0332 14.2044 15.0332C13.7925 15.0332 13.4585 14.6993 13.4585 14.2874C13.4585 13.8755 13.7925 13.5415 14.2044 13.5415C15.4382 13.5415 16.4419 12.5377 16.4419 11.304C16.4419 10.5936 16.0993 9.91841 15.5254 9.49775C14.0298 8.4014 12.8582 7.30077 11.9668 6.1522V18.0166C11.9668 19.6616 10.6285 21 8.98341 21C7.33836 21 6 19.6616 6 18.0166C6 16.3715 7.33836 15.0332 8.98341 15.0332C9.52664 15.0332 10.0359 15.1798 10.4751 15.4345V3.74593C10.4751 3.41726 10.6903 3.12732 11.0048 3.03205C11.3193 2.93683 11.6592 3.0587 11.8415 3.33218L12.7012 4.62156Z"
          fill="white"
        />
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M16.4073 8.29459C14.7525 7.08154 13.5402 5.88012 12.7012 4.62156L11.8415 3.33218C11.6592 3.0587 11.3193 2.93683 11.0048 3.03205C10.6903 3.12732 10.4751 3.41726 10.4751 3.74593V15.4345C10.0359 15.1798 9.52664 15.0332 8.98341 15.0332C7.33836 15.0332 6 16.3715 6 18.0166C6 19.6616 7.33836 21 8.98341 21C10.6285 21 11.9668 19.6616 11.9668 18.0166V6.1522C12.8582 7.30077 14.0298 8.4014 15.5254 9.49775C16.0993 9.91841 16.4419 10.5936 16.4419 11.304C16.4419 12.5377 15.4382 13.5415 14.2044 13.5415C13.7925 13.5415 13.4585 13.8755 13.4585 14.2874C13.4585 14.6993 13.7925 15.0332 14.2044 15.0332C16.2607 15.0332 17.9336 13.3602 17.9336 11.3039C17.9336 10.1201 17.363 8.99515 16.4073 8.29459ZM8.98341 19.5083C8.16089 19.5083 7.49171 18.8391 7.49171 18.0166C7.49171 17.1941 8.16089 16.5249 8.98341 16.5249C9.80594 16.5249 10.4751 17.1941 10.4751 18.0166C10.4751 18.8391 9.80594 19.5083 8.98341 19.5083Z"
          fill="#999999"
        />
      </svg>
    );
  }
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M16.4073 8.29459C14.7525 7.08154 13.5402 5.88012 12.7012 4.62156L11.8415 3.33218C11.6592 3.0587 11.3193 2.93683 11.0048 3.03205C10.6903 3.12732 10.4751 3.41726 10.4751 3.74593V15.4345C10.0359 15.1798 9.52664 15.0332 8.98341 15.0332C7.33836 15.0332 6 16.3715 6 18.0166C6 19.6616 7.33836 21 8.98341 21C10.6285 21 11.9668 19.6616 11.9668 18.0166V6.1522C12.8582 7.30077 14.0298 8.4014 15.5254 9.49775C16.0993 9.91841 16.4419 10.5936 16.4419 11.304C16.4419 12.5377 15.4382 13.5415 14.2044 13.5415C13.7925 13.5415 13.4585 13.8755 13.4585 14.2874C13.4585 14.6993 13.7925 15.0332 14.2044 15.0332C16.2607 15.0332 17.9336 13.3602 17.9336 11.3039C17.9336 10.1201 17.363 8.99515 16.4073 8.29459ZM8.98341 19.5083C8.16089 19.5083 7.49171 18.8391 7.49171 18.0166C7.49171 17.1941 8.16089 16.5249 8.98341 16.5249C9.80594 16.5249 10.4751 17.1941 10.4751 18.0166C10.4751 18.8391 9.80594 19.5083 8.98341 19.5083Z"
        fill="#CCCCCC"
      />
    </svg>
  );
};

export default Music;

import React from 'react';
import { TEXT } from '@constants/text';

const CustomButtonGroup = ({
  classNameLeft,
  classNameRight,
  onClose,
  isDisabled,
  type,
  subTitle,
  styleButton,
  onMouseEnter,
  onMouseLeave,
  onClick
}: any) => (
  <div className="button-group child-auto p-t3">
    <button
      className={classNameLeft}
      onClick={onClose}
      onMouseEnter={onMouseEnter || (() => {})}
      onMouseLeave={onMouseLeave || (() => {})}
      type={type || 'button'}
      title={subTitle || ''}
      style={styleButton}
    >
      <span className="text">{TEXT.SKIP}</span>
    </button>
    <button
      className={classNameRight}
      onClick={onClick}
      onMouseEnter={onMouseEnter || (() => {})}
      onMouseLeave={onMouseLeave || (() => {})}
      disabled={isDisabled}
      type={type || 'button'}
      title={subTitle || ''}
      style={styleButton}
    >
      <span className="text">{TEXT.SAVE}</span>
    </button>
  </div>
);

export default CustomButtonGroup;

import React from 'react';
import ButtonBase from '@components/basic/Buttons/ButtonBase';
import NewIcon from '@components/basic/Icon/NewIcon';
import styles from './Button.module.scss';
const ButtonViewAll = React.memo(({ title, iconName, onClick }: any) => (
  <ButtonBase className={styles.viewAll} onClick={onClick}>
    {title && <span className={styles.viewAllLabel}>{title}</span>}
    {iconName && (
      <span className={styles.viewAllIcon}>
        <NewIcon iCustomizeClass={styles.vieAllIcon} iconName={iconName} />
      </span>
    )}
  </ButtonBase>
));

export default ButtonViewAll;

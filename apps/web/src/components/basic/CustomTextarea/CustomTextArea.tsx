import React, { Component } from 'react';
import { EL_ID } from '@constants/constants';

class CustomTextArea extends Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      value: ''
    };
  }

  componentDidMount() {
    const { callback }: any = this.props;
    if (typeof callback === 'function') {
      callback({ callback: { onResetValue: this.onResetValue } });
    }
  }

  componentDidUpdate(prevProps: any) {
    const { id }: any = this.props;
    if (id !== prevProps.id) {
      this.setState({ value: '' });
    }
  }

  onChange = (e: any) => {
    const value = e?.target?.value;
    const { onChange }: any = this.props;
    if (typeof onChange === 'function') onChange(value);
    this.setState({ value });
  };

  onKeyPress = (e: any) => {
    if (e && e.charCode === 13) {
      e.preventDefault();
      const { onKeyPress }: any = this.props;
      if (typeof onKeyPress === 'function') onKeyPress();
      this.setState({ value: '' });
    }
  };

  onResetValue = () => {
    this.setState({ value: '' });
  };

  render() {
    const { name, id, cols, rows, placeholder, className, onClick, onFocus }: any = this.props;
    const { value }: any = this.state;
    return (
      <textarea
        onKeyPress={this.onKeyPress}
        onChange={this.onChange}
        onFocus={onFocus}
        className={className || ''}
        value={value || ''}
        name={name}
        id={id || EL_ID.COMMENT_AREA}
        cols={cols}
        rows={rows}
        placeholder={placeholder}
        onClick={onClick}
        tabIndex={-1}
      />
    );
  }
}

export default CustomTextArea;

import React from 'react';
import { useSelector } from 'react-redux';
import ConfigImage from '@config/ConfigImage';

const Loading = () => {
  const loading = useSelector((state: any) => state?.App?.loading);
  if (!loading) return null;
  return (
    <div className="modal-overlay fixed layer-max" id="MODAL">
      <div className="modal middle animate-fade-in">
        <img
          src={ConfigImage.loadingSpinner}
          style={{
            width: '50px',
            display: 'block',
            margin: '0 auto'
          }}
          alt="VieON"
        />
      </div>
    </div>
  );
};

export default React.memo(Loading);

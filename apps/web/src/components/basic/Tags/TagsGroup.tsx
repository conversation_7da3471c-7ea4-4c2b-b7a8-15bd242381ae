import { EL_CLASS } from '@constants/constants';
import React, { useMemo } from 'react';

const TagsGroup = ({
  // Define tag list
  tagList,

  divider,
  layout
}: any) => {
  // Define layoutClass: {? 'horizontal' : 'vertical'};
  // middle-[v/h]: align sub-elements in a certain direction

  const dividerClass = useMemo(() => EL_CLASS[divider] || '', [divider]);

  const layoutClass = useMemo(() => EL_CLASS[layout] || '', [layout]) || EL_CLASS.LAYOUT_H;
  const className = `tags-group${dividerClass}${layoutClass}`;

  return <div className={className}>{tagList}</div>;
};
export default TagsGroup;

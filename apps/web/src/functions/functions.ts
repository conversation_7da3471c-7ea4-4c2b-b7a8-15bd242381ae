import { getUrlFromPath, parseQueryString } from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import { PAGE } from '@constants/constants';
import { UtmParams } from '@models/subModels';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import ConfigImage from '@config/ConfigImage';

export const backFromPlayer = ({ router, contentId, newContentId, isIntro }: any) => {
  if (!router) return;
  let params: any = ConfigLocalStorage.get(LocalStorage.BACK_FROM_PLAYER);
  params = JSON.parse(params || '{}');
  let { pathname, queryParams, asPath, expand } = params || {};
  queryParams = queryParams || {};
  let vid = newContentId || contentId || queryParams.vid;
  const isRecommend = (asPath || '').indexOf('?id') > -1;
  if (!expand && !isIntro) vid = '';

  const utParams = UtmParams(queryParams);
  const query = { ...utParams };
  if (queryParams.q) query.q = queryParams.q;
  if (queryParams.id) query.id = queryParams.id;
  const url = getUrlFromPath(asPath);
  if (vid) query.vid = vid;
  if (isRecommend && !queryParams.id) {
    const location: any = Location;
    query.id = parseQueryString(location.search)?.id;
  }
  router.push({ pathname: pathname || PAGE.HOME, query }, { pathname: url || PAGE.HOME, query });
  ConfigLocalStorage.remove(LocalStorage.BACK_FROM_PLAYER);
};

export function pingGoogle(timeout: any) {
  const url = ConfigImage.logoVieONPng;
  return new Promise((resolve) => {
    const img: any = new Image();
    let timedOut = false;

    const timeoutId = setTimeout(() => {
      timedOut = true;
      img.src = ''; // Abort the image loading
      resolve(false);
    }, timeout || 3000);
    img.onload = function () {
      if (!timedOut) {
        clearTimeout(timeoutId);
        resolve(true);
      }
    };
    img.onerror = function () {
      if (!timedOut) {
        clearTimeout(timeoutId);
        resolve(false);
      }
    };

    img.src = url;
  });
}

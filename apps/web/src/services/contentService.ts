import { CONTENT_TYPE, PERMISSION, TVOD } from '@constants/constants';
import DetailApi from '@apis/detailApi';
import { TEXT } from '@constants/text';
import { parseBrowserName } from '@helpers/common';
import PageApi from '@apis/cm/PageApi';
import PaymentApi from '@apis/Payment';
import { VALUE } from '@config/ConfigSegment';
import { ACTION_TYPE, createAction } from '@actions/actionType';
import { handleGetSlugEpsFromPath } from './detailServices';

export const checkPermissionContent = async ({
  contentData,
  isVodDetail,
  router,
  cardDataEpisode,
  dispatch,
  isGlobal
}: any) => {
  let detail: any = {};
  const type = cardDataEpisode?.type || contentData?.type;
  switch (type) {
    case CONTENT_TYPE.SEASON: {
      let path = '';
      let episodeSlug = '';
      if (isVodDetail) {
        path = (router?.asPath || '').split('?')?.[0];
        const { episodeFromPath } = handleGetSlugEpsFromPath({ path });
        episodeSlug = episodeFromPath;
      }
      let defaultEpisode = isVodDetail && episodeSlug ? null : contentData?.defaultEpisode;
      if (!defaultEpisode?.id && !(isVodDetail && episodeSlug)) {
        const newContent = await DetailApi.getContentById({ contentId: contentData?.id, isGlobal });
        defaultEpisode = newContent?.data?.defaultEpisode;
      }
      if (!defaultEpisode?.id) {
        const episodeList = await DetailApi.getEpisodeListById({
          contentId: contentData?.id,
          page: 0,
          limit: 60,
          isGlobal
        });
        defaultEpisode =
          isVodDetail && episodeSlug
            ? (episodeList?.items || []).find((item: any) => item?.seo?.url === path)
            : episodeList?.items?.[0];
      }
      if (defaultEpisode?.id || contentData?.comingSoon?.epsIdCurrent) {
        const contentDetail = await DetailApi.getContentDetail({
          contentId: contentData?.id,
          episodeId: defaultEpisode?.id || contentData?.comingSoon?.epsIdCurrent,
          isCheckPermission: true,
          isCard: true
        });
        detail = contentDetail?.data;
      } else {
        dispatch(createAction(ACTION_TYPE.SET_TOAST, { message: TEXT.MSG_ERROR }, dispatch));
      }
      break;
    }
    case CONTENT_TYPE.EPISODE: {
      const contentDetail = await DetailApi.getContentDetail({
        contentId: cardDataEpisode ? contentData?.id : contentData?.groupId,
        episodeId: cardDataEpisode?.id || contentData?.comingSoon?.epsIdCurrent || contentData?.id,
        isCard: true
      });
      detail = contentDetail?.data;
      break;
    }
    case CONTENT_TYPE.MOVIE: {
      const contentDetail = await DetailApi.getContentDetail({
        contentId: contentData?.id,
        isCard: true
      });
      detail = contentDetail?.data;
      break;
    }
    case CONTENT_TYPE.LIVESTREAM: {
      // TODO GET PERMISSION LIVE STREAM
      const livestreamDetail = await PageApi.getLivestreamEventsById({
        id: contentData?.id,
        isCard: true
      });
      detail = livestreamDetail;
      break;
    }
    default:
      break;
  }
  const {
    permission,
    drmServiceName,
    packages,
    trialDuration,
    forceLogin,
    triggerLoginDuration,
    isVip
  } = detail || {};
  return {
    permission,
    drmServiceName,
    trialDuration,
    packageId: packages?.[0]?.id,
    packageItem: packages?.[0],
    forceLogin,
    triggerLoginDuration,
    isVip,
    detail
  };
};

export const parseTVodIdType = ({ type }: any) => {
  switch (type) {
    case CONTENT_TYPE.MOVIE:
      return TVOD.ID_TYPE.MOVIE;
    case CONTENT_TYPE.SEASON:
      return TVOD.ID_TYPE.SEASON;
    case CONTENT_TYPE.EPISODE:
      return TVOD.ID_TYPE.EPISODE;
    default:
      return '';
  }
};

export const formatTimeTVodString = ({ strConfig, strTime }: any) =>
  (strConfig || '').replace('{STR_TIME}', strTime);

export const formatUpperCaseTimeTVodString = ({ strConfig, strTime }: any) =>
  (strConfig || '').toUpperCase().replace('{STR_TIME}', strTime);

export const formatPaymentTVodNoteString = ({ strConfig, strTimeWaiting, strTimeDuration }: any) =>
  (strConfig || '')
    .replace('{STR_TIME_WAITING}', strTimeWaiting)
    .replace('{STR_TIME_DURATION}', strTimeDuration);

export const parseTimeExpiredTVod = ({ expiredTime, title, type }: any) => {
  let strTimeInfoBox = '';
  let strTimeStandard = '';
  let strTimeTag = '';
  let titleTVod = '';
  if (!expiredTime || expiredTime <= 0) {
    return { strTimeInfoBox, strTimeStandard, strTimeTag, titleTVod };
  }
  const timeNow = Math.floor(Date.now() / 1000);
  const seconds = expiredTime - timeNow;
  const days = seconds / (24 * 60 * 60);
  if (days > 2) {
    const floorDays = Math.ceil(days);
    const strTime = floorDays < 10 ? `0${floorDays}` : floorDays;
    // strTimeInfoBox = strTimeStandard = `${strTime} ngày`;
    strTimeInfoBox = `${strTime} ngày`;
    strTimeStandard = `${strTime} ngày`;
    strTimeTag = `${strTime} ngày`;
    // strTimeTag = `${strTime} NGÀY`;
  } else if (days === 2) {
    // strTimeTag = strTimeStandard = '48g';
    strTimeTag = '48g';
    strTimeStandard = '48g';
    strTimeInfoBox = '48 giờ';
  } else {
    const hours = seconds / (60 * 60);
    if (seconds % (60 * 60) === 0 && hours >= 1) {
      // strTimeTag = strTimeStandard = `${hours < 10 ? `0${hours}}` : hours}g`;
      strTimeTag = `${hours < 10 ? `0${hours}}` : hours}g`;
      strTimeStandard = `${hours < 10 ? `0${hours}}` : hours}g`;
      strTimeInfoBox = `${hours} giờ`;
    } else if (seconds % (60 * 60) !== 0 && hours >= 1) {
      const ceilHours = Math.floor(hours);
      let minutesMore = Math.floor((seconds % (60 * 60)) / 60);
      if ((seconds % (60 * 60)) / 60 <= 1) {
        minutesMore = 1;
      }
      const strHours = ceilHours < 10 ? `0${ceilHours}` : ceilHours;
      const strMinutes = minutesMore < 10 ? `0${minutesMore}` : minutesMore;
      // strTimeTag = strTimeStandard = `${strHours}g ${strMinutes}ph`;
      strTimeTag = `${strHours}g ${strMinutes}ph`;
      strTimeStandard = `${strHours}g ${strMinutes}ph`;
      strTimeInfoBox = `${strHours} giờ ${strMinutes} phút`;
    } else {
      let minutes = Math.floor(seconds / 60);
      if (seconds / 60 <= 1 && seconds > 0) {
        minutes = 1;
      } else if (seconds <= 0) {
        minutes = 0;
      }
      const strMinutes = minutes < 10 ? `0${minutes}` : minutes;
      strTimeTag = minutes > 0 ? `${strMinutes}ph` : '';
      strTimeStandard = strTimeTag;
      strTimeInfoBox = minutes > 0 ? `${strMinutes} phút` : '';
    }
  }
  if ((type === CONTENT_TYPE.SEASON || type === CONTENT_TYPE.MOVIE) && title) {
    titleTVod = strTimeInfoBox ? `Bạn còn ${strTimeInfoBox} để xem “${title}".` : '';
  }
  return {
    strTimeInfoBox,
    strTimeStandard, // chuỗi chuẩn, lấy theo time string của INDICATOR
    strTimeTag,
    titleTVod
  };
};

export const parseTVodFromInfo = (data: any) => {
  if (!data) return null;
  const { strTimeInfoBox, strTimeStandard, strTimeTag, titleTVod } = parseTimeExpiredTVod({
    expiredTime: data?.benefitInfo?.endAt
  });
  return {
    price: data?.bizInfo?.price,
    consumingDurMsg: data?.bizInfo?.consumingDurMsg,
    waitingDurMsg: data?.bizInfo?.waitingDurMsg,
    benefitEndedAt: data?.benefitInfo?.endAt,
    benefitType: data?.benefitInfo?.type,
    priceMsg: data?.bizInfo?.priceMsg,
    strTimeStandard,
    strTimeTag,
    strTimeInfoBox,
    titleTVod
  };
};
export const handlePreventContent = ({ id, preventContent, permission }: any) => {
  if (
    !preventContent ||
    !preventContent?.contentList ||
    preventContent?.contentList?.length === 0 ||
    !preventContent?.allowBrowser ||
    preventContent?.allowBrowser?.length === 0 ||
    permission !== PERMISSION.CAN_WATCH
  ) {
    return {};
  }
  const idCheck = (preventContent.contentList || []).findIndex((name: any) => name === id);
  if (idCheck === -1) return {};
  const browserName = parseBrowserName();
  const browserCheck = (preventContent.allowBrowser || []).findIndex(
    (brName: any) => (brName || '').toUpperCase() === browserName
  );
  if (browserCheck > -1) return {};
  return { isPrevent: true, preventNote: preventContent.note || '' };
};

export const checkExpiredTVOD = async ({ contentData }: any) => {
  let isExpired = false;
  const { id, type } = contentData || {};
  const newTVodInfo = await PaymentApi.getTVodInfo({ contentId: id, contentType: type });
  const { benefitInfo } = newTVodInfo || {};
  if (
    benefitInfo?.type === TVOD.USER_TYPE.EXPIRED &&
    contentData?.tvod?.benefitType === TVOD.USER_TYPE.RENTED
  ) {
    isExpired = true;
  }
  return { isExpired, newTVodInfo };
};

export const setVideoPlayType = ({ isVip, permission, trialDuration, isPremiumTVod }: any) => {
  let videoPlayType = VALUE.AVOD;

  if (isPremiumTVod) {
    videoPlayType = VALUE.TVOD;
  } else if (permission !== PERMISSION.CAN_WATCH && trialDuration > 0) {
    videoPlayType = VALUE.SVOD_TRIAL;
  } else if (isVip) {
    videoPlayType = VALUE.SVOD;
  }
  return videoPlayType;
};

export const convertTimeRangeToSeconds = (timeRangeStr: any) => {
  if (!timeRangeStr) return 0;

  // Convert time strings (MM:SS) to seconds
  const timeParts = timeRangeStr.split(':').map(Number);

  // Handle different time formats
  if (timeParts.length === 2) {
    // MM:SS format
    return timeParts[0] * 60 + timeParts[1];
  } else if (timeParts.length === 3) {
    // HH:MM:SS format
    return timeParts[0] * 3600 + timeParts[1] * 60 + timeParts[2];
  }

  return 0;
};

export const isTimeInRange = (currentTime: any, startTimeStr: any, endTimeStr: any) => {
  if (!startTimeStr || !endTimeStr) return false;

  const startTimeSeconds = convertTimeRangeToSeconds(startTimeStr);
  const endTimeSeconds = convertTimeRangeToSeconds(endTimeStr);

  return currentTime >= startTimeSeconds && currentTime <= endTimeSeconds;
};

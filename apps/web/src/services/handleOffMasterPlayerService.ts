import { PLAYER_TYPE } from '@constants/player';
import { createTimeout } from '@helpers/common';

let checkMasterPlayerTimer: any = null;
let checkMasterPlayerCount = 0;
export function handleOffMasterPlayerService() {
  const masterPlayer: any = window[PLAYER_TYPE.MASTER_BANNER];
  if (!masterPlayer) {
    if (checkMasterPlayerTimer) clearTimeout(checkMasterPlayerTimer);
    if (checkMasterPlayerCount >= 3) {
      if (checkMasterPlayerTimer) clearTimeout(checkMasterPlayerTimer);
    } else {
      checkMasterPlayerTimer = createTimeout(() => {
        handleOffMasterPlayerService();
        checkMasterPlayerCount += 1;
      }, 1000);
    }
  } else {
    masterPlayer.pause();
  }
}

import MenuItem from '@models/MenuItem';
import { PAGE } from '@constants/constants';
import { removeURLQueryParams } from '@helpers/common';

export const parseMenuData = (menuData: any) =>
  (menuData || []).map((item: any, index: any) => new MenuItem(item, false, { menuOrder: index }));

export const getActiveMenuBySlug = ({ menuList, slug }: any) => {
  const homeItem = (menuList || []).find((item: any) => item.primary);
  if ((slug || '').indexOf('#') > -1) slug = removeURLQueryParams(slug, '#') || '';
  slug = slug === '/' ? homeItem?.menuUrl : slug;
  let activeMenu = null;
  let activeSubMenu: any = null;
  let subHeader: any = null;

  (menuList || []).forEach((item: any) => {
    if (slug) {
      if (slug === PAGE.VIP && item?.slug === PAGE.VIP) {
        activeMenu = item;
      } else if (
        item?.seo?.url &&
        (slug || '').includes(item?.seo?.url) &&
        ((item?.seo?.url || '').includes(PAGE.LIVE_STREAM) ||
          ((item?.seo?.url || '').includes(PAGE.LIVE_TV) &&
            !(slug || '').includes(PAGE.LIVE_STREAM)) ||
          item?.seo?.url === slug)
      ) {
        activeMenu = item;
      } else {
        (item?.subMenu || []).forEach((sub: any) => {
          if (
            !activeSubMenu &&
            (slug || '').includes(sub?.seo?.url) &&
            ((sub?.seo?.url || '').includes(PAGE.LIVE_STREAM) ||
              ((sub?.seo?.url || '').includes(PAGE.LIVE_TV) &&
                !(slug || '').includes(PAGE.LIVE_STREAM)) ||
              sub?.seo?.url === slug)
          ) {
            activeSubMenu = sub;
            activeMenu = item;
          } else {
            const temp = (sub?.subMenuRibbon || []).find((it: any) => it?.seo?.url === slug);
            subHeader = subHeader || temp;
            if (temp) {
              activeSubMenu = sub;
              activeMenu = item;
            }
          }
        });
      }
    }
  });
  const subMenuRibbonData: any = [];
  const menuSubRibbonData = activeSubMenu?.subMenuRibbon || [];
  if (menuSubRibbonData?.length > 0 && menuSubRibbonData?.[0]?.id !== activeSubMenu?.id) {
    menuSubRibbonData.unshift({
      id: activeSubMenu?.id,
      name: activeSubMenu?.titleRibbon,
      seo: { ...activeSubMenu?.seo, href: PAGE.HOME }
    });
  }

  if (activeSubMenu) {
    const count = activeSubMenu?.quantityRibbon || 4;
    (menuSubRibbonData || []).forEach((item: any, index: any) => {
      const temp = Math.floor(index / count);
      if (!subMenuRibbonData?.[temp]) subMenuRibbonData[temp] = [];
      subMenuRibbonData?.[temp].push(item);
    });
    activeSubMenu.subMenuRibbonData = subMenuRibbonData;
  }

  return { activeMenu, activeSubMenu, subHeader };
};

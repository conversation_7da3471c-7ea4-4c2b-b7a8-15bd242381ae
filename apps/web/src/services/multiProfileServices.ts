import { TEXT } from '@constants/text';
import isArray from 'lodash/isArray';
import MultiProfileApi from '@apis/MultiProfile';
import {
  decodeParamDestination,
  encodeParamDestination,
  parseQueryString,
  queryStringEncoding
} from '@helpers/common';
import isEmpty from 'lodash/isEmpty';
import { PAGE, PROFILE_STATUS } from '@constants/constants';
import get from 'lodash/get';
import { getMultiProfile, selectedProfile } from '@/actions/multiProfile';
import ConfigLocalStorage from '@/config/ConfigLocalStorage';
import LocalStorage from '@/config/LocalStorage';
import { UtmParams } from '@/models/subModels';
import { TYPE_TRIGGER_AUTH } from '@constants/types';

declare const window: any;

export const createUniqueProfileName = (nameList: any) => {
  let name = TEXT.USER;
  const nameData = [
    TEXT.USER,
    `${TEXT.USER} 1`,
    `${TEXT.USER} 2`,
    `${TEXT.USER} 3`,
    `${TEXT.USER} 4`
  ];
  if (isArray(nameList)) {
    nameData.every((n) => {
      if (nameList.findIndex((nl) => (nl.name || '').toLowerCase() === n.toLowerCase()) === -1) {
        name = n;
        return false;
      }
      return true;
    });
  }
  return name;
};

export const pushToLobby = ({ asPath, router }: any) => {
  MultiProfileApi.getMultiProfile({})?.then((res: any) => {
    if (!isEmpty(res?.items)) {
      const remakeDestination = encodeParamDestination(asPath);
      router.push(`${PAGE.LOBBY_PROFILES}/?destination=${remakeDestination}&from=login`);
    }
  });
};

const handleRedirect = ({ currentProfile, router, returnUrl }: any) => {
  const urlToGo = returnUrl || get(router, 'query.destination', '');
  const reLoginParams = ConfigLocalStorage.get(LocalStorage.RE_LOGIN_PARAMS);
  const queryParams = queryStringEncoding(UtmParams(returnUrl || router?.query));
  ConfigLocalStorage.set(LocalStorage.CHECK_PROFILE_ID, true);

  if (currentProfile?.id && urlToGo === PAGE.HOME) {
    window.location = `${PAGE.HOME}${queryParams}`;
    return;
  }

  if (reLoginParams) {
    handleLoginParams({ reLoginParams, isFromLobby: false });
    return;
  }

  const finalUrl = urlToGo
    ? `${decodeParamDestination(urlToGo)}${queryParams ? `?${queryParams}` : ''}`
    : `${PAGE.HOME}${queryParams ? `?${queryParams}` : ''}`;
  window.location = finalUrl;
};

export const destinationLogin = async ({
  dataLogin,
  destination = '',
  router,
  dispatch,
  isLoginBySocial,
  isBindAccount
}: any) => {
  const { accessToken, profile } = dataLogin || {};
  const voucherCode = ConfigLocalStorage.get('voucherCode') || '';
  const trigger = router?.query?.trigger || '';
  const destinationParam = decodeParamDestination(router?.query?.destination);
  const parsedQuery = parseQueryString(destinationParam);
  const isTSvodContent = parsedQuery?.isTSvod === 'true';

  if (
    !accessToken ||
    profile?.status === PROFILE_STATUS.WAIT_FOR_DELETE ||
    voucherCode ||
    isBindAccount
  ) {
    return;
  }

  if (!isTSvodContent) {
    if (
      trigger === TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE ||
      trigger === TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE_PRE_ORDER ||
      trigger === TYPE_TRIGGER_AUTH.SETTING ||
      (destination &&
        (destination.includes(PAGE.PAYMENT_METHOD) || destination.includes(PAGE.RENTAL_CONTENT)))
    ) {
      console.log('zoko');
      router.push(destination);
      return;
    }
  }

  const agreementRes = await MultiProfileApi.getAgreement();
  if (agreementRes?.httpCode === 404) {
    router.push(`${PAGE.LOBBY_PROFILES}/?destination=${destination}&from=login`);
    return;
  }

  const res = await dispatch(getMultiProfile({ accessToken }));
  const profiles = res?.data?.items || [];
  const [currentProfile] = profiles;
  const commonQuery = `?destination=${destination}&from=login`;
  const reLogin: any = ConfigLocalStorage.get(LocalStorage.RE_LOGIN_PARAMS);
  const { url } = JSON.parse(reLogin || '{}');
  const queryAuth = Object.fromEntries(new URLSearchParams(url?.split('?')[1] || ''));

  const doSelectAndRedirect = () =>
    dispatch(
      selectedProfile({
        currentProfile,
        data: currentProfile,
        callback: () =>
          handleRedirect({
            currentProfile,
            router,
            returnUrl: queryAuth?.isTriggerAuth ? '' : destination
          }),
        isReloadPage: true,
        notSaveProfile: false,
        router,
        destination: commonQuery
      })
    );
  if (isLoginBySocial && profiles.length > 1) {
    router.push(`${PAGE.LOBBY_PROFILES}${commonQuery}`);
    return;
  }

  const shouldAutoSelect = profiles.length === 1 && profile?.isPremium;

  if (shouldAutoSelect && currentProfile?.id) {
    if (currentProfile.hasPinCode && !router.pathname.includes(PAGE.LOBBY_PROFILES)) {
      router.push(`${PAGE.LOBBY_PROFILES}${commonQuery}`);
    }
    doSelectAndRedirect();
    return;
  }

  if ((destination || queryAuth?.isTriggerAuth) && !destination?.includes(PAGE.PAYMENT)) {
    router.push(`${PAGE.LOBBY_PROFILES}${commonQuery}`);
  }
};

export const handleLoginParams = ({ reLoginParams, isFromLobby }: any) => {
  const { pathname, url, contentData } = JSON.parse(reLoginParams || '{}');
  const urlResultParams = new URLSearchParams(url?.slice(url.indexOf('?')));
  const query: any = Object.fromEntries(urlResultParams.entries());
  const { id, isAddMyList, seo } = contentData || {};
  if (query?.isTriggerAuth) {
    const hasQuery = query?.destination.includes('?');
    const joiner = hasQuery ? '&' : '?';
    const finalUrl = isFromLobby
      ? query?.destination
      : `${query?.destination}${joiner}isTriggerAuth=true`;
    window.location = finalUrl;

    return;
  }
  if (id) {
    if (pathname === PAGE.HOME) {
      if (query?.vid) window.location = `/?vid=${query?.vid}`;
      else if (isAddMyList) window.location = `/?vid=${id}`;
      else window.location = seo?.url;
    } else if (pathname === PAGE.SEARCH) {
      if (query?.vid) window.location = url || `${url}&vid=${query?.vid}`;
      else if (isAddMyList) window.location = `${url}&vid=${id}`;
      else window.location = seo?.url;
    } else if (query?.vid) window.location = url || `${url}?vid=${query?.vid}`;
    else if (isAddMyList) window.location = `${url}?vid=${id}`;
    else window.location = seo?.url;
  } else {
    window.location = PAGE.HOME;
  }
  ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
};

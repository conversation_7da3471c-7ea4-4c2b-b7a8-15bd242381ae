import { isSafari } from 'react-device-detect';
import axios from 'axios';
import { NAME, VALUE } from '@config/ConfigSegment';
import { CONTENT_TYPE, POPUP } from '@constants/constants';

export const handleResponseTime = async ({ linkPlay }: any) => {
  const { hlsLinkPlay, hlsBackup1, dashLinkPlay, dashBackup1 } = linkPlay || {};
  let link1 = dashLinkPlay;
  let link2 = dashBackup1;
  if (isSafari) {
    link1 = hlsLinkPlay;
    link2 = hlsBackup1;
  }
  const dataMain = await handleTimeRequest({ link: link1 });
  const dataExtra = await handleTimeRequest({ link: link2 });
  return { dataMain, dataExtra };
};
export const handleTimeRequest = async ({ link }: any) => {
  if (link) {
    const snStr = getUrlParams({ url: link, key: 'sn' });
    if (!snStr) return null;
    const url = `https://${snStr}.vieon.vn`;
    const timeStart = new Date().getTime();
    const data = await axios
      .options(url)
      .then(() => {
        const currentTime = new Date().getTime();
        const time = currentTime - timeStart;
        return { time };
      })
      .catch(() => {
        const currentTime = new Date().getTime();
        const time = currentTime - timeStart;
        return { time };
      });
    return { latency: data?.time, snStr };
  }
  return null;
};
function getUrlParams({ url, key }: any) {
  let name: any = '';
  const newUrl = new URL(url);
  const searchParams = new URLSearchParams(newUrl.search);
  name = searchParams.get(key);
  return name;
}
export const flowNameAccountDeletion = ({ isLoginSocial, phoneNumber }: any) => {
  let flowName = '';
  let flowNameOtp = '';
  if (isLoginSocial) {
    if (phoneNumber) {
      flowName = VALUE.RESTORE_ACCOUNT_SOCIAL_LOGIN_PHONE;
      flowNameOtp = VALUE.RESTORE_ACCOUNT_SOCIAL_LOGIN_PHONE;
    } else flowName = VALUE.RESTORE_ACCOUNT_SOCIAL_BIND_ACCOUNT;
  } else {
    flowName = VALUE.RESTORE_ACCOUNT_LOGIN_PHONE;
    flowNameOtp = VALUE.RESTORE_ACCOUNT_LOGIN_PHONE;
  }
  return { flowName, flowNameOtp };
};
export const handleTrackingMWebToApp = ({ data, type }: any) => {
  let eventNameInfoboxLoad;
  let eventNameInfoboxTouch;
  let eventNameBannerLoad;
  let eventNameBannerTouch;
  let flowName;

  const { numberTrialEpisode } = data || {};
  if (type === CONTENT_TYPE.SEASON || type === CONTENT_TYPE.EPISODE) {
    eventNameInfoboxLoad = NAME.TVSERIES_TRIAL_EPISODES_INFOBOX_LANDSCAPE_LOAD;
    eventNameInfoboxTouch = NAME.TVSERIES_TRIAL_EPISODES_INFOBOX_LANDSCAPE_TOUCH;
    eventNameBannerLoad = NAME.TVSERIES_TRIAL_EPISODES_BANNER_PORTRAIT_LOAD;
    eventNameBannerTouch = NAME.TVSERIES_TRIAL_EPISODES_BANNER_PORTRAIT_TOUCH;
    flowName = numberTrialEpisode
      ? VALUE.TRIGGER_BY_CONTENT_MOVIE_TVSERIES_TRIAL_EPISODES
      : VALUE.TRIGGER_BY_CONTENT_MOVIE_TVSERIES_ONLY_IN_APP;
  }
  if (type === CONTENT_TYPE.MOVIE) {
    eventNameInfoboxLoad = NAME.MOVIE_TRIAL_15P_INFOBOX_LANDSCAPE_LOAD;
    eventNameInfoboxTouch = NAME.MOVIE_TRIAL_15P_INFOBOX_LANDSCAPE_TOUCH;
    eventNameBannerLoad = NAME.MOVIE_TRIAL_15P_BANNER_LOAD;
    eventNameBannerTouch = NAME.MOVIE_TRIAL_15P_BANNER_TOUCH;
    flowName = VALUE.TRIGGER_BY_CONTENT_MOVIE_TRIAL_15P;
  }
  return {
    eventNameInfoboxLoad,
    eventNameInfoboxTouch,
    eventNameBannerLoad,
    eventNameBannerTouch,
    flowName
  };
};

export const handleTrackingDialogMWebToApp = ({ popupName, porTrait, numberTrialEpisode }: any) => {
  let eventNameDialogLoad;
  let eventNameDialogTouch;
  let eventNameDialogClose;
  let flowName;

  switch (popupName) {
    case POPUP.NAME.CONVERT_MOBILE_WEB_TO_APP:
      eventNameDialogLoad = porTrait
        ? NAME.MOVIE_TRIAL_15P_DIALOG_PORTRAIT_LOAD
        : NAME.MOVIE_TRIAL_15P_DIALOG_LANDSCAPE_LOAD;
      eventNameDialogTouch = porTrait
        ? NAME.MOVIE_TRIAL_15P_DIALOG_PORTRAIT_TOUCH
        : NAME.MOVIE_TRIAL_15P_DIALOG_LANDSCAPE_TOUCH;
      eventNameDialogClose = porTrait
        ? NAME.MOVIE_TRIAL_15P_DIALOG_PORTRAIT_CLOSE
        : NAME.MOVIE_TRIAL_15P_DIALOG_LANDSCAPE_CLOSE;
      flowName = VALUE.TRIGGER_BY_CONTENT_MOVIE_TRIAL_15P;
      break;
    case POPUP.NAME.MOBILE_WEB_ONLY_APP:
      eventNameDialogLoad = numberTrialEpisode
        ? porTrait
          ? NAME.TVSERIES_TRIAL_EPISODES_DIALOG_PORTRAIT_LOAD
          : NAME.TVSERIES_TRIAL_EPISODES_DIALOG_LANDSCAPE_LOAD
        : NAME.DIALOG_CONTENT_ONLY_IN_APP_LOAD;
      eventNameDialogTouch = numberTrialEpisode
        ? porTrait
          ? NAME.TVSERIES_TRIAL_EPISODES_DIALOG_PORTRAIT_TOUCH
          : NAME.TVSERIES_TRIAL_EPISODES_DIALOG_LANDSCAPE_TOUCH
        : NAME.DIALOG_CONTENT_ONLY_IN_APP_TOUCH;
      eventNameDialogClose = numberTrialEpisode
        ? porTrait
          ? NAME.TVSERIES_TRIAL_EPISODES_DIALOG_PORTRAIT_CLOSE
          : NAME.TVSERIES_TRIAL_EPISODES_DIALOG_LANDSCAPE_CLOSE
        : NAME.DIALOG_CONTENT_ONLY_IN_APP_CLOSE;
      flowName = numberTrialEpisode
        ? VALUE.TRIGGER_BY_CONTENT_MOVIE_TVSERIES_TRIAL_EPISODES
        : VALUE.TRIGGER_BY_CONTENT_MOVIE_TVSERIES_ONLY_IN_APP;
      break;
    default:
      break;
  }
  return { eventNameDialogLoad, eventNameDialogTouch, eventNameDialogClose, flowName };
};

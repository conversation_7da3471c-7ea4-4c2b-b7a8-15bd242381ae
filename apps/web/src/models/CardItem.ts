import isEmpty from 'lodash/isEmpty';
import {
  getLiveTime,
  numberWithCommas,
  parsedTimeLive,
  parseRemainText,
  parseTagsData,
  parteExpiredDate,
  setLinkAttribute
} from '@helpers/common';
import { CONTENT_TYPE, CURRENCY, PREMIUM_TYPE, RIBBON_TYPE, TAG_TYPE } from '@constants/constants';
import ConfigImage from '@config/ConfigImage';
import { parseTimeExpiredTVod, setVideoPlayType } from '@services/contentService';
import { checkBetweenTime } from '@helpers/utils';
import { isMobile } from 'react-device-detect';
import { setStartTimeLiveStream } from '@services/datetimeServices';
import { parseStartTimeToCurrent, setLive } from './epgItem';
import {
  Images,
  LinkPlay,
  RankingText,
  RelatedSeason,
  SeoItem,
  Triggers,
  TriggersGlobal
} from './subModels';

class CardItem {
  ageRange: any;
  allowClick: any;
  altSEOImg: any;
  avgRate: any;
  canLimit: any;
  castStr: any;
  category: any;
  categoryTags: any;
  categoryText: any;
  comingSoon: any;
  contentMsg: any;
  currentEpisode: any;
  defaultEpisode: any;
  defaultImgSrc: any;
  displayImageType: any;
  drmServiceName: any;
  endTime: any;
  episode: any;
  expirationMsg: any;
  expiredDate: any;
  externalUrl: any;
  forceLogin: any;
  genre: any;
  genreText: any;
  groupId: any;
  hasObjectDetection: any;
  hasPVOD: any;
  href: any;
  id: any;
  images: any;
  imgSrc: any;
  index: any;
  isAiring: any;
  isArtist: any;
  isBroadcasting: any;
  isCatchUp: any;
  isComingSoon: any;
  isDRM: any;
  isDetailPage: any;
  isEnd: any;
  isEpisode: any;
  isEpisodeTrialInApp: any;
  isFavorite: any;
  isGlobal: any;
  isLive: any;
  isLiveStream: any;
  isLiveTv: any;
  isMasterBanner: any;
  isMovieTrialInApp: any;
  isNew: any;
  isOriginal: any;
  isPoster: any;
  isPremiere: any;
  isPremium: any;
  isPremiumDisplay: any;
  isPremiumPVod: any;
  isPremiumPVodHaveSVod: any;
  isPremiumPVodNotSVod: any;
  isPremiumTVod: any;
  isSearchPage: any;
  isSvodTvod: any;
  isTV: any;
  isTopView: any;
  isTriggerRegister: any;
  isTriggerToApp: any;
  isUpComingSoon: any;
  isVip: any;
  isWatchLater: any;
  isWatchMoreRibbon: any;
  knownAs: any;
  labelPublicDay: any;
  labelSubtitleAudio: any;
  linkPlay: any;
  longDescription: any;
  moreInfo: any;
  movie: any;
  noSeeker: any;
  numberTrialEpisode: any;
  optionDirect: any;
  optionDirectPackageId: any;
  packages: any;
  people: any;
  playerLogo: any;
  previewEnabled: any;
  progress: any;
  progressPercent: any;
  pvod: any;
  randomID: any;
  rangePageIndex: any;
  ranking: any;
  rankingText: any;
  relatedSeason: any;
  releaseYear: any;
  remainText: any;
  resolution: any;
  ribbonId: any;
  ribbonName: any;
  ribbonOrder: any;
  ribbonType: any;
  runTime: any;
  seasonName: any;
  seo: any;
  shareUrl: any;
  shareUrlSeo: any;
  shortDescription: any;
  showAotp: any;
  slug: any;
  startText: any;
  startTime: any;
  subtitle: any;
  tagData: any;
  tagDisPlay: any;
  tagGenreComingSoon: any;
  tags: any;
  thumbClass: any;
  timeFutureActive: any;
  timeLiveStream: any;
  timeLiveTv: any;
  title: any;
  totalRate: any;
  trackingData: any;
  trialDuration: any;
  triggerLoginDuration: any;
  triggers: any;
  tvod: any;
  type: any;
  videoPlayType: any;
  vodSchedule: any;
  warningMessage: any;
  warningScreen: any;
  warningTag: any;
  windowingMessage: any;
  constructor(props: any, index?: any) {
    this.id = props?.id;
    this.isGlobal = props?.isGlobal;
    this.isUpComingSoon = props?.isUpComingSoon;
    this.isBroadcasting = props?.isBroadcasting;
    this.index = index || 0;
    this.isTV = props?.isTV;
    this.trackingData = {};
    this.isSearchPage = props?.isSearchPage;
    this.ribbonId = props?.ribbonId;
    this.ribbonType = props?.ribbonType;
    this.ribbonOrder = props?.ribbonOrder;
    this.ribbonName = props?.ribbonName || props?.name;
    this.seasonName = props?.seasonName;
    this.isMasterBanner = !!props?.isMasterBanner;
    this.knownAs = props?.known_as || '';
    this.isAiring = !!props?.isAiring;
    this.hasObjectDetection = !!props?.has_object_detection;
    this.isOriginal = this.ribbonType === RIBBON_TYPE.ORIGINAL;
    this.isTopView = this.ribbonType === RIBBON_TYPE.TOP_VIEWS;
    this.thumbClass = props?.thumbClass || '';
    this.isWatchMoreRibbon = this.ribbonType === RIBBON_TYPE.WATCH_MORE;
    if (props?.group_id) this.groupId = props?.group_id;
    if (props?.title) this.title = props?.title;
    this.genre = props?.genre;
    if (props?.resolution) this.resolution = props?.resolution;
    this.images = Images({ images: props?.images });
    this.category = props?.category;
    this.rangePageIndex = props?.range_page_index;
    this.defaultEpisode = props?.default_episode?.id
      ? new CardItem({ ...props?.default_episode, isGlobal: this.isGlobal })
      : null;
    this.avgRate = props?.avg_rate;
    this.totalRate = props?.total_rate;
    this.defaultImgSrc =
      this.isPoster || this.isOriginal ? ConfigImage.defaultPoster : ConfigImage.defaultBanner16x9;
    this.isFavorite = props.is_favorite || false;
    this.isWatchLater = props?.is_watchlater || false;
    this.isPremium = props?.is_premium >= 1;
    this.isPremiumTVod = props?.is_premium === PREMIUM_TYPE.TVOD;
    this.isPremiumPVod =
      props?.is_premium === PREMIUM_TYPE.PVOD_6 || props?.is_premium === PREMIUM_TYPE.PVOD_7;
    this.isPremiumPVodHaveSVod = props?.is_premium === PREMIUM_TYPE.PVOD_6;
    this.isPremiumPVodNotSVod = props?.is_premium === PREMIUM_TYPE.PVOD_7;
    this.isSvodTvod = props?.is_premium === PREMIUM_TYPE.SVOD_TVOD;
    this.isPremiere = props?.is_premiere === 1;
    this.showAotp = props?.show_aotp === 1;
    this.isPremiumDisplay =
      props?.type === CONTENT_TYPE.EPG
        ? props?.more_info?.is_premium_display || props?.is_premium_display
        : props?.is_premium_display;

    this.isTriggerToApp = props?.is_trigger_to_app === 1;
    this.isTriggerRegister = props?.is_trigger_register > 0;
    this.numberTrialEpisode = props?.number_trial_episode;
    this.isMovieTrialInApp =
      this.isTriggerToApp &&
      this.trialDuration > 0 &&
      isMobile &&
      props.type === CONTENT_TYPE.MOVIE;
    this.isEpisodeTrialInApp = this.isTriggerToApp && this.numberTrialEpisode > 0 && isMobile;
    this.drmServiceName =
      props?.type === CONTENT_TYPE.EPG
        ? props?.more_info?.drm_service_name
        : props?.drm_service_name;
    this.videoPlayType = setVideoPlayType({
      isVip: this.isVip,
      isPremiumTVod: this.isPremiumTVod
    });
    this.isNew = props?.is_new === 1;
    this.isVip = props?.is_vip === 1;
    this.trialDuration = Math.floor(props?.trial_duration / 60) || 0;
    this.windowingMessage = props?.windowing_message;
    this.isComingSoon = props?.is_coming_soon === 1 || this.ribbonType === RIBBON_TYPE.COMING_SOON;
    this.isCatchUp = props?.is_catch_up;
    this.previewEnabled = !!props?.preview_enabled;
    this.isDRM = props?.drm_service_name && props?.asset_id;
    this.isEnd = props?.is_end;
    this.hasPVOD = !!props?.has_pvod;
    this.isTriggerToApp = props?.is_trigger_to_app;
    this.numberTrialEpisode = props?.number_trial_episode;
    this.isEpisodeTrialInApp = this.isTriggerToApp && isMobile;
    this.moreInfo = props?.more_info;
    this.playerLogo = props?.player_logo;
    this.noSeeker = props?.no_seeker;
    this.pvod = props?.pvod || {};
    this.contentMsg = props?.pvod?.content_msg && props?.pvod?.content_msg.replace('_', ' đến ');
    this.expirationMsg = props?.pvod?.expiration_msg && props?.pvod?.expiration_msg;
    this.warningMessage = props?.warning_message;
    this.warningScreen = props?.warning_screen;
    this.warningTag = props?.warning_tag;
    this.timeLiveTv = parsedTimeLive(
      props?.more_info?.programme?.time_start,
      props?.more_info?.programme?.time_end
    );
    this.timeLiveStream = getLiveTime(props?.start_time);
    this.imgSrc = this.isTopView
      ? this?.images?.poster
      : this.isOriginal
      ? this?.images?.posterOriginal
      : this?.images.thumbnail;
    this.episode = props?.episode || 0;
    this.currentEpisode = props?.current_episode || '';
    this.shortDescription = props?.short_description || '';
    this.longDescription = props?.long_description || '';
    this.seo = SeoItem(props?.seo);
    const { href } = setLinkAttribute(props?.type, props?.seo?.url);
    this.href = href;
    this.slug = props?.slug;
    this.displayImageType = props?.display_image_type;
    this.subtitle = props?.subtitle;
    this.shareUrl = props?.share_url;
    this.shareUrlSeo = props?.share_url_seo;
    this.labelPublicDay = props?.label_public_day;
    this.labelSubtitleAudio = props?.label_subtitle_audio;
    this.linkPlay = LinkPlay(props);
    this.people = props?.people;
    this.releaseYear = props?.release_year;
    this.tagDisPlay =
      props?.tags_display?.length > 0
        ? props?.tags_display
        : // : this.hasObjectDetection
          //   ? ['NỘI DUNG', 'TƯƠNG TÁC']
          [];
    this.ranking = props?.ranking;
    this.movie = props?.movie;
    this.seasonName = props?.season_name;
    this.rankingText = RankingText(props);
    this.externalUrl = props?.external_url;
    this.allowClick = props?.allow_click;
    this.ageRange = props?.age_range;
    this.isArtist = props?.is_artist;
    this.tags = props?.tags;
    this.tagGenreComingSoon = (props?.tags || [])?.filter(
      (item: any) => item.type === TAG_TYPE.GENRE
    );
    if (props?.is_premium === PREMIUM_TYPE.TVOD) {
      this.genreText = props?.tags_genre_txt;
    }
    this.packages = props?.packages;
    this.optionDirect = props?.option_direct;
    this.optionDirectPackageId = props?.option_direct_package_id;
    this.tvod = this.settingTVod(props);
    if (props?.tags?.length > 0) {
      if (!this.genreText) {
        // const genre = (props?.tags || []).find((item) => item?.type === TAG_TYPE.GENRE);
        const genreNames = (props?.tags || [])
          .filter((tag: any) => tag.type === 'genre')
          .map((tag: any) => tag.name)
          .join(', ');

        this.genreText = genreNames || '';
      }
      const category = (props?.tags || []).find((item: any) => item?.type === TAG_TYPE.CATEGORY);
      this.categoryText = category?.name || '';
    }
    this.categoryTags = (props?.tags || [])
      .filter((item: any) => item && item.type === 'genre')
      .filter((item: any, index: any) => item && index < 3);
    this.type = this.ribbonType === RIBBON_TYPE.EPG ? CONTENT_TYPE.EPG : props?.type;
    this.relatedSeason = (props?.related_season || []).map((item: any) => RelatedSeason(item));
    this.runTime = props?.runtime;
    this.startTime = props?.start_time || this.moreInfo?.programme?.time_start;
    this.endTime = props?.end_time || this.moreInfo?.programme?.time_end;
    this.isLiveTv =
      this.type === CONTENT_TYPE.LIVE_TV ||
      this.type === RIBBON_TYPE.FAVORITE_LIVE_TV ||
      this.type === CONTENT_TYPE.EPG;
    this.isLiveStream = props?.type === CONTENT_TYPE.LIVESTREAM;
    this.isLive = props?.is_live > 0; // this.type === CONTENT_TYPE.EPG
    this.forceLogin = props?.force_login;
    this.triggerLoginDuration = props?.trigger_login_duration;
    this.castStr = props?.cast_str;
    const { startText } = setLive(this.startTime);

    if (this.type === CONTENT_TYPE.EPG || this.type === CONTENT_TYPE.LIVE_TV) {
      this.startText = startText;
    }
    if (this.type === CONTENT_TYPE.LIVESTREAM) {
      this.startText = setStartTimeLiveStream(this.startTime, this.isLive, this.isPremiere);
    }
    this.timeFutureActive = parseStartTimeToCurrent(this.moreInfo?.programme?.time_start);
    this.isDetailPage = this.type === CONTENT_TYPE.SEASON || this.type === CONTENT_TYPE.MOVIE;
    this.progress = props?.progress;
    const { progressPercent, remainText } = parseRemainText(props?.progress, props?.runtime * 60);
    this.progressPercent = progressPercent;
    this.remainText = remainText;
    this.vodSchedule = props?.vod_schedule;
    this.expiredDate = parteExpiredDate(props?.expire_date);
    this.isEpisode = props?.isEpisode ?? false;
    // add  SEO ALT IMAGE
    this.altSEOImg = this.setAltSEOImg(props, index);
    this.triggers = this.isGlobal ? TriggersGlobal(this) : Triggers(this);
    this.tagData = parseTagsData(this);
    this.randomID = Math.floor(Math.random() * 10000) + (index || 0);
    this.canLimit = props?.can_limit;
    this.comingSoon = {
      broadcastSince: props?.coming_soon?.broadcast_since,
      broadcasting: props?.coming_soon?.broadcasting,
      availableFreeEps: props?.coming_soon?.available_free_eps,
      availableVipEps: props?.coming_soon?.available_vip_eps,
      premiereDay: props?.coming_soon?.premiere_day,
      epsIdCurrent: props?.coming_soon?.eps_id_current,
      numRelatedSeason: props?.coming_soon?.num_related_season,
      runtimeDuration: props?.coming_soon?.runtime_duration
    };
  }
  getTVodPreOder = (data: any) => {
    if (isEmpty(data)) return null;
    const startedAt = data?.pre_order_started_at;
    const endedAt = data?.pre_order_ended_at;
    const price = data?.pre_order_price;
    const isPreOrdering = checkBetweenTime({
      started: startedAt,
      ended: endedAt
    });

    return {
      startedAt,
      endedAt,
      isPreOrdering,
      price
    };
  };

  settingTVod = (props: any) => {
    const data = props?.tvod;
    if (isEmpty(data)) return null;
    const { strTimeInfoBox, strTimeStandard, strTimeTag, titleTVod } = parseTimeExpiredTVod({
      expiredTime: data?.benefit_ended_at,
      title: props.title,
      type: props?.type
    });

    const tvod: any = {
      price: data?.price,
      benefitEndedAt: data?.benefit_ended_at,
      benefitType: data?.benefit_type,
      isLiveEvent: props?.type === CONTENT_TYPE.LIVESTREAM,
      isSimulcast: props?.is_simulcast || false,
      preOrder: this.getTVodPreOder(data?.pre_order),
      strTimeInfoBox,
      titleTVod,
      strTimeStandard,
      strTimeTag
    };
    tvod.preOrderRemainTimeText = this.setPreOrderRemainTimeText({ tvod });
    return tvod;
  };
  setAltSEOImg = (props: any, index: any) => {
    let altSEOImg = props?.seo?.title || props?.title || props?.movie?.title;
    if (props?.seasonName) altSEOImg = `${altSEOImg} - ${props?.seasonName}`;
    if (props?.type === CONTENT_TYPE.EPISODE) altSEOImg = props?.seo?.title_seo_tag;
    if (props?.people_str) {
      const indexText = (index || 0) + 1 < 10 ? `0${(index || 0) + 1}` : (index || 0) + 1;
      altSEOImg = `${altSEOImg} - ${indexText} - ${props?.people_str}`;
    }
    return altSEOImg || 'VieON';
  };
  setPreOrderRemainTimeText = ({ tvod }: any) => {
    let text = '';
    if (!tvod?.preOrder) return '';
    const price = tvod?.price || 0;
    const preOrderEndedAt = tvod?.preOrder?.endedAt || 0;
    const { strTimeStandard } = parseTimeExpiredTVod({ expiredTime: preOrderEndedAt });
    if (strTimeStandard) {
      text = `Giá ưu đãi chỉ còn <span class="text-bold">${strTimeStandard}</span> | Giá gốc: <span class="text-bold">${numberWithCommas(
        price
      )} ${CURRENCY.VND}</span>`;
    }
    return text;
  };
}

export default CardItem;

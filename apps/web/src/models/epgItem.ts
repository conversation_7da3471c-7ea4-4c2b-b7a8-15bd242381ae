import moment from 'moment';
import { Images } from '@models/subModels';
import { parseTimeToText } from '@helpers/common';

class EpgItem {
  dashLinkPlay: any;
  duration: any;
  end: any;
  forceLogin: any;
  hlsLinkPlay: any;
  id: any;
  images: any;
  isCatchUp: any;
  isComingSoon: any;
  isLive: any;
  isNotifyComingSoon: any;
  linkPlay: any;
  seo: any;
  slug: any;
  start: any;
  time: any;
  timeTitle: any;
  title: any;
  triggerLoginDuration: any;
  constructor(data: any, iOS?: any, isConcurrentScreenLimit?: any) {
    this.id = data?.id;
    this.title = data?.title;
    this.isCatchUp = data?.is_catch_up || false;
    this.isLive = checkIsLive(data?.time_start, data?.time_end);
    this.isComingSoon = data?.is_coming_soon || false;
    this.timeTitle = setEpgTitle(data);
    this.time = moment(data?.time_start * 1000).format('HH:mm');
    this.start = data?.time_start;
    this.end = data?.time_end;
    this.isNotifyComingSoon = data?.isNotifyComingSoon || false;
    this.slug = data?.slug;
    this.seo = data?.seo;
    this.dashLinkPlay = isConcurrentScreenLimit ? '' : data?.dash_link_play;
    this.hlsLinkPlay = isConcurrentScreenLimit ? '' : data?.hls_link_play;
    this.linkPlay = isConcurrentScreenLimit ? '' : iOS ? this.hlsLinkPlay : this.dashLinkPlay;
    this.duration = data?.duration;
    this.images = Images({ images: data?.images });
    this.forceLogin = data?.force_login;
    this.triggerLoginDuration = data?.trigger_login_duration;
  }
}

const checkIsLive = (timeStart: any, timeEnd: any) => {
  if (!timeStart || !timeEnd) return false;
  const timeNow = Math.floor(Date.now() / 1000);
  return timeNow >= timeStart && timeNow < timeEnd;
};

const setEpgTitle = (data: any) => {
  let timeTitle = data?.title;
  const start = data?.time_start;
  const end = data?.time_end;
  const startTitle = moment(start * 1000).format('HH:mm');
  const endTitle = moment(end * 1000).format('HH:mm');
  if (startTitle && endTitle && end > start) {
    timeTitle = `${startTitle} - ${endTitle}`;
  }
  return timeTitle;
};

export const setLive = (start: any) => {
  const currentTime = new Date().getTime() / 1000;
  const hoursText = moment(new Date(start * 1000)).format('HH:mm');
  let startText = '';
  if (currentTime < start) {
    const parseText = parseStartText(start);
    startText = `${hoursText}, còn khoảng ${parseText?.startText || ''}`;
    if (parseText?.dates >= 1) {
      const dateText = moment(new Date(start * 1000)).format('DD/MM');
      startText = `${hoursText} ngày ${dateText}`;
    }
  }
  return { startText };
};

export const parseStartText = (start: any) => {
  const currentTime = Date.now() / 1000;
  const seconds = Math.abs(start - currentTime);
  const parseText: any = parseTimeToText(seconds);
  const startText = parseText?.text || '';
  return { startText, ...parseText };
};
export const parseStartTimeToCurrent = (start: any) => {
  if (!start || start === 0) return false;
  const currentTime = Date.now() / 1000;
  const seconds = Math.abs(start - currentTime);
  return seconds;
};

export default EpgItem;

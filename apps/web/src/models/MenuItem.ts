import { PAGE, SLUG } from '@constants/constants';

class MenuItem {
  active: any;
  dataSlug: any;
  displayType: any;
  dotCode: any;
  haveBanner: any;
  highlights: any;
  href: any;
  iconText: any;
  id: any;
  isMainMenu: any;
  layoutType: any;
  menuType: any;
  menuUrl: any;
  name: any;
  order: any;
  parentId: any;
  primary: any;
  quantityRibbon: any;
  seo: any;
  slug: any;
  subMenu: any;
  subMenuRibbon: any;
  tag: any;
  titleRibbon: any;
  urlRedirect: any;
  constructor(props: any, isSubMenu: any, { menuOrder, subMenuOrder }: any) {
    const isHaveSubMenu = !isSubMenu;
    this.id = props?.id;
    this.isMainMenu = !isSubMenu;
    this.name = props?.name;
    this.iconText = props?.icon_text;
    this.seo = props?.seo;
    this.primary = props?.primary === 1;
    this.menuUrl = props?.sub_menu?.[0]?.seo?.url;
    this.href = this.setHrefFromUrl(props);
    this.slug = props?.slug;
    this.dotCode = props?.dot_code;
    this.menuType = props?.menu_type;
    this.displayType = props?.display_type;
    this.order = isSubMenu ? (subMenuOrder || 0) - 1 : menuOrder || 0;
    this.dataSlug = isHaveSubMenu ? (props?.sub_menu || [])?.[0]?.seo?.url : '';
    this.parentId = props?.parent_id;
    this.haveBanner = props?.have_banner;
    this.titleRibbon = props?.title_ribbon;
    this.quantityRibbon = props?.quantity_ribbon;
    this.subMenuRibbon = props?.sub_menu_ribbon;
    this.subMenu = this.setSubMenu({ data: props?.sub_menu || props?.subMenu });
    this.tag = props?.tag;
    this.active = false;
    this.layoutType = props?.layout_type;
    this.highlights = this.primary ? this.parseHighlightMenu(props?.highlights || []) : [];
    this.urlRedirect = props?.url_redirect;
  }

  parseHighlightMenu = (listData = []) =>
    listData.map((item: any, i: any) => ({
      id: item?.id,
      seo: item?.seo,
      href: this.setHrefFromUrl(item),
      name: item?.name,
      order: i
    }));
  setSubMenu = ({ data }: any) =>
    (data || []).map((item: any, index: any) => new MenuItem(item, true, { subMenuOrder: index }));
  setHrefFromUrl = ({ slug, seo, primary }: any) => {
    let href = PAGE.CATEGORY;
    const { url } = seo || {};

    if ((url || '').includes('/m/')) href = PAGE.PAGE_MENU;
    else if ((url || '').includes('/r/')) href = PAGE.COLLECTION;
    else if ((url || '').includes('/t/')) href = PAGE.TAG;

    if (primary === 1) {
      href = PAGE.HOME;
    } else {
      switch (slug) {
        case SLUG.LIVE_TV:
          href = PAGE.LIVE_TV;
          break;
        case SLUG.LIVE_STREAM:
          href = PAGE.LIVE_STREAM;
          break;
        case SLUG.SPORT:
          href = PAGE.SPORT;
          break;
        default:
          break;
      }
    }

    return href;
  };
}

export default MenuItem;

import { setLinkAttribute, setLinkAttributeNotify } from '@helpers/common';
import { ACTION_TYPE_NOTIFY } from '@constants/constants';
import { SeoItem } from './subModels';

class MessageItem {
  action: any;
  content: any;
  createdAt: any;
  entityId: any;
  episodeId: any;
  expiredDate: any;
  externalLink: any;
  href: any;
  id: any;
  image: any;
  isNotiTypeAnnounce: any;
  isRead: any;
  layoutType: any;
  layoutTypeImage: any;
  layoutTypeInfo: any;
  notiType: any;
  packageId: any;
  receivedAt: any;
  seo: any;
  title: any;
  type: any;
  updatedAt: any;
  constructor(props: any) {
    Object.assign(this, props);
    this.id = props.id;
    this.title = props.title;
    this.content = props.content;
    this.image = props.image;
    this.isRead = props.is_read;
    this.receivedAt = props.received_at;
    this.expiredDate = props.expired_date;
    this.createdAt = new Date(props.created_at).getTime();
    this.updatedAt = props.updated_at;
    this.notiType = props.noti_type;
    this.isNotiTypeAnnounce = props.noti_type === 1;
    this.layoutType = props.layout_type;
    this.layoutTypeImage = props.layout_type === 0;
    this.layoutTypeInfo = props.layout_type === 1;
    this.action = props.target_link?.action;
    this.externalLink = props.target_link?.external_link;
    this.packageId = props.target_link?.package_id;
    this.type = props.target_link?.entity_type;
    this.entityId = props.target_link?.entity_id;
    this.episodeId = props.target_link?.episode_id;
    this.seo = SeoItem(props?.target_link?.seo);
    // if (this.type === CONTENT_TYPE.EPG) {
    //   this.seo = SeoItem(props?.target_link.programme?.seo);
    // }
    const { href } = setLinkAttribute(this.type);
    const hrefCustom = setLinkAttributeNotify({ action: this.action, packageId: this.packageId });
    this.href = this.setLinkHref({ action: this.action, hrefCustom, href });
  }

  setLinkHref = (data: any) => {
    const { action, hrefCustom, href } = data;
    if (action === ACTION_TYPE_NOTIFY.PAYMENT || action === ACTION_TYPE_NOTIFY.FAVORITE)
      return hrefCustom;
    if (
      action === ACTION_TYPE_NOTIFY.DETAIL ||
      action === ACTION_TYPE_NOTIFY.PLAYER ||
      action === ACTION_TYPE_NOTIFY.BROWSER
    )
      return href;
    return '';
  };
}

export default MessageItem;

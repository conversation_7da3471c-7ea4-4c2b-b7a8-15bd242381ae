import isEmpty from 'lodash/isEmpty';
import find from 'lodash/find';
import moment from 'moment';
import { ADS_POINT, ERROR_CODE, PAGE, PREMIUM_TYPE } from '@constants/constants';
import { checkPosition, getUpcommingTime, parseAdsLink } from '@helpers/common';
import { convertToDateTime } from '@helpers/utils';
import { Images, LinkPlaysToRetry, TrailerLinkPlay } from './subModels';

class StreamItem {
  ads: any;
  altSEOImg: any;
  buttonLive: any;
  classPosition: any;
  comingSoonTimeString: any;
  contentConcurrentGroup: any;
  contentId: any;
  drmMerchant: any;
  drmProvider: any;
  drmServiceName: any;
  forceLogin: any;
  href: any;
  id: any;
  images: any;
  isAutoLoadMainLink: any;
  isBanner: any;
  isComingSoon: any;
  isComingSoonDefault: any;
  isConcurrentScreenLimit: any;
  isDVR: any;
  isDrm: any;
  isEndStream: any;
  isLive: any;
  isPremiere: any;
  isPremiumTVod: any;
  isVip: any;
  labelPublicDay: any;
  linkPlay: any;
  linkPlaysToRetry: any;
  longDescription: any;
  packages: any;
  permission: any;
  positionLiveCCU: any;
  props: any;
  seo: any;
  shareURL: any;
  showCCU: any;
  slug: any;
  softLogo: any;
  startTime: any;
  timeEnd: any;
  timeStart: any;
  timeStartLiveStream: any;
  title: any;
  totalWard: any;
  trailerLinkPlay: any;
  type: any;
  viewPlus: any;
  warningMessage: any;
  warningScreen: any;
  warningTag: any;
  constructor(props: any, index?: any) {
    this.id = props?.id;
    const preItem = find(parseAdsLink(props?.ads), ['type', ADS_POINT.PRE]);
    if (!isEmpty(preItem)) this.ads = [preItem];
    this.title = props?.title;
    this.timeStart = props?.str_to_time;
    this.startTime = props?.start_time;
    this.timeStartLiveStream = parseInt(
      Math.floor(new Date(props?.str_to_time * 1000).getTime() / 1000).toString(),
      10
    );
    this.timeEnd = props?.str_to_end_time;
    this.isLive = props?.is_live;
    this.contentId = props?.content_id;
    this.isPremiumTVod = props?.is_premium === PREMIUM_TYPE.TVOD;
    this.labelPublicDay = `${props.is_premiere ? 'Công chiếu' : 'Trực tiếp'} lúc ${moment(
      props?.str_to_time * 1000
    ).format('HH:mm')}, ngày ${moment(props?.str_to_time * 1000).format('DD/MM')}`;
    this.slug = props?.slug;
    this.href = PAGE.LIVE_STREAM;
    this.softLogo = props?.player_logo;
    this.isVip = props?.is_premium === PREMIUM_TYPE.SVOD;
    this.isComingSoon = props?.is_coming_soon > 0;
    this.isComingSoonDefault = props?.is_coming_soon;
    this.type = props?.type;
    this.packages = props?.packages;
    this.viewPlus = props.view_plus;
    this.totalWard = props.total_award;
    this.showCCU = props.show_ccu;
    this.positionLiveCCU = props.position_live_ccu;
    this.classPosition = checkPosition(this.positionLiveCCU);
    this.buttonLive = props.button_live;
    this.trailerLinkPlay = TrailerLinkPlay(props?.trailer_link_play);

    this.isAutoLoadMainLink = !!(
      this.timeStartLiveStream - parseInt(Math.floor(new Date().getTime() / 1000).toString(), 10) >
        0 &&
      (this.trailerLinkPlay?.hls || this.trailerLinkPlay?.dash)
    );

    this.comingSoonTimeString = getUpcommingTime(props?.start_time, props.is_premiere);
    this.permission = props?.permission;
    this.contentConcurrentGroup = props?.content_concurrent_group || '';
    this.shareURL = props?.seo?.share_url;
    this.isEndStream = !props?.is_coming_soon && !props?.is_live;
    this.linkPlaysToRetry =
      props?.concurrentScreen?.code === ERROR_CODE.CODE_2 ? [] : LinkPlaysToRetry(props);
    this.linkPlay =
      props?.concurrentScreen?.code === ERROR_CODE.CODE_2 || this.isEndStream
        ? ''
        : this.linkPlaysToRetry?.[0];
    this.isPremiere = props?.is_premiere === 1;
    this.isDVR = this.linkPlay?.includes('dvr=1');
    this.isConcurrentScreenLimit = props.concurrentScreen?.code === ERROR_CODE.CODE_2;
    this.images = Images({ images: props?.images });
    this.seo = props?.seo;
    this.longDescription = props?.long_description || '';
    this.forceLogin = props?.force_login;
    this.warningScreen = props?.warning_screen || '';
    this.warningTag = props?.warning_tag;
    this.warningMessage = props?.warning_message;
    this.isBanner = props?.is_banner;

    // add  SEO ALT IMAGE
    this.altSEOImg = this.setAltSEOImg(props, index);

    // TODO: [FOREST] todo check
    // DRM
    this.drmMerchant = props?.drm_merchant || '';
    this.drmProvider = props?.drm_provider || '';
    this.drmServiceName = (props?.drm_service_name || '').toUpperCase();
    this.isDrm = !!this.drmServiceName;
  }

  setAltSEOImg = (props: any, index: any) => {
    let altSEOImg = this.props?.seo?.title;
    if (props?.people?.director || props?.people?.actor) {
      const indexText = (index || 0) + 1 < 10 ? `0${(index || 0) + 1}` : (index || 0) + 1;
      const directors = (props?.people?.director || []).map((item: any) => item.name).join(' – ');
      const actors = (props?.people?.actor || []).map((item: any) => item.name).join(' – ');
      altSEOImg = `${props?.seo?.title} - ${indexText}`;
      if (directors) altSEOImg += ` - ${directors}`;
      if (actors) altSEOImg += ` - ${actors}`;
    }
    return altSEOImg;
  };
}

export const parseTimeCommentLive = ({ timestamp }: any) => {
  const date = new Date(timestamp * 1000);
  const now = new Date();
  const today = now.getDay();
  // Hours part from the timestamp
  const hours = date.getHours();
  // Minutes part from the timestamp
  const minutes = `0${date.getMinutes()}`;
  // Seconds part from the timestamp

  // Will display time in 10:30:23 format
  let formattedTime = `${hours}:${minutes.substr(-2)}`;
  if (today - date.getDay() >= 1) formattedTime = `${today - date.getDay()} ngày trước`;
  return formattedTime;
};

export class StreamComment {
  avatar: any;
  comment: any;
  id: any;
  name: any;
  timeStamp: any;
  userID: any;
  constructor(props: any) {
    this.id = props?.comment_id;
    this.userID = props?.user_id;
    this.name = props?.name;
    this.avatar = props?.avatar;
    this.comment = props?.comment;
    this.timeStamp = convertToDateTime(String(props?.created_at * 1000), 'HH:mm DD/MM/YYYY');
  }
}

export default StreamItem;

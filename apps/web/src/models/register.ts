class Register {
  accessToken: any;
  expireAt: any;
  isExist: any;
  isFirstLogin: any;
  message: any;
  phoneNumber: any;
  profile: any;
  registerSessionId: any;
  constructor(props: any) {
    this.expireAt = props?.expire_at;
    this.phoneNumber = props?.phone_number;
    this.registerSessionId = props?.register_session_id;
    this.isExist = props?.data === 'Exist';
    this.message = props?.message;
    this.accessToken = props?.access_token;
    this.isFirstLogin = props?.is_first_login;
    this.profile = props?.profile || {};
  }
}

export default Register;

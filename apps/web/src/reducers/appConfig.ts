import { ACTION_TYPE } from '@actions/actionType';
import initialState from './initialState';

const appConfigReducer = (state: any = initialState.appConfig, { type, data }: any) => {
  switch (type) {
    case ACTION_TYPE.GET_CONTENT_CONFIG_SUCCESS:
      return { ...state, content: data };
    case ACTION_TYPE.GET_TRIGGER_CONFIG_SUCCESS:
      return { ...state, trigger: data };
    case ACTION_TYPE.GET_PLAYER_CONFIG_SUCCESS:
      return { ...state, playerConfig: data };
    default:
      return state;
  }
};

export default appConfigReducer;

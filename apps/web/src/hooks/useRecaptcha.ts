'use client';

import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';

export const useReCaptcha = () => {
  const { executeRecaptcha } = useGoogleReCaptcha();

  const getToken = async (action: string): Promise<string | null> => {
    try {
      if (!executeRecaptcha) {
        throw new Error('ReCaptcha initialization fail');
      }

      const token = await executeRecaptcha(action);
      return token;
    } catch (error: any) {
      return error.message;
    }
  };

  return { getToken };
};

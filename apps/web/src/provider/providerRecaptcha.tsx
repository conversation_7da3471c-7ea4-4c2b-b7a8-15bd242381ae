'use client';

import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { useEffect } from 'react';
import { RECAPTCHA_SITE_KEY } from '@/config/ConfigEnv';

type Props = {
  children: React.ReactNode;
};

const ReCaptchaProvider = ({ children }: Props) => {
  const siteKey = RECAPTCHA_SITE_KEY || '';

  useEffect(() => {
    if (!siteKey) {
      console.error('ReCaptcha site key is not set. Please check your environment variables.');
    }
  }, [siteKey]);

  if (!siteKey) {
    return <>{children}</>;
  }

  return (
    <GoogleReCaptchaProvider
      reCaptchaKey={siteKey}
      language="vi"
      useEnterprise={true}
      scriptProps={{
        async: true,
        defer: true,
        appendTo: 'head',
        nonce: undefined
      }}
    >
      {children}
    </GoogleReCaptchaProvider>
  );
};

export default ReCaptchaProvider;

import { DOMAIN_WEB } from '@config/ConfigEnv';
import ConfigImage from '@config/ConfigImage';

export const AUTH_PROVIDER = {
  MOBILE: 0,
  EMAIL: 1,
  GOOGLE: 2,
  FACEBOOK: 3,
  APPLE: 4,
  HOTEL: 5
};

export const DATA_DROPDOWN_COUNTRY = [
  {
    title: 'Việt Nam',
    key: '+84',
    value: 'VN',
    isDefault: true
  },
  {
    title: 'Mỹ',
    key: '+1',
    value: 'US'
  }
];

export const FLOW_GLOBAL_AUTH = {
  OPTIONS_TO_CHOOSE: 'options',
  PHONE: 'phone',
  PHONE_REGISTER_OTP: 'phone_register_otp',
  PHONE_REGISTER_PASSWORD: 'phone_register_password',
  PHONE_LOGIN: 'phone_login',
  PHONE_FORGOT_PASSWORD_OTP: 'phone_forgot_password_otp',
  EMAIL: 'email',
  EMAIL_REGISTER_OTP: 'email_register_otp',
  EMAIL_REGISTER_PASSWORD: 'email_register_password',
  EMAIL_LOGIN: 'email_login',
  EMAIL_FORGOT_PASSWORD_OTP: 'email_forgot_password_otp',
  RESET_PASSWORD: 'reset_password',
  RESTORE_ACCOUNT_OTP: 'restore_account_otp',
  UPDATE_PASSWORD_OTP: 'update_password_otp',
  UPDATE_PASSWORD_SET_PASS: 'update_password_set_pass',
  LINK_PHONE_NUMBER: 'link_phone_number',
  LINK_PHONE_NUMBER_OTP: 'link_phone_number_otp',
  LINK_PHONE_NUMBER_SET_PASS: 'link_phone_number_set_pass',
  BIND_PHONE: 'bind_phone',
  BIND_PHONE_OTP: 'bind_phone_otp',
  BIND_PHONE_SET_PASS: 'bin_phone_set_pass'
};

export const GG_LIBRARY = 'https://accounts.google.com/gsi/client';
export const KEY_NAPAS_RESULT_WEB = 'napas_result_web';
export const KEY_NAPAS_RESULT_SMART_TV = 'napas_result_smart_tv';

export const PLATFORM = {
  WEB: 'web',
  MOBILE_WEB: 'mobile_web',
  SMART_TV: 'smart_tv',
  TABLET_WEB: 'tablet_web',
  SMART_TV_WEB: 'smarttv_web'
};

export const TIME_RESEND_OTP = 60;

export const ASK_LOGIN = 'ASK_LOGIN';

export const PRICE_GIFT_CODE_DEFAULT = 30000;

export const LOBBY = {
  AGE_RANGES: 'age_ranges',
  KID_AGE_RANGES: 'kid_age_ranges',
  GENDERS: 'genders'
};

export const STATUS_NOTIFY = {
  UNREAD: 0,
  READED: 1,
  DELETE: 2
};

export const SAFE_ZONE = {
  WIDTH_SCREEN_STANDAR: 1920,
  WIDTH_SAFE_ZONE: 144,
  HEIGHT_TOP_SAFE_ZONE: 175,
  HEIGHT_BOTTOM_SAFE_ZONE: 84,
  HEIGHT_INFO_BOX: 70,
  WIDTH_INFO_BOX: 400
};

export const ACTION_NOTIFY = {
  MARK_ALL: 'mark_all',
  MARK: 'mark',
  UN_MARK: 'unmark',
  READ_ALL: 'read_all',
  DELETE_ALL: 'delete_all',
  READ: 'read'
};

export const API_METHOD = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
  OPTIONS: 'OPTIONS'
};

export const PAYMENT = {
  METHOD_KEY: {
    ATM: 'ATM',
    EWALLET: 'E_WALLET',
    INTERNATIONAL_CARD: 'INTERNATIONAL_CARD',
    INTERNET_BANKING: 'INTERNET_BANKING',
    VNPAYQR: 'VNPAYQR',
    VNPAY: 'VNPAY',
    MOMO: 'Ví điện tử MoMo'
  },
  RETURN_URL: `${DOMAIN_WEB}/thanh-toan-goi-cuoc`
};

export const LOGIN_METHOD = {
  MOBILE: 'mobile',
  EMAIL: 'email',
  GOOGLE: 'google',
  FACEBOOK: 'facebook',
  APPLE: 'apple'
};

export const LOGIN_TYPE = {
  FIRST_LOGIN: 'FIRST_LOGIN',
  RE_LOGIN: 'RE_LOGIN'
};

export const ID = {
  VIEW_MORE: 'VIEW_MORE',

  // PROFILE
  ACCOUNT_BILLING: 'tai-khoan-cai-dat',
  FAVORITE: 'yeu-thich',
  CONTENT_RENT: 'noi-dung-dang-thue',
  DEVICE_MANAGEMENT: 'quan-ly-thiet-bi',
  KIDS_MANAGEMENT: 'quan-ly-tre-em',
  RESTRICTION_CONTENT: 'gioi-han-noi-dung',
  LOYALTY_POINT: 'khan-gia-than-thiet',
  UPDATE_NAME: 'UPDATE_NAME',
  UPDATE_EMAIL: 'UPDATE_EMAIL',
  UPDATE_PHONE: 'UPDATE_PHONE',
  CONFIRM_PHONE: 'CONFIRM_PHONE',
  CONFIRM_PHONE_PASSWORD: 'CONFIRM_PHONE_PASSWORD',
  UPDATE_GENDER: 'UPDATE_GENDER',
  UPDATE_DATE_OF_BIRTH: 'UPDATE_DATE_OF_BIRTH',
  UPDATE_PASSWORD: 'UPDATE_PASSWORD',
  ALLOW_PUSH: 'ALLOW_PUSH',
  CLEAR_HISTORY: 'CLEAR_HISTORY',
  CLEAR_SEARCH: 'CLEAR_SEARCH',
  UPDATE_COMPANY_NAME: 'UPDATE_COMPANY_NAME',
  UPDATE_COMPANY_TIN: 'UPDATE_COMPANY_TIN',
  UPDATE_COMPANY_ADDRESS: 'UPDATE_COMPANY_ADDRESS',
  UPDATE_INVOICE_EMAIL: 'UPDATE_INVOICE_EMAIL',

  // PAYMENT PROFILE
  PAYMENT_INFO: 'dich-vu-da-mua',
  WATCHING: 'dang-xem',

  // KID ACTIVITY
  SUMMARY: 'thoi-luong-da-xem',
  DETAIL: 'noi-dung-da-xem',

  // LOYTALTY
  MEMBER_POINT: 'viecoin-cua-toi',
  ACTIVITY_HISTORY: 'lich-su-hoat-dong',
  PLUS_POINT_ACTIVYTY: 'viecoin-tich-luy',
  USED_POINT_ACTIVYTY: 'viecoin-da-su-dung',

  // COMING SOON
  BROADCASTING: 'dang-phat-song',
  UP_BROADCASTING: 'sap-phat-song'
};

export const BANNER_VIP_TYPE = {
  NON_VIP: 0,
  VIP: 1,
  VIP_K_PLUS: 2
};

export const DIRECTION = {
  HORIZONTAL: 'HORIZONTAL',
  VERTICAL: 'VERTICAL'
};

export const LAYOUT_MENU = {
  MORE: 'view_more',
  PAGES: 'pages'
};

export const TAG_TYPE = {
  CATEGORY: 'category',
  COUNTRY: 'country',
  GENRE: 'genre'
};

export const ICON_KEY = {
  HOME: 'home',
  K_PLUS: 'kplus',
  NALA: 'nala',
  TELEVISION: 'television',
  RUNNING_MAN: 'runningman',
  HBO: 'hbo',
  RAPVIET: 'rapviet',
  LIVESTREAM: 'livestream',
  VIDEO: 'video',
  VIP: 'vip',
  MORE: 'more',
  SCHEDULE: 'schedule',
  VIEZ: 'viez',
  VTV: 'vtv',
  VIECOMIC: 'viecomic',
  GAME: 'game',
  SPORT: 'sport',
  KID: 'kids',
  MUSIC: 'music',
  PODCAST: 'podcast',
  NEWS: 'news',
  READING: 'reading',
  TENNIS: 'tennis',
  VTVCAB: 'vtvcab',
  DESKTOP: 'desktop',
  OTHER: 'other',
  APP: 'app',
  SMART_TV: 'smarttv',
  TABLET: 'tablet',
  EMPTY_BOX: 'empty_box',
  TICK_CIRCLE: 'tick_circle',
  COPY: 'copy',
  PLUS: 'plus',
  EDIT: 'edit',
  BACK: 'back',
  KID_SOLID: 'kid_solid',
  CLOCK: 'clock',
  CHECK_ALL: 'check_all',
  CHECKED_ALL: 'checked_all',
  LOG_OUT: 'log_out',
  BROWSER: 'browser',
  MOBILE: 'mobile',
  PHONE: 'phone',
  CART: 'cart',
  REDIRECT: 'redirect',
  CART_INDICATOR: 'cart_indicator',
  SHOPPING_CART: 'shopping_cart',
  FAQ: 'faq',
  LIST: 'list',
  CHECK: 'check',
  LOCK: 'lock',
  PLAYBACK_SPEED: 'playback_speed',
  EMAIL: 'email',
  WARNING: 'warning',
  VISA: 'visa',
  INFO: 'info',
  EXPAND: 'expand',
  COLLAPSE: 'collapse',
  PHONE_PAYMENT: 'phone_payment',
  PASSWORD_PAYMENT: 'password_payment',
  REGISTER_PAYMENT: 'register_payment',
  CHAT_SOLID: 'chat_solid',
  NCT: 'nct',
  VOUCHER_PAYMENT: 'voucher_payment',
  CLOCK_COUNTDOWN: 'clock_countdown',
  PENTAGRAM: 'pentagram',
  HEADPHONES: 'headphones',
  EXSH: 'exsh'
};

export const ICON_KEY_DEVICE_DATA = {
  [ICON_KEY.DESKTOP]: ICON_KEY.DESKTOP,
  [ICON_KEY.APP]: ICON_KEY.APP,
  [ICON_KEY.SMART_TV]: ICON_KEY.SMART_TV,
  [ICON_KEY.TABLET]: ICON_KEY.TABLET,
  [ICON_KEY.EMPTY_BOX]: ICON_KEY.EMPTY_BOX
};

export const SETTING_REACT_SLICK = {
  dots: false,
  arrows: true,
  prevArrow: false,
  nextArrow: false,
  infinite: false,
  slidesToShow: 6,
  slidesToScroll: 6,
  focusOnSelect: false,
  centerPadding: '100px',
  // lazyLoad: true,
  // initialSlide:0,
  responsive: [
    {
      breakpoint: 1440,
      settings: {
        slidesToShow: 5,
        slidesToScroll: 5
      }
    },
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 4
      }
    },
    {
      breakpoint: 600,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 2
      }
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1
      }
    }
  ]
};

export const CONFIG_KEY = {
  MSG_CONFIG: 'message_config',
  PERSONAL_GENRE: 'personal_genre',
  PAGE_SHOP_SCHEMA: 'page_shop_schema',
  POPUP_CONFIG: 'web_dialog',
  PAGE_PHIM_HAY: 'page_phim_hay',
  PAGE_PHIM_LE: 'page_phim_le',
  PAYMENT_CONFIG: 'web_payment_config',
  PAYMENT_CONFIG_GLOBAL: 'web_payment_config_global',
  FLOATING_CONFIG_DEEPLINK_GUILD: 'floating_guide_button_deeplink_web',
  WEB_FAQS: 'web_faqs',
  WEB_PRIVACY: 'web_privacy',
  WEB_PRIVACY_120424: 'web_privacy_120424',
  WEB_USAGE: 'web_usage',
  WEB_ABOUT_US: 'web_about_us',
  WEB_USAGE_V1: 'web_usage_ver1',
  WEB_USAGE_V2: 'web_usage_ver2',
  WEB_USAGE_V3: 'web_usage_ver3',
  WEB_USAGE_V4: 'web_usage_ver4',
  WEB_ANNOUNCE: 'web_announce',
  WEB_AGREEMENT: 'web_agreement',
  WEB_AGREEMENT_120424: 'web_agreement_120424',
  INTRO_SERVICE: 'web_intro_service',
  PAYMENT_POLICY: 'web_payment_policy',
  WEB_LICENSE: 'web_license',
  WEB_REGULATION: 'web_regulation',
  WEB_POLICY_CANCELLATION: 'web_policy_cancellation',
  WEB_CONFIG: 'web_config',
  APP_FAQS: 'app_faqs',
  AGE_RANGES: 'age_ranges',
  WEB_CONTENT_CONFIG: 'web_content_config',
  WEB_TRIGGER_CONFIG: 'web_trigger_config',
  PLAYER_CONFIG: 'web_player_config'
};

export const FOOTER: any = {
  NAV: {
    RULE: 'Quy định',
    SUPPORT: 'Trợ giúp',
    INFO: 'Thông tin',
    INTRO: 'Giới Thiệu',
    INTRODUCE: {
      PATH: 'https://gioithieu.vieon.vn',
      KEY: CONFIG_KEY.WEB_ABOUT_US,
      NAME: 'Giới Thiệu Về VieON',
      TITLE: 'About Us - Giới Thiệu Về VieON | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    TERMS: {
      PATH: '/hop-dong-dien-tu/',
      KEY: CONFIG_KEY.WEB_USAGE,
      NAME: 'Hợp đồng điện tử',
      TITLE: 'About Us - Hợp đồng điện tử | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.',
      DEEPLINK: 'vieonapp://vieon.vn/aboutus/hop-dong-dien-tu'
    },
    USAGE_V1: {
      PATH: '/hop-dong-012022/',
      KEY: CONFIG_KEY.WEB_USAGE_V1,
      NAME: 'Hợp đồng điện tử 012022',
      TITLE: 'About Us - Hợp đồng điện tử 012022 | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    USAGE_V2: {
      PATH: '/hop-dong-092022/',
      KEY: CONFIG_KEY.WEB_USAGE_V2,
      NAME: 'Hợp đồng điện tử 092022',
      TITLE: 'About Us - Hợp đồng điện tử 092022 | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    USAGE_V4: {
      PATH: '/hop-dong-160625/',
      KEY: CONFIG_KEY.WEB_USAGE_V4,
      NAME: 'Hợp đồng điện tử 160625',
      TITLE: 'About Us - Hợp đồng điện tử 160625 | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    AGREEMENT: {
      PATH: '/thoa-thuan-va-chinh-sach/',
      KEY: CONFIG_KEY.WEB_AGREEMENT,
      NAME: 'Điều Khoản Và Điều Kiện',
      TITLE: 'About Us - Thỏa Thuận Và Chính Sách | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    AGREEMENT_120424: {
      PATH: '/thoa-thuan-va-chinh-sach-120424/',
      KEY: CONFIG_KEY.WEB_AGREEMENT_120424,
      NAME: 'Điều Khoản Và Điều Kiện',
      TITLE: 'About Us - Thỏa Thuận Và Chính Sách | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    INTRO_SERVICE: {
      PATH: '/goi-dich-vu/',
      KEY: CONFIG_KEY.INTRO_SERVICE,
      NAME: 'Gói Và Phương Thức Cung Cấp Dịch Vụ',
      TITLE: 'About Us - Gói Và Phương Thức Cung Cấp Dịch Vụ | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    PAYMENT_POLICY: {
      PATH: '/chinh-sach-thanh-toan/',
      KEY: CONFIG_KEY.PAYMENT_POLICY,
      NAME: 'Chính Sách Thanh Toán',
      TITLE: 'About Us - Chính Sách Thanh Toán | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    PRIVATE_POLICY: {
      PATH: '/chinh-sach-quyen-rieng-tu/',
      KEY: CONFIG_KEY.WEB_PRIVACY,
      NAME: 'Chính Sách Bảo Vệ Thông Tin Cá Nhân',
      TITLE: 'About Us - Chính Sách Bảo Vệ Thông Tin Cá Nhân | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    PRIVATE_POLICY_120424: {
      PATH: '/chinh-sach-quyen-rieng-tu-120424/',
      KEY: CONFIG_KEY.WEB_PRIVACY_120424,
      NAME: 'Chính Sách Bảo Vệ Thông Tin Cá Nhân',
      TITLE: 'About Us - Chính Sách Bảo Vệ Thông Tin Cá Nhân | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    COPY_RIGHT: {
      PATH: '/ban-quyen/',
      KEY: CONFIG_KEY.WEB_LICENSE,
      NAME: 'Chính Sách Về Sở Hữu Trí Tuệ',
      TITLE: 'About Us - Bản Quyền | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    REGULATION: {
      PATH: '/quy-dinh/',
      KEY: CONFIG_KEY.WEB_REGULATION,
      NAME: 'Hợp Đồng và Chính Sách',
      TITLE: 'About Us - Hợp Đồng và Chính Sách | VieON',
      DESC: 'Hợp Đồng và Chính Sách'
    },
    POLICY_CANCELLATION: {
      PATH: '/chinh-sach-huy-gia-han/',
      KEY: CONFIG_KEY.WEB_POLICY_CANCELLATION,
      NAME: 'Chính Sách Hủy Gia Hạn',
      TITLE: 'About Us - Chính Sách Hủy Gia Hạn | VieON',
      DESC: 'Chính Sách Hủy Gia Hạn'
    },
    ANNOUNCEMENT: {
      PATH: '/thong-bao/',
      KEY: CONFIG_KEY.WEB_ANNOUNCE,
      NAME: 'Thông Báo',
      TITLE: 'About Us - Thông Báo | VieON',
      DESC: 'Thông Báo'
    },
    FAQS: {
      PATH: 'https://help.vieon.vn',
      KEY: CONFIG_KEY.WEB_FAQS,
      NAME: 'FAQs',
      TITLE: 'About Us - Câu Hỏi Thường Gặp | VieON',
      DESC: 'Hợp đồng điện tử, Điều Khoản Và Điều Kiện, Chính Sách Quyền Riêng Tư, Chính Sách Về Quyền Sở Hữu Trí Tuệ, Các Câu Hỏi Thường Gặp Và Liên Hệ Với Chúng Tôi.'
    },
    SUPPORT_CENTER: {
      URL: 'https://marketing.vieon.vn/help-center',
      NAME: 'Trung tâm hỗ trợ'
    },
    CONTACT: 'Liên hệ',
    FEEDBACK: 'Góp ý'
  },
  COMPANY_INFO: {
    ADDRESS:
      'Công ty Cổ phần VieON - Địa chỉ: Tầng 5, 222 Pasteur, Phường Võ Thị Sáu, Quận 3, Thành phố Hồ Chí Minh.',
    EMAIL: '<EMAIL>',
    BUSINESS_LICENSE:
      'Giấy phép Cung cấp Dịch vụ Phát thanh, Truyền hình trả tiền số 247/GP-BTTTT cấp ngày 21/07/2023.',
    BUSINESS_LICENSE_2:
      'Giấy Chứng Nhận Đăng Ký Doanh Nghiệp số: ********** do Sở Kế Hoạch Đầu Tư Thành Phố Hồ Chí Minh cấp ngày 19/05/2017',
    HOTLINE: '1800.599.920'
  },
  MIT: {
    TITLE: 'Bộ công thương',
    LINK: 'http://online.gov.vn/Home/WebDetails/108074',
    IMAGE: ConfigImage.logoMit
  },
  APP_INFO: {}
};

export const CASE_SHOW_LOBBY = {
  ONLY_LOGIN: 1,
  LOGIN_AND_REGISTER: 2
};

export const LIMIT_ITEMS_RIBBON_CALL_API = 12;
export const LIMIT_ITEMS_RIBBON_FOR_SHOW = 75;
export const LIMIT_ITEMS_FIRST_RIBBON_FOR_SHOW = 42;
export const LINK_VIEON_DOWNLOAD_APP = `${DOMAIN_WEB}/download-app`;
export const LINK_QRCODE_DOWNLOAD_APP =
  'https://static.vieon.vn/vieon-images/rapviet/qrcode_download-app_web.png';

export const AFF_NETWORK = {
  ACCESS_TRADE: 'accesstrade'
};

export const PREMIUM_TYPE = {
  AVOD: 0,
  SVOD: 1,
  TVOD: 5,
  PVOD_6: 6,
  PVOD_7: 7,
  SVOD_TVOD: 8
};

export const TVOD_TYPE = {
  RENTAL: 2,
  PRE_ORDER_LIVE_EVENT: 3,
  PRE_ORDER_SIMULCAST: 4
};

export const TAB_LISTS = [
  {
    id: 'DSK',
    title: 'Danh sách kênh',
    name: 'danh_sach_kenh',
    idTracking: 'danh_sach_kenh_id',
    order: 0,
    iconClass: 'vie-list-ul-rc',
    btnClass: 'player__button player__button--channel-list shrink !ml-auto'
  },
  {
    id: 'DPS',
    title: 'Đang phát sóng',
    name: 'dang_phat_song',
    idTracking: 'dang_phat_song_id',
    order: 1,
    iconClass: 'vie-stream-rc-medium',
    btnClass: 'player__button player__button--broadcast shrink'
  },
  {
    id: 'LPS',
    title: 'Lịch phát sóng',
    name: 'lich_phat_song',
    idTracking: 'lich_phat_song_id',
    order: 2,
    iconClass: 'vie-calendar-o',
    btnClass: 'player__button player__button--broadcast-schedule shrink'
  }
];

export const URL_SUPPORT_SMART_TV = `${DOMAIN_WEB}/smart-tv`;

export const USER_TYPE = {
  NON_VIP: 0,
  VIP: 1,
  VIP_K_PLUS: 2,
  VIP_HBO: 3,
  ALL_ACCESS: 4
};

export const USER_TYPE_ENUM = {
  GUEST: 'Guest',
  NON_VIP: 'Non VIP',
  VIP: 'VIP',
  HBO: 'HBO',
  K_PLUS: 'K+',
  ALL_ACCESS: 'All Access'
};

export const DISPLAY_TYPE_ROUND = {
  LIST_ARTIST: 1,
  RATING: 2,
  FINAL_RESULT: 3
};

export const POPUP = {
  LINE: {
    GREEN: 'green',
    YELLOW: 'yellow'
  },
  NAME: {
    CANCEL_CHANGE: 'cancel-change',
    WARNING_KID_CHANGE_PROFILE_NON_KID: 'warning-kid-change-profile-non-kid_dialog',
    KID_LIMITED: 'kid-limited_dialog',
    PROFILE_HAS_DELETED: 'profile-has-deleted_dialog',
    PIN_CODE: 'PIN_CODE',
    PIN_CODE_OVER_INPUT: 'PIN_CODE_OVER_INPUT',
    DELETE_PROFILE: 'delete-profile_dialog',
    LOGIN: 'LOGIN',
    REQUEST_LOGIN: 'request-login_dialog',
    USER_REPORT: 'USER_REPORT',
    USER_RESTORE: 'user_restore',
    REQUEST_LOGIN_NUMBER_REGISTER: 'request-login-by-number-exist-register_dialog',
    REQUEST_LOGIN_NOT_NUMBER_REGISTER: 'request-register-by-number-not-exist_dialog',
    REGISTER: 'REGISTER',
    FORGET_PASSWORD: 'FORGET_PASSWORD',
    REQUEST_FAVORITE_LIST: 'REQUEST_FAVORITE_LIST',
    SORT_FAVORITE_LIST: 'SORT_FAVORITE_LIST',
    CONFIRM_NEW_PASSWORD: 'CONFIRM_NEW_PASSWORD',
    CONFIRM_OTP_FORGET_PASSWORD: 'CONFIRM_OTP_FORGET_PASSWORD',
    REQUIRE_UPDATE_PHONE: 'REQUIRE_UPDATE_PHONE',
    REQUIRE_UPDATE_PHONE_OTP: 'REQUIRE_UPDATE_PHONE_OTP',
    REQUIRE_UPDATE_PHONE_PASSWORD: 'REQUIRE_UPDATE_PHONE_PASSWORD',
    CONFIRM_OTP_REGISTER: 'CONFIRM_OTP_REGISTER',
    QR_ZALO_PAY: 'QR_ZALO_PAY',
    QR_SHOPEE_PAY: 'QR_SHOPEE_PAY',
    QR_MOCA: 'QR_MOCA',
    WELCOME_PAYMENT_SUCCESS: 'WELCOME_PAYMENT_SUCCESS',
    POPUP_RATING: 'POPUP_RATING',
    END_LIVESTREAM: 'end-live-stream_dialog',
    PRE_PAY: 'PRE_PAY',
    SUGGEST_CANCEL_PACKAGE: 'SUGGEST_CANCEL_PACKAGE',
    PREVENT_BUY_PACKAGE: 'PREVENT_BUY_PACKAGE',

    FIRST_LOGIN: 'first-login_dialog',
    NON_LOGIN_BUY_VOD_VIP_PACKAGE: 'anonymous-user_vod-vip-last-step_dialog',
    NON_LOGIN_INPUT_VOUCHER: 'anonymous-user_sub-audio-vip-last-step_dialog',
    NON_LOGIN_INPUT_PROMOTION: 'anonymous-user_quality-vip-last-step_dialog',
    NON_LOGIN_END_SVOD_TRIAL: 'anonymous-user_end-svod-trial_dialog',
    NON_LOGIN_QUALITY: 'anonymous-user_quality-vip_dialog',
    NON_LOGIN_SUB_AUDIO: 'anonymous-user_sub-audio-vip_dialog',
    LOGIN_NON_VIP_END_SVOD_TRIAL: 'non-vip-user_end-svod-trial_dialog',
    LOGIN_NON_VIP_QUALITY: 'non-vip-user_quality-vip_dialog',
    LOGIN_NON_VIP_SUB_AUDIO: 'non-vip-user_sub-audio-vip_dialog',

    NON_LOGIN_REMIND_VOD: 'anonymous-user_remind-vod_dialog',
    NON_LOGIN_ADD_LIST_VOD: 'anonymous-user_add-list-vod_dialog',
    NON_LOGIN_RATING: 'anonymous-user_rating_dialog',
    NON_LOGIN_COMMENT: 'anonymous-user_comment_dialog',
    NON_LOGIN_USER_REPORT: 'anonymous-user_report_dialog',
    NON_LOGIN_ADD_LIST_LIVE_TV: 'anonymous-user_add-list-livetv_dialog',
    NON_LOGIN_REMIND_EPG: 'anonymous-user_remind-livetv-epg_dialog',
    NON_LOGIN_REMIND_LIVESTREAM: 'anonymous-user_remind-livestream_dialog',

    NON_LOGIN_COMMENT_GLOBAL: 'anonymous-user_comment_dialog_global',
    NON_LOGIN_ADD_LIST_VOD_GLOBAL: 'anonymous-user_add-list-vod_dialog_global',
    NON_LOGIN_REMIND_VOD_GLOBAL: 'anonymous-user_remind-vod_dialog_global',
    NON_LOGIN_RATING_GLOBAL: 'anonymous-user_rating_dialog_global',
    NON_LOGIN_USER_REPORT_GLOBAL: 'anonymous-user_report_dialog_global',
    NON_LOGIN_ADD_LIST_LIVE_TV_GLOBAL: 'anonymous-user_add-list-livetv_dialog_global',
    NON_LOGIN_REMIND_EPG_GLOBAL: 'anonymous-user_remind-livetv-epg_dialog_global',
    NON_LOGIN_REMIND_LIVESTREAM_GLOBAL: 'anonymous-user_remind-livestream_dialog_global',
    NON_LOGIN_INPUT_VOUCHER_GLOBAL: 'anonymous-user_sub-audio-vip-last-step_dialog_global',
    NON_LOGIN_INPUT_PROMOTION_GLOBAL: 'anonymous-user_quality-vip-last-step_dialog_global',
    REQUEST_REGISTER_CONVERSION: 'request-user_register_conversion_dialog',
    CONVERT_MOBILE_WEB_TO_APP: 'convert_mobile_web_to_app',
    MOBILE_WEB_ONLY_APP: 'mobile_web_only_app',
    ENGAGEMENT_DIALOG: 'engagement_dialog',
    SURVEY_PAYMENT: 'survey_payment',

    // GLOBAL
    NON_LOGIN_TRIAL_GLOBAL: 'anonymous_global_trial_dialog',
    USER_VOD_TRIAL_GLOBAL: 'user_global_trial_dialog',
    LIMIT_REGION: 'limit_region_dialog',

    BLOCK_ACCOUNT: 'block-acount_dialog',
    BLOCK_ACCOUNT_DELETE: 'block-acount_delete_dialog',
    BLOCK_BUY_PACKAGE_FOREIGN_USERS: 'block-buy-package-foreign-users',
    LIMIT_CCU: 'limit-ccu_dialog',
    LIMIT_EPG: 'limit-epg_dialog',
    PLAYER_ERROR: 'player-error_dialog',
    RATING: 'rating',
    LIVESTREAM_END: 'livestreamEnded',
    LOGOUT: 'log-out_dialog',
    PLAYER_ERROR_NETWORK: 'player-error-network_dialog',
    PLAYER_ERROR_VOD: 'cat-error-vod_dialog',
    PLAYER_ERROR_LIVETV: 'cat-error-livetv_dialog',
    PLAYER_ERROR_LIVESTREAM: 'cat-error-livestream_dialog',
    PLAYER_ERROR_PREMIERE: 'cat-error-premiere_dialog',
    PLAYER_TRIGGER_AUTH: 'player_trigger_auth_dialog',
    VALIDATE_K_PLUS: 'validate-kplus_dialog',
    KICKED_DEVICE: 'kicked-device_dialog',

    // Profile
    CONFIRM_OTP_RESTORE_ACCOUNT: 'CONFIRM_OTP_RESTORE_ACCOUNT',

    // TVOD
    TVOD_ON_BOARDING: 'TVOD_ON_BOARDING',
    TVOD_RENTED: 'TVOD_RENTED',
    TVOD_LIVE_CAN_RENTAL: 'TVOD_LIVE_CAN_RENTAL',
    TVOD_LIVE_EXPIRED: 'TVOD_LIVE_EXPIRED',
    TVOD_PRE_ORDERED: 'TVOD_PRE_ORDERED',
    TVOD_PRE_ORDER_EXPIRED: 'TVOD_PRE_ORDER_EXPIRED',
    TVOD_PRE_ORDER_PROMOTIONAL_PRICE_EXPIRED: 'TVOD_PRE_ORDER_PROMOTIONAL_PRICE_EXPIRED',
    TVOD_EXPIRED: 'tvod_expired_dialog',

    DOWNLOAD_APP: 'DOWNLOAD_APP',
    REFERRAL_CODE: 'REFERRAL_CODE',

    // Multi Profile
    RECREATE_PIN_CODE: 'RECREATE_PIN_CODE',
    CONFIRM_OVER_18: 'confirm_over_18',
    KID_LIMITED_CONTENT_DIALOG: 'kid-limited-content_dialog',
    KID_LIMITED_PAGE_DIALOG: 'kid-limited-page_dialog',
    KID_LIMITED_VIP_DIALOG: 'kid-limited-vip_dialog',
    KID_ACCESS_SVOD: 'kid-svod_dialog',
    KID_ACCESS_TVOD: 'kid-tvod_dialog',
    KID_ACCESS_PVOD: 'kid-pvod_dialog',
    LOBBY_GUIDE: 'LOBBY_GUIDE',
    CONFIRM_WHEN_RETURN: 'confirm_when_return',

    // Loyalty
    REDEEM_VOUCHER: 'redeem_voucher',
    DETAIL_LOYALTY: 'detail_loyalty',

    // Title Restriction
    TITLE_RESCTRICTION: 'title_resctriction',
    TITLE_RESCTRICTION_CLOSE_CHANGED_NOTI: 'title_resctriction_close_changed_noti',
    CONTENT_RESCTRICTED: 'content_restricted',

    ALREADY_GLOBAL_VIEON: 'already_global_vieon',
    LOCAL_TO_GLOBAL: 'local_to_global',
    GLOBAL_TO_LOCAL: 'global_to_local',

    // Linked Social
    LINKED_SOCIAL_REQUEST_UPDATE_INFO: 'linked_social_request_update_info',
    LINKED_SOCIAL_REQUEST_UPDATE_PHONE: 'linked_social_request_update_phone',
    LINK_SOCIAL_FAILED: 'link_social_failed',

    // Concurrent Screen
    CONCURRENT_SCREEN: 'concurrent_screen',

    // Fast Track
    PVOD_OWNED: 'pvod_dialog_owned',
    PVOD_EXPIRED: 'pvod_dialog_expired',
    PVOD_REGISTER: 'pvod_register_dialog',
    PVOD_REGISTER_VIP: 'pvod_register_vip_dialog',

    // Policy Announce
    POLICY_ANNOUNCE_CONFIRM: 'policy_announce_confirm_dialog',
    POLICY_ANNOUNCE_SYNC: 'policy_announce_sync_dialog',

    TRIGGER_FIRSTPAY: 'trigger_firstpay_dialog',

    // TVOD and SVOD popup
    TVOD_VIP_UPSELL: 'TVOD_VIP_UPSELL',
    SVOD_TRIGGER: 'SVOD_TRIGGER'
  }
};

export const PERMISSION = {
  NON_LOGIN: 0,
  CAN_WATCH: 206, // can watch permission
  GIFT_CODE: 207,
  PAYMENT: 208, // chưa mua gói
  DONT_ALLOW_BROADCAST: 405,
  LIMITED_DEVICE: 429, // giới hạn tb
  FORCE_LOGIN: 1,
  KID_LIMITED: 145,
  CONTENT_RESCTRICTED: 146
};

export const POSITION_TRIGGER = {
  SEARCH: 'search',
  NOTIFY: 'notification',
  VIDEO_INTRO: 'video intro',
  PROFILE: 'profile',
  LIVE_TV: 'livetv',
  COMING_SOON: 'coming soon'
};
export const CONTENT_TYPE: any = {
  LIVESTREAM: 0,
  SEASON: 3,
  EPISODE: 4,
  LIVE_TV: 5,
  MOVIE: 1,
  EPG: 7,
  TRAILER: 6,
  RIBBON: 10,
  MASK_ID: 'mask-id',
  ADS: 'banner-ads',
  BANNER_TRIGGER: 'banner-trigger',
  SHORT_CONTENT: 4
};

export const SEASON_CATEGORY = {
  SHOW: 2
};

export const TRANSACTION_CODE = {
  PROCESSING: 0,
  SUCCESS: 1,
  NOT_SUCCESS: 2,
  FAIL: 3,
  TRANSACTION_CANCEL: 4,
  CODE_5: 5, // giao dich hoan tien
  CODE_6: 6, // So tien thanh toan khong dung
  CODE_7: 7, // Bi huy boi nguoi van hanh
  CODE_99: 99, // nguoi van hanh huy
  CODE_100: 100 // het han thanh toan
};

export const PROMOTION_BANNER_FUNC_TYPE = {
  buyPackage: 'buy-package',
  buyVip: 'buy-vip',
  buyVipKPLUS: 'buy-vip-K+',
  buyVipHBO: 'buy-vip-HBO',
  buyVipSPORT: 'buy-vip-SPORT',
  buyVipFAMILY: 'buy-vip-FAMILY',
  buyAllAccess: 'buy-all-access',
  Login: 'login',
  Register: 'register'
};

export const RIBBON_TYPE = {
  MASTER_BANNER: 0,
  POSTER: 1,
  BANNER: 3,
  LIVE_TV: 4,
  LIVESTREAM: 5,
  EPG: 6,
  COLLECTION: 7,
  COMING_SOON: 8,
  PROMOTION_BANNER: 9,
  ORIGINAL: 10,
  TOP_VIEWS: 11,
  PROMOTION_RIBBON: 12,
  PROMOTION_BANNER_FUNC: 14,
  WATCH_MORE: 100,
  FAVORITE_VOD: 101,
  FAVORITE_LIVE_TV: 102,
  WATCHED_LIST: 103,
  RAP_VIET_SS2: 104,
  RANKING_BOARD: 106,
  BANNER_RIBBON_ADS: 109,
  PROMOTED_RIBBON_ADS: 110,
  TVOD_RENTING: 111,
  CONVERT_MOBILE_WEB_TO_APP: 112,
  RAP_RANKING_BOARD: 116,
  MULTI_TABS_RIBBON: 117,
  PROMOTE_VOTING: 118,
  EXSH_BOARD: 120
};

export const PAGE: any = {
  USER_UPDATE_PHONE_NUMBER: '/ca-nhan/cap-nhat-so-dien-thoai',
  USER_UPDATE_PASSWORD: '/ca-nhan/doi-mat-khau',
  AUTH: '/auth',
  VIP: 'vip',
  LINK: '/link',
  HOME: '/',
  HOME_DEFAULT: '/trang-chu/',
  PHIMMOI: '/phim-moi/',
  VOD: '/[slug].html',
  CATEGORY: '/[pageSlug]',
  PAGE_MENU: '/[pageSlug]/m/[slug]',
  COLLECTION: '/[pageSlug]/r/[slug]',
  COLLECTION_RIB: '/[pageSlug]/r/[slug]',
  LIVE_STREAM: '/truyen-hinh-truc-tuyen/livestream',
  LIVE_STREAM_SLUG: '/truyen-hinh-truc-tuyen/livestream/[slug]',
  LIVE_TVOD_SLUG: '/[slug]--live-[liveParam].html',
  TAG: '/[pageSlug]/t/[slug]',
  SEARCH: '/tim-kiem',
  PHIM_HAY: '/phim-hay',
  PHIM_LE: '/phim-le',
  SPORT: '/the-thao',
  ARTIST: '/nghe-si/[slug]',
  PROFILE: '/ca-nhan',
  PROFILE_SLUG: '/ca-nhan/[slug]',
  LIST_WINNERS: '/danh-sach-trung-thuong',
  PROFILE_PURCHASED: '/ca-nhan/dich-vu-da-mua',
  PROFILE_FAVORITE: '/ca-nhan/yeu-thich',
  DEVICE_MANAGEMENT: '/ca-nhan/quan-ly-thiet-bi',
  CONTENT_RENT: '/ca-nhan/noi-dung-dang-thue',
  PAGE_SUPPORT_SMART_TV: '/smart-tv',
  PAYMENT: '/mua-goi',
  PAYMENT_METHOD: '/mua-goi/phuong-thuc',
  PAYMENT_RESULT: '/mua-goi/ket-qua',
  WALLET_LINK: '/lien-ket-vi',
  RENTAL_CONTENT: '/mua-goi/tvod',
  PVOD_CONTENT: '/mua-goi/pvod',
  SMART_TV_RENTAL_CONTENT: '/smart-tv/thue-noi-dung',
  CODE_GUIDE: '/huong-dan-nhap-ma',
  // IN APP
  INAPP: '/in-app/',
  ZALOPAY: '/in-app/zalopay',
  ZALOPAY_METHOD: '/in-app/zalopay/phuong-thuc',
  ZALOPAY_RESULT: '/in-app/zalopay/ket-qua',
  ZALOPAY_USAGE: '/in-app/zalopay/hop-dong-dien-tu',
  ZALOPAY_REGULATION: '/in-app/zalopay/quy-dinh',
  ZALOPAY_COPY_RIGHT: '/in-app/zalopay/ban-quyen',
  ZALOPAY_POLICY_CANCELLATION: '/in-app/zalopay/chinh-sach-huy-gia-han',
  ZALOPAY_POLICY_PRIVACY: '/in-app/zalopay/chinh-sach-quyen-rieng-tu',
  COPY_RIGHT: '/ban-quyen',
  POLICY_PRIVACY: '/chinh-sach-quyen-rieng-tu',
  PAYMENT_TPBANK: '/tpbank',
  PAYMENT_TPBANK_SLUG: '/tpbank/[slug]',
  VOUCHER: '/ma-vieon',
  SMART_TV_RESULT_PAYMENT: '/smart-tv/[orderId]/ket-qua',
  TRANSACTION: '/lich-su-giao-dich',
  TRANSACTION_HISTORY: '/ca-nhan/lich-su-giao-dich',
  WATCHLATER: '/ca-nhan/xem-sau',
  WATCHMORE: '/ca-nhan/dang-xem',
  SERVICE_PACK: '/goi-dich-vu',
  LIVE_TV: '/truyen-hinh-truc-tuyen',
  LIVE_TV_SLUG: '/truyen-hinh-truc-tuyen/[slug]',
  LIVE_TV_EPG: '/truyen-hinh-truc-tuyen/[slug]/[epg]',
  EMAIL_VERIFIED: '/email-verified',
  MAINTENANCE: '/bao-tri',
  INTRODUCE: '/gioi-thieu-vieon',
  USAGE: '/hop-dong-dien-tu',
  REGULATION: '/quy-dinh',
  POLICY_CANCELLATION: '/chinh-sach-huy-gia-han',
  ANNOUNCEMENT: '/thong-bao',
  FAQS: '/cau-hoi-thuong-gap',
  AGREEMENT: '/thoa-thuan-va-chinh-sach',
  INTRO_SERVICE: '/goi-dich-vu',
  PAYMENT_POLICY: '/chinh-sach-thanh-toan',
  PRIVATE_POLICY: '/chinh-sach-quyen-rieng-tu',
  USAGE_V1: '/hop-dong-012022',
  USAGE_V2: '/hop-dong-092022',
  ZALOPAY_AGREEMENT: '/in-app/zalopay/thoa-thuan-va-chinh-sach',

  // lobby
  LOBBY_PROFILES: '/ca-nhan/nguoi-dung',

  // title restriction
  TITLE_RESCTRICTION: '/ca-nhan/gioi-han-noi-dung'
};

export const SLUG = {
  LIVE_TV: 'truyen-hinh-truc-tuyen',
  LIVE_STREAM: 'truyen-hinh-truc-tuyen/livestream',
  SPORT: 'the-thao',
  SHOP: 'shop',
  RAP_VIET: '/rap-viet/'
};

export const TRIGGER_KEY = {
  TVOD_NOW: 'TVOD_NOW',
  PRE_ORDER: 'PRE_ORDER',
  TVOD_FULL_EPS: 'TVOD_FULL_EPS',
  WATCH_NOW: 'WATCH_NOW',
  // Global
  WATCH_TRIAL: 'WATCH_TRIAL',
  BUY_FULL_SEASON: 'BUY_FULL_SEASON',
  BUY_GLOBAL: 'BUY_GLOBAL',

  REMIND_ME: 'REMIND_ME',
  MY_LIST: 'MY_LIST',
  DETAIL: 'DETAIL',
  WATCH_NOW_APP_VIEON: 'WATCH_NOW_APP_VIEON',
  WATCH_TRAILER: 'WATCH_TRAILER',
  SHARE: 'SHARE'
};

export const TRIGGER_ICON: any = {
  TVOD_NOW: 'vie-play-solid-rc',
  TVOD_FULL_EPS: 'vie-play-solid-rc',
  WATCH_TRAILER: 'vie-play-solid-rc',
  SHARE: 'vie-location-share-o',
  WATCH_NOW: 'vie-play-solid-rc',
  // Global
  WATCH_TRIAL: 'vie-play-solid-rc',
  BUY_FULL_SEASON: 'vie-play-solid-rc',
  BUY_GLOBAL: 'vie-play-solid-rc',

  REMIND_ME: 'vie-bell-o-rc-light',
  MY_LIST: 'vie-math-plus',
  MY_LIST_ADDED: 'vie-tick',
  DETAIL: 'vie-info-o-c-script',
  PRE_ORDER: 'vie-calendar-o-rc-tick',
  DOWNLOAD_APP: 'vie-mobile-download'
};

export const TAG_KEY = {
  VIP: 'VIP',
  WATCH_SOON: 'WATCH_SOON',
  LIVE: 'LIVE',
  PREMIERE: 'PREMIERE',
  TOP_VIEW: 'TOP_VIEW',
  INTRO: 'INTRO',
  TOTAL_CCU: 'TOTAL_CCU',
  PRICE: 'PRICE',
  REMIND: 'REMIND',
  PVOD: 'PVOD',
  DEFAULT: '',
  NEW: 'NEW',
  PARALLEL: 'PARALLEL',
  MONOPOLY: 'MONOPOLY'
};
export const TAG_VIP = {
  VIP: 'VIP',
  VIP_CONTENT: 'VIP Content',
  VIP_K: 'VIP K+',
  VIP_HBO: 'VIP HBO',
  VIP_PRO: 'VIP PRO',
  VIP_FAMILY: 'VIP FAMILY',
  VIP_SPORT: 'VIP SPORT',
  TVOD: 'TVOD',
  PVOD: 'PVOD'
};

export const TAG_VIP_LABEL = {
  [TAG_VIP.VIP_CONTENT]: 'VIP',
  [TAG_VIP.VIP_K]: 'K+',
  [TAG_VIP.VIP_FAMILY]: 'FAMILY',
  [TAG_VIP.VIP_HBO]: 'HBO GO',
  [TAG_VIP.VIP_SPORT]: 'SPORT K+',
  [TAG_VIP.PVOD]: 'Xem trước'
};

export const BANNER_IMAGE_RATE = 0.44;

export const MENU_TYPE = {
  SCHEDULE: 'schedule'
};
export const ID_COMING_SOON = {
  SCHEDULE: 'schedule',
  NUMBER_FREE: 'number_free',
  NUMBER_VIP: 'number_vip'
};

export const HEADER_LAYER_NAME = {
  MENU: 'MENU',
  NOTIFY: 'NOTIFY',
  PROFILE: 'PROFILE',
  SEARCH: 'SEARCH'
};
export const VIDEO_QUALITY = {
  AUTO: 'AUTO',
  HD: 'HD',
  SD: 'SD',
  FULL_HD: 'FULL_HD'
};
export const VIDEO_PLAYBACK_SPEED = {
  DEFAULT: '1'
};

export const EL_ID = {
  COMMENT_AREA: 'COMMENT_AREA',
  MODAL: 'MODAL',
  SECTION_HOME: 'SECTION_HOME',
  PLAYER_CONTAINER: 'PLAYER_CONTAINER',
  PLAYER_CONTROL: 'PLAYER_CONTROL',
  PLAYER_INNER: 'PLAYER_INNER',
  TAG_PREMIERE: 'TAG_PREMIERE',
  EPISODE_LIST_SCROLL: 'EPISODE_LIST_SCROLL',
  WATCHING_RIBBON: 'WATCHING_RIBBON',
  TVOD_RENTING_RIBBON: 'TVOD_RENTING_RIBBON',
  FAVORITE_VOD: 'FAVORITE_VOD',
  INTRO_COMMENT: 'INTRO_COMMENT',
  DETAIL_COMMENT: 'DETAIL_COMMENT',
  VIE_PLAYER: 'VIE_PLAYER',
  PLAYER_STAGE: 'playerStage',
  LIVETV_PLAYER: 'LIVETV_PLAYER',
  LIVESTREAM_PLAYER: 'LIVESTREAM_PLAYER',
  SEARCH_INPUT: 'SEARCH_INPUT',
  ADS_CONTAINER: 'ADS_CONTAINER',
  PLAY_ADS_BUTTON: 'PLAY_ADS_BUTTON',

  CARD_NUMBER: 'CARD_NUMBER',
  CARD_NAME: 'CARD_NAME',
  CARD_EXPIRED: 'CARD_EXPIRED',
  CARD_CVV: 'CARD_CVV',

  BILLING_ADDRESS: 'BILLING_ADDRESS',

  CARD_NUMBER_CAKE: 'CARD_NUMBER_CAKE',
  CARD_NAME_CAKE: 'CARD_NAME_CAKE',
  CARD_EXPIRED_CAKE: 'CARD_EXPIRED_CAKE',
  CARD_CVV_CAKE: 'CARD_CVV_CAKE',

  PROMOTION_INPUT: 'PROMOTION_INPUT',
  VOUCHER_INPUT: 'VOUCHER_INPUT',
  REFERRAL_CODE_INPUT: 'REFERRAL_CODE_INPUT',
  UPDATE_EMAIL_INPUT: 'UPDATE_EMAIL_INPUT',
  X_BUTTON: 'X_BUTTON',
  MODAL_WELCOME_ADS: 'MODAL_WELCOME_ADS',
  BANNER_INTRO_BACKGROUND: 'BANNER_INTRO_BACKGROUND',
  INPAGE_INTRO: 'INPAGE_INTRO',
  ID_ELM_NAPAS_PAYMENT: 'ID_ELM_NAPAS_PAYMENT',
  PINNED_COMMENT: 'PINNED_COMMENT',
  ONLY_APP_ITEM: 'ONLY_APP_ITEM',
  PIN_PASSWORD: 'PIN_PASSWORD'
};
export const FAB_ID = {
  RAP_VIET3: 'FAB_RAPVIET_VOTING',
  RAP_VIET: 'FAB_RAPVIET',
  MANUAL: 'FAB_MANUAL',
  DOWNLOAD_APP: 'FAB_DOWNLOAD_APP'
};

export const PROFILE_STATUS = {
  VERIFY_OPT: 'verify_otp',
  SUCCESS: 'success',
  WAIT_FOR_DELETE: 3
};

export const OTP_STATUS = {
  FAIL: 'fail'
};

export const BILLBOARD = {
  TITLE_CARD_TIMER: 5
};

export const CARD_DETAIL = {
  MIN_WIDTH: 408,
  MIN_WIDTH_ORIGINAL: 330
};

export const HTTP_CODE = {
  OK_200: 200,
  OK_202: 202,
  FAIL: 400,
  NOT_FOUND: 404,
  UNAUTHORIZED: 401,
  BLOCKED_ACCOUNT: 403,
  MAX_CCU: 405,
  KICKED_DEVICE: 406,
  BLOCKED_ACCOUNT_DELETE: 409,
  GONE: 410,
  BLOCKED_ACCOUNT_423: 423,
  EXPIRE: 426,
  TOO_MANY_REQUEST: 429,
  UNDER_CONSTRUCTION: 511,
  PROFILE_DELETED: 494,
  TOKEN_PROFILE_INVALID: 491,
  ERROR_423: 423,
  ERROR_404: 404,
  ACCOUNT_LINKED: 4009,
  ACCOUNT_NOT_LINKED: 0,
  OTP_LIMITED: 4010,
  ERROR_555: 555
};

export const ERROR_CODE = {
  CODE_0: 0, // Thành công
  CODE_1: 1, // Chưa xác thực người dùng / Dữ liệu yêu cầu không tồn tại / Tính năng TVOD không sẵn sàng / Đạt giới hạn số lần gọi API trong một khoảng thời gian nhất định
  CODE_2: 2, // Thất bại do đạt giới hạn
  CODE_3: 3, // session not exist
  CODE_100: 100, // Dữ liệu gửi lên không hợp lệ
  CODE_101: 101, // Dữ liệu content_id không đúng
  CODE_102: 102, // Dữ liệu tvod_product_id không đúng
  CODE_103: 103, // Dữ liệu order không tìm thấy, không thể thực hiện thanh toán
  CODE_104: 104, // Order không thuộc về user
  CODE_105: 105, // Không tồn tại giao dịch
  CODE_106: 106, // Hết hạn kinh doanh
  CODE_200: 200, // Lỗi phát sinh khi server đang xử lý
  CODE_201: 201, // Không khởi tạo được giao dịch với đối tác thanh toán.
  CODE_109: 109, // Dữ liệu không còn tồn tại,
  CODE_110: 110, // Dữ liệu gửi lên không hợp lệ
  CODE_122: 122, // Trùng mã Pin,
  CODE_20: 20, // Không tạo được profile do vượt quá số lượng tối đa
  CODE_21: 21, // Không tạo được profile do tên không hợp lệ
  CODE_1001: 1001, // Comment blocked
  CODE_2200: 2200, // Danh sách người dùng vừa được cập nhật
  CODE_2201: 2201, // Tất cả các profile kid của account bị xoá hết
  CODE_2202: 2202, // Có 1 người dùng trẻ em đã bị xóa ở thiết bị khác
  CODE_400: 400 // tài khoản người dung không đúng
};

export const PAYMENT_TYPE = {
  TVOD: 'TVOD',
  SVOD: 'SVOD',
  PVOD: 'PVOD'
};

export const STATUS_TRANSACTION = {
  SUCCESS: 1,
  PROCESS: 0
};

export const TIMER = {
  UNDER_CONSTRUCTION: 30000
};

export const KEY_CODE = {
  ESC: 27,
  SPACE: 32,
  ARROW_LEFT: 37,
  ARROW_RIGHT: 39,
  BACK_SPACE: 8
};

export const UTM_SOURCE = {
  TP_BANK: 'tpbank',
  ZALO_PAY: 'zalopay',
  ADBRO: 'Adbro'
};

export const CURRENCY = {
  VND: 'VND',
  D: 'đ'
};

export const PAYMENT_METHOD = {
  NAPAS: 'napas',
  ASIAPAY: 'asiapay',
  VN_PAY: 'vnpay',
  MOMO: 'momo',
  MOCA: 'moca',
  MOBI: 'mobiphone',
  VINA: 'vinaphone',
  ZALO_PAY: 'zalopay',
  SHOPEE_PAY: 'shopeepay',
  VIETTEL: 'viettel',
  PAYOO: 'payoo',
  IAP: 'IAP',
  TP_BANK: 'tpbank',
  VIETTEL_PAY: 'viettelpay',
  QRCODE: 'qrcode',
  CAKE: 'cake',
  QR_VNPAY: 'qr_vnpay'
};

export const SERVICE_NAME = {
  NAPAS: 'napas',
  ASIAPAY: 'asiapay',
  VN_PAY: 'vnpay',
  MOMO: 'momo',
  MOCA: 'moca',
  MOBI: 'mobiphone',
  VINA: 'vinaphone',
  ZALO_PAY: 'zalopay',
  SHOPEE_PAY: 'shopeepay',
  VIETTEL: 'viettel',
  PAYOO: 'payoo',
  IAP: 'IAP',
  TP_BANK: 'tpbank',
  VIETTEL_PAY: 'viettelpay'
};

export const PAYMENT_METHOD_BE = {
  CARD: 'Card',
  ASIAPAY: 'AsiaPay',
  VN_PAY: 'VNPay',
  MOCA: 'Moca',
  SMS: 'SMS',
  ZALO_PAY: 'ZaloPay',
  SHOPEE_PAY: 'ShopeePay',
  IAP: 'IAP',
  TP_BANK: 'TPBank',
  VIETTEL_PAY: 'ViettelPay',
  WALLET: 'wallet',
  MOMO: 'Momo'
};

export const ADS_URL = {
  URL1: 'url1',
  URL2: 'url2',
  URL3: 'url3'
};
export const ADS_POINT = {
  PRE: 'pre',
  MID: 'mid',
  POST: 'post'
};

export const POPUP_ACTION_TYPE = {
  LOGIN: 'LOGIN',
  BUY_PACKAGE: 'BUY_PACKAGE',
  SKIP: 'SKIP'
};

export const CHECK_STATUS_TIMEOUT = 300000;
export const CHECK_STATUS_TIMER = 2000;
export const SCAN_QR_CODE_TIMER = 5000;
export const VieON_TEL = '**********';

export const SEO_PAGES = {
  CATEGORY: 'category_page',
  CATEGORY_NOT_PROMOTED: 'category_page_not_promote',
  COLLECTION: 'collection',
  COLLECTION_DETAIL: 'collection_detail_page',
  ACTOR: 'actor_page',
  TAG: 'tag_page',
  LIVETV: 'livetv_page',
  LIVETV_DETAIL: 'channel_detail_page',
  LIVETV_EPG: 'epg_page',
  DETAIL: 'video_detail_page',
  DETAIL_EPISODE: 'episode_detail_page',
  DETAIL_RELATED: 'video_related_page',
  SEARCH: 'search',
  LIVE_STREAM: 'live_streaming',
  LIVE_STREAM_DETAIL: 'live_streaming_event_detail_page',
  PAYMENT: 'snippet_mua_goi'
};
export const SEO_CONFIG = {
  SNIPPET_GENERAL: 'snippet_general',
  SNIPPET_HOME: 'snippet_more_for_home'
};

export const PAID_TYPE = {
  OVERLAP: 2,
  ACCUMULATE: 1
};

export const NODE_ENV = {
  DEV: 'development',
  PROD: 'production'
};

export const WEB_ENV = {
  DEV: 'develop',
  TESTING: 'testing',
  STAGING: 'staging',
  PRODUCTION: 'production'
};

export const LOCATION = {
  VIETNAM: 'VN'
};
export const SENTRY = {
  PAYMENT: 'PAYMENT',
  API: 'API',
  PLAYER: 'PLAYER',
  PROFILE: 'PROFILE',
  CREATE_TRANSACTION: 'CREATE_TRANSACTION'
};

export const STATUS_OUTSTREAM_ADS = {
  FAIL: 'fail',
  CLOSE: 'close',
  SUCCESS: 'success'
};

export const PLAYER_TIP_STATUS = {
  INIT: 'INIT',
  COLLAPSE: 'COLLAPSE',
  EXPAND: 'EXPAND'
};

export const DOMAIN_API_SSR = {
  CM_V5: 'api.vieon.vn/backend/cm/v5',
  BACKEND_USER: 'api.vieon.vn/backend/user',
  CM_ACTIVITY: 'api.vieon.vn/backend/cm/activity',
  USER_REPORT: 'api.vieon.vn/backend/user-report',
  SHOP_BACKEND_API: 'api.vieon.vn/shop-backend-api'
};

export const RANKING_TAB = {
  SCHEDULE: 'SCHEDULE',
  RANKING: 'RANKING'
};

export const MATCH_STATUS = {
  SCHEDULE: 'SCHEDULE',
  FINISHED: 'FINISHED',
  LIVE: 'LIVE',
  CANCELED: 'CANCELED',
  SUSPENDED: 'SUSPENDED'
};

export const LINK_PLAY_KEY = {
  HLS: 'hlsLinkPlay',
  HLS_BK1: 'hlsBackup1',
  HLS_BK2: 'hlsBackup2',
  DASH: 'dashLinkPlay',
  DASH_BK1: 'dashBackup1',
  DASH_BK2: 'dashBackup2',
  ERROR: 'ERROR'
};

export const ON_OFF = {
  ON: 'on'
};

export const VieON = {
  SUPPORT_EMAIL: '<EMAIL>',
  HOTLINE: '1800 599920'
};

export const PACKAGE_INTRO_LENGTH = 150;
export const DAY_SECOND = 24 * 60 * 60;

export const TVOD = {
  TYPE: {
    MOVIE: 'MOVIE',
    TV_SERIES: 'TV_SERIES',
    RIBBON: 'RIBBON',
    LIVE_EVENT: 'LIVE_EVENT',
    SIMULCAST_SINGLE: 'SIMULCAST_SINGLE',
    SIMULCAST_MULTI: 'SIMULCAST_MULTI'
  },
  USER_TYPE: {
    EXPIRED: -1, // Hết hạn
    NONE: 0, // Chưa mua
    RENTED: 1, // Đã thuê
    WATCHED: 2 // Đã xem
  },
  ID_TYPE: {
    MOVIE: 'content',
    SEASON: 'season',
    EPISODE: 'episode'
  },
  TOAST: {
    EVENT_HAPPENING_REMINDER: 'event_happening_reminder'
  },
  POPUP: {
    TIME_OUT_SALE_PRE_ORDER_HAVE_VOD: 'time_out_sale_pre_order_have_vod',
    TIME_OUT_SALE_PRE_ORDER: 'time_out_sale_pre_order'
  },

  TVOD_REMINDER: 'tvod_reminder',
  TVOD_REMINDER_MULTI: 'tvod_reminder_multi',
  TVOD_ON_BOARDING: 'tvod_onboarding',
  TVOD_ON_BOARDING_PRE_ORDER: 'tvod_onboarding_pre_order',
  TVOD_ON_BOARDING_MULTI: 'tvod_onboarding_multi',
  TVOD_TYPE: 'tvod',
  RENT: 'rent',
  PRE_ORDER: 'pre_order',
  SALE_PRE_ORDER: 'sale_pre_order',
  EVENT_HAPPENING_REMINDER: 'event_happening_reminder',
  DAY3_DAY1_REMINDER_MULTI_INCLUDE_TVOD: 'day3_day1_reminder_multi_include_tvod',
  DAY3_DAY1_REMINDER_MULTI: 'day3_day1_reminder_multi',
  DAY3_DAY1_REMINDER: 'day3_day1_reminder',
  LIVE_STREAM_END: 'livestream_end',
  LIVE_STREAM_END_HAVE_VOD: 'livestream_end_have_vod',
  MISSED_EVENT: 'missed_event',
  MISSED_EVENT_HAVE_VOD: 'missed_event_have_vod'
};

export const BROWSER = {
  KEY: {
    SAFARI: 'SAFARI',
    CHROME: 'CHROME',
    EDGE: 'MICROSOFT EDGE',
    OPERA: 'OPERA',
    FIREFOX: 'FIREFOX',
    COC_COC: 'COC_COC',
    IE: 'INTERNET EXPLORER'
  }
};

export const POSITION = {
  TOP_LEFT: 'topLeft', // state key - don't change
  TOP_RIGHT: 'topRight', // state key - don't change
  TOP_CENTER: 'topCenter', // state key - don't change
  BOTTOM_LEFT: 'bottomLeft', // state key - don't change
  BOTTOM_RIGHT: 'bottomRight', // state key - don't change
  BOTTOM_CENTER: 'bottomCenter', // state key - don't change
  CENTER: 'center',
  MIDDLE_LEFT: 'middleLeft',
  MIDDLE_RIGHT: 'middleRight'
};

export const TOAST_KEY = {
  REMINDER: 'REMINDER'
};

export const EL_PROPERTY = {
  TOP_LEFT: 'TOP_LEFT',
  TOP1_LEFT: 'TOP1_LEFT',
  TOP_RIGHT: 'TOP_RIGHT',
  BOTTOM_LEFT: 'BOTTOM_LEFT',
  BOTTOM_RIGHT: 'BOTTOM_RIGHT',
  BOTTOM_CENTER: 'BOTTOM_CENTER',
  ALL: 'ALL',

  SMALL: 'SMALL',
  MEDIUM: 'MEDIUM',
  LARGE: 'LARGE',
  X_LARGE: 'X-LARGE',

  GREEN: 'GREEN',
  GREEN_GRADIENT: 'GREEN_GRADIENT',
  RED: 'RED',
  RED_GRADIENT: 'RED_GRADIENT',
  GOLD: 'GOLD',
  GOLD_GRADIENT: 'GOLD_GRADIENT',

  DIVIDER_CIRCLE_V: 'DIVIDER_CIRCLE_V',
  DIVIDER_LINE_V: 'DIVIDER_LINE_V',

  LAYOUT_H: 'LAYOUT_H',
  LAYOUT_V: 'LAYOUT_V',

  SLIDE_IN_LEFT: 'SLIDE_IN_LEFT',
  SLIDE_IN_RIGHT: 'SLIDE_IN_RIGHT'
};
export const EL_CLASS: any = {
  TOP_LEFT: 'absolute top-1 left',
  TOP_RIGHT: 'absolute top-1 right',
  BOTTOM_LEFT: 'absolute bottom-1 left',
  BOTTOM_RIGHT: 'absolute bottom-1 right',

  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large',
  X_LARGE: 'x-large',

  GREEN: 'GREEN',
  GREEN_GRADIENT: 'GREEN_GRADIENT',
  RED: 'RED',
  RED_GRADIENT: 'RED_GRADIENT',
  GOLD: 'GOLD',
  GOLD_GRADIENT: 'GOLD_GRADIENT',

  DIVIDER_CIRCLE_V:
    ' divider-cel-margin-x1 large-up-divider-cel-margin-x2 divider-cel-bg-grey239 divider-cel-circle-v1',
  DIVIDER_LINE_V:
    ' divider-cel-margin-x1 large-up-divider-cel-margin-x2 divider-cel-bg-grey239 divider-cel-line-v1',

  LAYOUT_H: ' horizontal',
  LAYOUT_V: ' vertical',

  SLIDE_IN_LEFT: 'animate-slide-in-left',
  SLIDE_IN_RIGHT: 'animate-slide-in-right'
};

export const EL_SIZE_CLASS = {
  SMALL: 'Sm',
  MEDIUM: 'Md',
  LARGE: 'Lg',
  LARGE_SUBTLE: 'LgSubtle',
  VARIANT_SMALL: 'VariantSm',
  VARIANT_MEDIUM: 'VariantMd',
  VARIANT_LARGE: 'VariantLg'
};

export const EL_THEME_CLASS = {
  BLUE: 'Blue',
  GRAY: 'Gray',
  BLACK: 'Black',
  BLACK_GLASS_50: 'BlackGl50',
  WHITE: 'White',
  GREEN: 'Green',
  GREEN_SUBTLE: 'GreenSubtle',
  GOLD: 'Gold',
  RED: 'Red',
  YELLOW: 'Yellow',
  YELLOW_SUBTLE: 'YellowSubtle',
  IMAGE: 'Image',
  DEFAULT: 'default',
  RED_LIVE: 'RedLive',
  PRIMARY: 'primary',
  PRIMARY_SUBTLE: 'primarySubtle',
  PRIMARY_OUT_LINE: 'primaryOutline',
  PRIMARY_OUT_LINE_GLASS: 'primaryOutlineGlass',
  BLUE_TVOD: 'blueTvod'
};
export const EL_ROUNDED_CLASS = {
  TOP_RIGHT: 'roundedTr',
  TOP_RIGHT_SM: 'roundedTrSm',
  TOP_LEFT: 'roundedTl',
  TOP_LEFT_SM: 'roundedTlSm',
  BOTTOM_RIGHT: 'roundedBr',
  BOTTOM_RIGHT_SM: 'roundedBrSm',
  BOTTOM_LEFT: 'roundedBl',
  BOTTOM_LEFT_SM: 'roundedBlSm',
  ALL: 'roundedAll',
  ALL_SM: 'roundedAllSm',
  TOP: 'roundedT',
  TOP_SM: 'roundedTSm'
};

export const BREAKPOINT = {
  SM: '640px',
  MD: '768px',
  LG: '1024px',
  XL: '1280px',
  XXL: '1536px'
};

export const DEVICE_TYPE = {
  DESKTOP: 'desktop',
  APP: 'app',
  TABLET: 'tablet',
  SMART_TV: 'smarttv'
};

export const DEVICE_TYPE_DETECT = {
  BROWSER: 'browser',
  MOBILE: 'mobile',
  TABLET: 'tablet',
  SMART_TV: 'smarttv'
};

export const LOBBY_PROFILE_STEP = {
  ADD: 'ADD',
  EDIT: 'EDIT',
  VIEW: 'VIEW',
  AVATAR_SELECT: 'AVATAR_SELECT',
  AGE_RANGES: 'AGE_RANGES',
  GENDERS: 'GENDERS'
};

export const ZALOPAY = {
  MSG: {
    NOT_ENOUGH_BALANCE: 'Số dư không đủ'
  }
};
export const PIN_CODE = {
  CREATE: 'CREATE',
  CREATE_FORGOT: 'CREATE_FORGOT',
  VERIFY: 'VERIFY',
  VERIFY_EDIT: 'VERIFY_EDIT'
};

export const REGEX = {
  VN_PHONE_NUMBER: /^(0\d{9}|[1-9]\d{8})$/,
  PHONE_NUMBER: /^0[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s./0-9]*$/,
  NUMBER: /^[0-9\b]+$/, // only number,
  // NOT_CHAR_SPECIAL: /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/,
  NOT_CHAR_SPECIAL: /[`!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?~]/,
  EMAIL:
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
};

export const RESOLUTION = {
  SD: 1,
  HD: 2,
  FULL_HD: 3,
  UHD: 4
};

export const LIMIT_DATA = 10;

export const MKT_PARAMS = [
  'af_force_deeplink',
  'fbclid',
  'pid',
  'utm_source',
  'utm_medium',
  'af_adset',
  'af_ad',
  'utm_campaign',
  'c'
];

export const VIDEO_INDEXING = {
  PRODUCT: 'product',
  ALL_BRAND: 'all_brand',
  BRAND: 'brand',
  ALL_PRODUCT: 'all_product'
};
export const POSITION_ACTION = {
  TOAST: 'TOAST',
  INDICATOR: 'INDICATOR'
};

export const TIER: any = {
  BRONZE: 'Đồng',
  SILVER: 'Bạc',
  GOLD: 'Vàng',
  DIAMOND: 'Kim Cương'
};

// Loyalty
export const LOYALTY_TYPE = {
  PROMOTION: 1,
  POINT: 2
};

export const LOYALTY_EXPIRY_TYPE = {
  NUMBER_OF_DAY: 1,
  SPECIFIC_DATE: 2
};

export const PAGE_MAX_SIZE = 10000;

export const LOYALTY_STATUS = {
  SUCCESS: 'success',
  FAILED: 'failed',
  PROMOTE_TO_SILVER: 'promote_to_silver',
  PROMOTE_TO_GOLD: 'promote_to_gold',
  PROMOTE_TO_DIAMOND: 'promote_to_diamond'
};

export const TOAST = {
  KIND: {
    TIMER: 'timer',
    WARNING: 'warning'
  }
};

export const NOTIFY_TYPE = {
  TIER_CHANGED: 'TIER_CHANGED',
  EARNING_POINTS: 'EARNING_POINTS'
};

export const PROVIDER = {
  FACEBOOK: 'facebook',
  GOOGLE: 'google',
  APPLE: 'apple'
};

export const DATE_TIME = {
  DD_MM_YYYY: 'dd/mm/yyyy'
};

export const DATE_FORMAT = {
  SLASH: 'dd/MM/yyyy',
  DASH: 'dd-mm-yyyy',
  YYMMDD: 'yyyy-mm-dd'
};

export const LANG = {
  VI: 'vi',
  EN: 'en'
};

export const VOUCHER_PACKAGE_TYPE = {
  TVOD: 2,
  SVOD: 1
};
export const VOUCHER_TVOD_TYPE = {
  MOVIE: 1,
  SEASON: 2,
  LIVE_EVENT: 3,
  SIMULCAST: 4,
  LIFE_TIME: 5
};

export const KEY_TIME_NOTIFY = {
  TO_DAY: 0,
  YESTERDAY: 1,
  OLD_DAYS: 2
};

export const ACTION_TYPE_NOTIFY = {
  PAYMENT: 'navigate_to_payment',
  DETAIL: 'navigate_to_detail',
  PLAYER: 'navigate_to_player',
  BROWSER: 'navigate_to_browser',
  FAVORITE: 'navigate_to_favorite'
};
export const CONTENT_TYPE_NOTIFY = {
  LIVE_TV: 7,
  LIVE_EVENT: 0
};
export const TYPE_TRIGGER_ALWAYS = {
  SEARCH: 'search',
  VOD_INTRO: 'vod_intro',
  LIVE_TV: 'livetv',
  COMING_SOON: 'coming_soon',
  PROFILE: 'profile',
  NOTIFICATION: 'notification'
};

export const LIKE_VIDEO = {
  LIKE: 2,
  UNLIKE: 0
};

export const OUTSTREAM_TYPE = {
  WELCOME_AD_HOME: {
    value: '1',
    name: 'welcome-ad-home'
  },
  WELCOME_AD_MAIN_MENU: {
    value: '2',
    name: 'welcome-ad-main-menu'
  },
  WELCOME_AD_VIDEO_INTRO: {
    value: '3',
    name: 'welcome-ad-video-intro'
  },
  MASTER_BANNER: {
    value: '4',
    name: 'master-banner'
  },
  BANNER_RIBBON: {
    value: '5',
    name: 'banner-ribbon'
  },
  PROMOTED_RIBBON: {
    value: '6',
    name: 'promoted-ribbon'
  },
  INPAGE: {
    value: '7',
    name: 'inpage-banner-video-intro'
  },
  LOBBY: {
    value: '8',
    name: 'lobby'
  }
};

export const AD_TYPE = {
  INSTREAM: 0,
  OVERLAY: 1 //  Paused ad, Onstream(PiP), Companion banner
};

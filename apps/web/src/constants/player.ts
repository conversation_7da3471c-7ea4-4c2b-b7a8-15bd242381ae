import {
  SIGMA_DRM_FAIR_PLAY,
  SIGMA_DRM_FAIR_PLAY_CERT,
  SIGMA_DRM_PLAY_READY,
  SIGMA_DRM_WIDE_VINE,
  TODAY_DRM_SERVER_FAIR_PLAY,
  TODAY_DRM_SERVER_FAIR_PLAY_CERT,
  TODAY_DRM_SERVER_WIDE_VINE
} from '@config/ConfigEnv';

export const DRM_PROVIDER = {
  SIGMA_DRM: 'sigmadrm'
};

export const ERROR_PLAYER = {
  SEVERITY: {
    RECOVERABLE: 1,
    CRITICAL: 2
  },
  CATEGORY: {
    NETWORK: 1,
    TEXT: 2,
    MEDIA: 3,
    MANIFEST: 4,
    STREAMING: 5,
    DRM: 6,
    PLAYER: 7,
    CAST: 8,
    STORAGE: 9,
    ADS: 10
  },
  TYPE: {
    PLAYER: 'PLAYER',
    API_DETAIL: 'API_DETAIL',
    EMPTY_LINK: 'EMPTY_LINK',
    QNET: 'QNET',
    VALIDATE_KPLUS: 'VALIDATE_KPLUS',
    SOCKET: 'SOCKET',
    UNDEFINED: 'UNDEFINED'
  }
};

export const HLS_CONFIG = {
  maxBufferLength: 24,
  maxMaxBufferLength: 200,
  maxBufferSize: 20 * 1000 * 1000,
  maxLoadingDelay: 0,
  manifestLoadingTimeOut: 5000, // time out
  manifestLoadingMaxRetry: 2, // retry
  manifestLoadingRetryDelay: 1000,
  manifestLoadingMaxRetryTimeout: 64000,
  levelLoadingTimeOut: 10000,
  levelLoadingMaxRetry: 3,
  levelLoadingRetryDelay: 1000,
  levelLoadingMaxRetryTimeout: 64000,
  nudgeMaxRetry: 6,
  enableWebVTT: true,
  subtitleDisplay: false
};

export const DRM = {
  CASTLAB: 'CASTLAB',
  QNET: 'QNET',
  K_PLUS: 'K+',
  HBO: 'HBO',
  VieON: 'vieon',
  APP_ID: 'vieon'
};

export const QNET = {
  USER_ID: '7-926dbdfbeee346e692c1d27aece20131',
  SESSION_ID:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySUQiOiI3LTkyNmRiZGZiZWVlMzQ2ZTY5MmMxZDI3YWVjZTIwMTMxIiwic2Vzc2lvbklkIjoiZGZnZGZnZGZnZGYiLCJ0aW1lc3RhbXAiOiIxNTgzMjA1MjMxIn0.dUbMaENNRRDb5WcIFqainSrvQQZWgv9MIytk-KSySvo',
  MERCHANT: 'qnet'
};

export const PLAYER_NAME = {
  SHAKA_PLAYER: 'SHAKA_PLAYER',
  HLS_PLAYER: 'HLS_PLAYER'
};

export const PLAYER_TYPE: any = {
  MASTER_BANNER: 'MASTER_BANNER',
  BANNER_COLLECTION: 'BANNER_COLLECTION',
  BANNER: 'BANNER',
  CARD_DETAIL: 'CARD_DETAIL',
  CARD_HOVER: 'CARD_HOVER',
  CARD_AIRING: 'CARD_AIRING',
  END_SCREEN_TRAILER: 'END_SCREEN_TRAILER',
  VOD: 'VOD'
};

export const PLAYER_STATUS = {
  PLAYING: 1,
  ENDED: 2,
  ERROR: 3,
  PAUSED: 4,
  WAITING: 5,
  CLEAR: 6
};

export const DRM_CERT = {
  DRM_TODAY_FAIR_PLAY: TODAY_DRM_SERVER_FAIR_PLAY_CERT,
  SIGMA_FAIR_PLAY: SIGMA_DRM_FAIR_PLAY_CERT
};

export const DRM_SERVER = {
  TODAY_WIDE_VINE: TODAY_DRM_SERVER_WIDE_VINE,
  TODAY_FAIR_PLAY: TODAY_DRM_SERVER_FAIR_PLAY,
  SIGMA_WIDE_VINE: SIGMA_DRM_WIDE_VINE,
  SIGMA_FAIR_PLAY: SIGMA_DRM_FAIR_PLAY,
  SIGMA_PLAY_READY: SIGMA_DRM_PLAY_READY
};

import React, { useEffect } from 'react';
import { ENABLE_SDK_GG_ADSENSE } from '@config/ConfigEnv';

const GGAdsense = () => {
  useEffect(() => {
    const loadGGAdsenseScript = async () => {
      if (!ENABLE_SDK_GG_ADSENSE || ENABLE_SDK_GG_ADSENSE === 'false') {
        return;
      }

      try {
        const script = document.createElement('script');
        script.src =
          'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8375416224350816';
        script.id = 'GGAdsense';
        script.async = true;
        script.crossOrigin = 'anonymous';

        await new Promise((resolve, reject) => {
          script.onload = resolve;
          script.onerror = reject;
          document.body.appendChild(script);
        });

        console.log('Google Adsense script loaded successfully');
      } catch (error) {
        console.error('Failed to load Google Adsense script:', error);
      }
    };

    loadGGAdsenseScript();

    return () => {
      const script = document.getElementById('GGAdsense');
      if (script) {
        document.body.removeChild(script);
      }
    };
  }, []);

  return null;
};

export default React.memo(GGAdsense);

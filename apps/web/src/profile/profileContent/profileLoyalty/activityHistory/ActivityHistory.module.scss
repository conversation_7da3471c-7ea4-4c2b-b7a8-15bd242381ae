// Modules
@use 'sass:math';

/* =============== function caculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);

  //
  @return #{$remSize}rem;
}

/* =============== style =============== */
.activity-history {
  background: rgba(255, 255, 255, 0.02);
  border-radius: rem(16);
  backdrop-filter: blur(18px);
}

table.history-table {
  border: unset !important;
  thead.head {
    background-color: unset !important;
    border: unset !important;
    th {
      padding-left: 0;
      padding-right: 0;
    }
  }
  tbody.body {
    background-color: unset;
    tr {
      border: rem(1) solid #646464;
      margin-bottom: rem(16);
      padding-left: rem(8);
      padding-right: rem(8);
      td {
        font-size: rem(13);
        padding-left: 0;
        padding-right: 0;
        &::before {
          font-weight: 600;
          color: #fff;
        }
      }
    }
  }

  @media screen and (min-width: rem(769)) {
    thead.head {
      th {
        font-size: rem(16);
        &:last-child,
        &:first-child {
          padding-left: 0;
          padding-right: 0;
        }
      }
    }
    tbody.body {
      background-color: unset;
      tr {
        border-top: rem(1) solid #646464;
        border-bottom: unset;
        border-left: unset;
        border-right: unset;
        margin-bottom: 0;
        td {
          font-size: rem(16);
          &:last-child,
          &:first-child {
            padding-left: 0;
            padding-right: 0;
          }
        }
      }
    }
  }
}

.text-red {
  color: #e74c3c !important;
}
.text-green {
  color: #3ac882 !important;
}

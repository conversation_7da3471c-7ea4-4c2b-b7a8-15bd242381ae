import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import { getUsedPointHistory, getEarningActivity } from '@actions/user';
import { ID } from '@constants/constants';
import { TEXT } from '@constants/text';
import Tabs from '../Tabs';
import styles from './ActivityHistory.module.scss';
import HistoryTable from './HistoryTable';

const TABS: any = [
  { title: TEXT.TAB.PLUS_POINT_ACTIVITY, id: ID.PLUS_POINT_ACTIVYTY },
  { title: TEXT.TAB.USED_POINT_ACTIVITY, id: ID.USED_POINT_ACTIVYTY }
];
const TABLE_TITLE: any = {
  EARNING_POINT: [
    { width: 0, title: TEXT.TIME, id: 1, key: 'earningDateTime' },
    { width: '26%', title: TEXT.ACTIVITY, id: 2, key: 'activityName' },
    { width: '18%', title: TEXT.POINT_CHANGE, id: 3, key: 'earningPoints' },
    { width: '18%', title: TEXT.CURRENT_POINT, id: 4, key: 'availablePoints' },
    { width: 0, title: TEXT.TOTAL_POINT_TEXT, id: 5, key: 'totalPoints' }
  ],

  USED_POINT: [
    { width: 0, title: TEXT.TIME, id: 6, key: 'eventDateTime' },
    { width: '26%', title: TEXT.ACTIVITY, id: 7, key: 'eventName' },
    { width: '18%', title: TEXT.POINT_CHANGE, id: 8, key: 'usedPoints' },
    { width: '18%', title: TEXT.CURRENT_POINT, id: 9, key: 'availablePoints' },
    { width: 0, title: TEXT.VOUCHER_CODE, id: 10, key: 'voucherCode' }
  ]
};
const ActivityHistory = ({ tabId }: any) => {
  const [tab, setTab] = useState(TABS[0].id);
  const earningPointsData = useSelector(
    (state: any) => state?.User?.loyalty?.earningActivityHistory || {}
  );
  const usedPointHistory = useSelector(
    (state: any) => state?.User?.loyalty?.usedPointHistory || {}
  );

  const dispatch = useDispatch();
  const profile = useSelector((state: any) => state?.Profile?.profile || {});

  useEffect(() => {
    dispatch(getUsedPointHistory({ userId: profile?.id }));
    dispatch(getEarningActivity({ userId: profile?.id }));
  }, [profile?.id]);

  return (
    <div
      className={classNames(
        'padding-small-up-12 padding-large-up-24 margin-small-up-top-12',
        styles['activity-history']
      )}
    >
      <Tabs
        data={TABS}
        id={tabId}
        setTab={setTab}
        tab={tab}
        className="padding-small-up-bottom-16 padding-large-up-bottom-28 margin-small-up-left-auto margin-small-up-right-auto margin-medium-up-left-4 margin-medium-up-right-4"
        itemClass="text-large-up-16 text-14"
      />

      {tab === ID.PLUS_POINT_ACTIVYTY && (
        <HistoryTable
          tableTitle={TABLE_TITLE.EARNING_POINT}
          tableContent={earningPointsData?.items}
        />
      )}
      {tab === ID.USED_POINT_ACTIVYTY && (
        <HistoryTable tableTitle={TABLE_TITLE.USED_POINT} tableContent={usedPointHistory?.items} />
      )}
    </div>
  );
};

export default React.memo(ActivityHistory);

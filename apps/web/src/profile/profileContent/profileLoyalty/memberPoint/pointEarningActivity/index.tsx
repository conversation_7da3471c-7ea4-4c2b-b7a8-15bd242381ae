import React from 'react';
import { useSelector } from 'react-redux';
import classNames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import { TEXT } from '@constants/text';
import PointEarning from './PointEarning';
import styles from './PointEarningActivity.module.scss';

const PointEarningActivity = () => {
  const activities = useSelector((state: any) => state?.User?.loyalty?.earningActivities || {});

  if (isEmpty(activities)) return null;
  return (
    <div
      className={classNames(
        'padding-x-small-up-8 padding-small-up-top-20 padding-small-up-bottom-12 padding-x-large-up-36 layer-1 relative',
        styles['point-earning']
      )}
    >
      <div className="text-16 text-large-up-24 text-gray173 text-bold relative">
        {TEXT.EARNING_POINT_ACTIVITY}
      </div>
      <PointEarning data={activities} />
    </div>
  );
};

export default React.memo(PointEarningActivity);

import React from 'react';
import { useSelector } from 'react-redux';
import { TEXT } from '@constants/text';
import PointEarning from '../pointEarningActivity/PointEarning';
import Voucher from '../redeemVoucher/Voucher';

const SeeAllPage = () => {
  const seeAllStatus = useSelector((state: any) => state?.User?.loyalty?.seeAllStatus || '');
  const activities = useSelector((state: any) => state?.User?.loyalty?.earningActivities || []);
  const vouchers = useSelector((state: any) => state?.User?.loyalty?.vouchers || {});
  const info = useSelector((state: any) => state?.User?.loyalty?.info || {});
  const { availablePoints } = info;

  if (seeAllStatus === TEXT.EARNING_POINT) return <PointEarning data={activities} />;
  return (
    <>
      {vouchers?.items?.map((item: any, index: any) => (
        <Voucher key={item?.category?.id + index} item={item} currentPoint={availablePoints} />
      ))}
    </>
  );
};

export default React.memo(SeeAllPage);

import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import { useDispatch, useSelector } from 'react-redux';
import Image from '@components/basic/Image/Image';
import Button from '@components/basic/Buttons/Button';
import { useVieRouter } from '@customHook';
import { TEXT } from '@constants/text';
import { openPopup } from '@actions/popup';
import { ID, PAGE_MAX_SIZE, POPUP, POSITION } from '@constants/constants';
import { getVouchers, redeemVoucher, seeAllVoucher } from '@actions/user';
import { setLoading, clearToast } from '@actions/app';
import ConfigImage from '@config/ConfigImage';
import styles from './RedeemVoucher.module.scss';
import SubItemSlider from './SubItemSlider';

const Voucher = ({ item, currentPoint }: any) => {
  const dispatch = useDispatch();
  const seeAllStatus = useSelector((state: any) => state?.User?.loyalty?.seeAllStatus || '');
  const router = useVieRouter();
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const [listItemsFiltered, setListFiltered] = useState<any>({});
  const timerRef = useRef<any>(null);

  useEffect(
    () => () => {
      clearTimeout(timerRef.current);
    },
    []
  );

  useEffect(() => {
    if (router.query.slug !== ID.LOYALTY_POINT) {
      dispatch(seeAllVoucher(''));
    }
  }, [router.query.slug]);

  const handleClickSeeAll = () => {
    if (item?.category?.id) {
      dispatch(
        getVouchers({
          userId: profile?.id,
          categoryIds: item?.category?.id,
          isClickSeeAll: true,
          voucherPageSize: PAGE_MAX_SIZE
        })
      );
    }
  };

  const handleCloseToast = () => {
    dispatch(
      clearToast({
        position: POSITION.BOTTOM_RIGHT
      })
    );
  };

  const handleRedeem = (data: any) => {
    if (profile?.mobile || (profile?.email && profile?.emailVerified)) {
      const replaceArray = {
        '{user_type}': profile?.mobile
          ? TEXT.USER_LABEL.PHONE_NUMBER.toLowerCase()
          : TEXT.USER_LABEL.EMAIL.toLowerCase(),
        '{user_value}': profile?.mobile ? profile?.mobile : profile?.email
      };
      dispatch(setLoading(true));

      dispatch(
        redeemVoucher({
          userId: profile?.id,
          voucherId: data?.voucherRefId,
          data: replaceArray || {},
          onCloseLoyalty: handleCloseToast,
          codeDeliveryChannel: profile?.mobile ? 1 : 2
        })
      );
      clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => {
        dispatch(setLoading(false));
      }, 2000);
      return;
    }
    dispatch(
      openPopup({
        name: POPUP.NAME.REDEEM_VOUCHER,
        voucherId: data?.voucherRefId
      })
    );
  };

  if (!item?.category) return null;
  return (
    <div className="padding-small-up-top-12 padding-xlarge-up-top-24">
      <div className="text-16 text-large-up-18 text-white p-b2 relative padding-small-up-right-64">
        <span className="text-medium">{item?.category?.name}</span>
        {seeAllStatus && (
          <Button
            title={TEXT.BACK}
            subTitle={TEXT.BACK}
            className="text-green text-12 text-large-up-14 text-bold absolute right p-y1 p-x2 top"
            onClick={() => dispatch(seeAllVoucher(''))}
          />
        )}
      </div>

      {!isEmpty(item?.subCategories) && (
        <SubItemSlider
          data={item?.subCategories}
          categoryId={item?.category?.id}
          seeAllStatus={seeAllStatus}
          setListFiltered={setListFiltered}
        />
      )}
      <div
        className={classNames(
          seeAllStatus ? styles['voucher-box-see-all'] : styles['voucher-box'],
          'grid-x align-middle'
        )}
      >
        {(!isEmpty(listItemsFiltered?.items)
          ? listItemsFiltered?.items
          : (!seeAllStatus ? item?.vouchers?.items?.slice(0, 6) : item?.vouchers?.items) || []
        )?.map((item: any) => (
          <VoucherItem
            key={item.id}
            data={item}
            onRedeemVoucher={() => handleRedeem(item)}
            seeAllStatus={seeAllStatus}
            currentPoint={currentPoint}
          />
        ))}
      </div>
      {(!isEmpty(listItemsFiltered?.items)
        ? listItemsFiltered?.hasNextPage && item?.vouchers?.hasNextPage
        : item?.vouchers?.hasNextPage) && (
        <div className="flex-box justify-content-center padding-small-up-top-6 padding-large-up-top-12">
          <button
            title={TEXT.SEE_ALL}
            className={classNames('button button--large-up text-white', styles['button-see-more'])}
            onClick={handleClickSeeAll}
          >
            <span className="text">{TEXT.SEE_ALL}</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default React.memo(Voucher);

export const VoucherItem = ({ data, onRedeemVoucher, seeAllStatus, currentPoint }: any) => {
  // handle class
  const cellClassName = classNames(
    'padding-y-small-up-12 padding-y-large-up-20 padding-x-small-up-8 cell'
  );
  const cellDetailClassName = classNames(
    'relative shrink',
    styles['voucher-detail'],
    cellClassName
  );

  // end handle class
  const { image, voucherName, pointsToRedeem, remainingVouchers } = data || {};
  const isDisabled = pointsToRedeem > (currentPoint || 0);
  const dispatch = useDispatch();

  const handleClickDetail = (e: any, data: any) => {
    e.preventDefault();
    dispatch(
      openPopup({
        name: POPUP.NAME.DETAIL_LOYALTY,
        data,
        type: 1
      })
    );
  };

  return (
    <div
      className={classNames(
        'padding-x-medium-up-24 padding-y-6 size-w-full item',
        seeAllStatus ? 'medium-6 xlarge-4' : 'medium-6'
      )}
    >
      <div
        className={classNames(
          'grid-x size-w-full text-white round-4 layer-1',
          styles['voucher-item']
        )}
      >
        <div
          className={classNames(
            'auto grid-x padding-x-small-up-16',
            cellClassName,
            styles['voucher-line']
          )}
        >
          <div
            className={classNames(
              'cell large-shrink small-3 flex-box align-items-end',
              styles[`voucher-image`]
            )}
          >
            <span className={classNames('relative overflow', styles.image)}>
              <Image src={image || ConfigImage.defaultBanner16x9} notWebp />
            </span>
          </div>
          <div
            className={classNames(
              'cell large-auto small-9 flex p-y1 padding-small-up-left-8 padding-large-up-left-16',
              styles.content
            )}
          >
            <div
              className={classNames('text-medium text-12 line-clamp m-b2 text-xlarge-up-16')}
              data-line-clamp="2"
            >
              {voucherName}
            </div>
            <Button
              className="text-12 text-gray239 text-medium"
              title={TEXT.SEE_DETAIL}
              subTitle={TEXT.SEE_DETAIL}
              onClick={(e: any) => handleClickDetail(e, data)}
            />
          </div>
        </div>
        <div className={cellDetailClassName}>
          <div
            className={classNames(
              'size-h-full flex-box justify-content-center align-items-center text-center',
              styles['read-more']
            )}
          >
            <div
              className={classNames(
                'text text-12 text-medium-up-16 text-large-up-20 text-bold',
                !remainingVouchers ? styles['text-gray64'] : 'text-green'
              )}
            >
              {pointsToRedeem}
            </div>
            <div
              className={classNames(
                'text text-12 text-bold',
                !remainingVouchers ? styles['text-gray64'] : 'text-green'
              )}
            >
              {TEXT.VIECOIN}
            </div>
            <Button
              className={classNames(
                'button button--green button--small m-t2',
                styles['button-redeem']
              )}
              title={TEXT.REDEEM}
              subTitle={TEXT.REDEEM}
              onClick={onRedeemVoucher}
              disabled={isDisabled || !remainingVouchers}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

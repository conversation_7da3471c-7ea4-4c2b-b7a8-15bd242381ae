// Modules
@use 'sass:math';

/* =============== function caculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);

  //
  @return #{$remSize}rem;
}

/* =============== style =============== */
// Redeem vouchers
.redeem-voucher {
  background: rgba(255, 255, 255, 0.02);
  border-radius: rem(16);
  backdrop-filter: blur(18px);
}

.m-r-full {
  margin-right: -100% !important;
}
.text-gray64 {
  color: #646464;
}

.slider-sub-items {
  & > div > * {
    width: max-content;
    &:not(.active) {
      button {
        &:hover {
          color: #fff;
        }
      }
    }
  }
}
.prev,
.next {
  top: 50%;
  transform: translateY(-50%);
  &::after {
    position: absolute;
    content: '';
    width: rem(40);
    height: 100%;
    z-index: -1;
    top: 0;
  }
}
.prev {
  &::after {
    left: 0;
    background: linear-gradient(
      90deg,
      rgba(17, 17, 17, 0.7) 0%,
      rgba(17, 17, 17, 0.5) 75%,
      rgba(17, 17, 17, 0.3) 100%
    );
  }
}
.next {
  &::after {
    background: linear-gradient(
      270deg,
      rgba(17, 17, 17, 0.7) 0%,
      rgba(17, 17, 17, 0.5) 75%,
      rgba(17, 17, 17, 0.36) 100%
    );
    right: 0;
  }
}

.voucher-box {
  max-width: rem(1078);
}

// voucher item Style
.voucher-item {
  background-color: rgba(34, 34, 34, 0.2);
  border: rem(1) solid rgba(255, 255, 255, 0.1);
  max-height: rem(132);
  position: relative;
  border-radius: rem(8);
  box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.25), -5px -5px 5px rgba(39, 39, 44, 0.56),
    10px 10px 10px rgba(4, 4, 4, 0.64);

  // voucher detail Style
  .voucher-detail {
    width: rem(108);
    &::before,
    &::after {
      position: absolute;
      left: rem(-8);
      content: '';
      width: rem(16);
      height: rem(8);
      background-color: rgba(4, 4, 4, 0.64);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 0 rem(10) rgba(4, 4, 4, 0.64) inset;
    }
    &::before {
      top: rem(-1);
      background-color: rgba(34, 34, 34, 0.8);
      border-bottom-left-radius: rem(100);
      border-bottom-right-radius: rem(100);
      border-top: 0;
      box-shadow: 0 0 rem(10) rgba(34, 34, 34, 0.8) inset;
    }
    &::after {
      bottom: rem(-1);
      border-top-left-radius: rem(100);
      border-top-right-radius: rem(100);
      border-bottom: 0;
    }
    & > div {
      &::before {
        border-left: 1px dashed rgba(34, 34, 34, 0.2);
        content: '';
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0;
        width: rem(1);
        height: 77%;
      }
    }
  }

  .voucher-line {
    position: relative;
    z-index: -1;

    &::before {
      position: absolute;
      top: 50%;
      right: 0;
      content: '';
      height: rem(90);
      border: rem(1) dashed rgba(255, 255, 255, 0.1);
      transform: translateY(-50%);
    }
  }

  .read-more {
    flex-direction: column;
    & > div {
      line-height: normal;
    }
  }

  .button-see-more {
    span {
      font-size: rem(16) !important;
      font-family: 'Roboto Bold', sans-serif !important;
    }
  }

  .button-redeem {
    border-radius: rem(4);
    span {
      font-family: 'Roboto Bold', sans-serif !important;
      font-size: 14px !important;
    }
    &:disabled {
      background: #454545 !important;
      box-shadow: 0px 0px 1px #636363;
      span {
        color: #646464 !important;
      }
    }
  }

  // Content Style
  .content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-around;
  }
  .image {
    max-width: rem(84);
    max-height: rem(84);
    object-fit: cover;
    object-position: center;
  }
}

// Media screen
@media screen and (min-width: rem(640)) {
  .voucher-box,
  .voucher-box-see-all {
    margin-left: rem(-24);
    margin-right: rem(-24);
  }
}

@media only screen and (max-width: rem(640)) {
  .voucher-item {
    .voucher-detail {
      width: rem(86);
      &::before,
      &::after {
        left: rem(calc(0 - 6));
        width: rem(12);
        height: rem(6);
      }
    }

    .button-redeem {
      span {
        font-size: rem(10) !important;
        line-height: normal;
      }
    }
  }
}

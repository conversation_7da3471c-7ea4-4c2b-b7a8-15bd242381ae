import React from 'react';
import classnames from 'classnames';
import { TEXT } from '@constants/text';
import styles from './ProfileKidsActivity.module.scss';

const ViewDurationActivity = ({ summaryDurationData }: any) => {
  const { data } = summaryDurationData || {};
  const handleDisplayDuration = (data: any) => {
    if ((data?.duration === '' || data?.duration?.seconds === 0) && data?.parseDate < Date.now()) {
      return <span className={styles['text-gray9b']}>{TEXT.NO_ACTIVITY}</span>;
    }
    if (data?.parseDate > Date.now()) {
      return <span className={styles['text-gray9b']}>{TEXT.NO_DATA}</span>;
    }
    return (
      <span className="text-white">{data?.duration?.text || `${data?.duration?.seconds}s`}</span>
    );
  };

  return (
    <div
      className={classnames(
        'grid-x size-w-full animate-fade-in padding-x-small-up padding-x-large-up-12',
        styles['duration-activity']
      )}
    >
      <div
        className={classnames(
          'grid-x align-middle text-white text-14 size-w-full text-medium padding-y-small-up-8 padding-x-small-up-12',
          styles['line-bottom']
        )}
      >
        <div className="cell small-7">{TEXT.TOTAL_DURATION}</div>
        <div className="cell small-5">
          {data?.totalDuration?.text || `${data?.totalDuration?.seconds}s`}
        </div>
      </div>
      <div className="size-w-full">
        {(data?.details || [])?.map((item: any) => (
          <div
            key={item.date}
            className={classnames(
              'grid-x align-middle size-w-full text-14 text-white padding-small-up-12',
              styles['item-activity']
            )}
          >
            <div className={classnames('cell small-7', styles.capi)}>{item.date}</div>
            <div className="cell small-5">{handleDisplayDuration(item)}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default React.memo(ViewDurationActivity);

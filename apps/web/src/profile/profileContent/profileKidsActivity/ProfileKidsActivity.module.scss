// Modules
@use 'sass:math';

/* =============== function caculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);

  //
  @return #{$remSize}rem;
}

/* =============== style =============== */

.line-bottom {
  border-bottom: #{rem(1)} dashed #646464;
  @media screen and (min-width: rem(1024)) {
    border-bottom: #{rem(1)} solid #646464;
  }
}

.menu {
  overflow-x: auto;
  display: flex;
  &-item {
    border-radius: rem(4);
    transition: all 0.35s ease;
    min-width: rem(204);
    border: rem(1) solid #333333;

    &:not(:first-child) {
      margin-left: rem(8);
    }
    &:not(:last-child) {
      margin-right: rem(8);
    }

    &.active {
      background: #646464;
    }
  }
  @media screen and (min-width: rem(1024)) {
    flex-direction: column;
    &-item {
      min-width: auto;
      border: none;

      &:not(:first-child) {
        margin-left: 0;
      }
      &:not(:last-child) {
        margin-right: 0;
      }
      &:hover {
        background: #333333;
      }
    }
  }
}
.capi::first-letter {
  text-transform: uppercase;
}
.tab {
  &-item {
    transition: all 0.35s ease;
    &:not(:last-child) {
      margin-right: rem(40);
      &::before {
        content: '';
        right: rem(-21);
        width: rem(1);
        height: 100%;
        position: absolute;
        top: 0;
        background-color: #9b9b9b;
      }
    }

    &:not(:disabled) {
      &:hover {
        color: white;
      }
    }
    &:disabled {
      cursor: inherit;
    }
  }
}

.kid-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: rem(12);
  height: rem(12);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #3ac882;
  border-radius: 50%;
  svg {
    width: rem(8);
    height: rem(8);
  }
}
.duration-activity {
  max-width: rem(444);
}
.history-activity {
  max-width: rem(920);
  .item {
    display: flex;
    flex-direction: column;
    gap: rem(8);
    @media screen and (min-width: rem(1024)) {
      flex-direction: row;
      gap: rem(4);
    }
  }
  .line-left {
    display: flex;
    & > :first-child {
      width: 54%;
      margin-right: rem(8);
    }
    & > :last-child {
      width: 46%;
      margin-left: rem(8);
    }
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: calc(54% - rem(1));
      width: rem(1);
      height: rem(8);
      transform: translate(-50%, -54%);
      background-color: #333333;
    }
    @media screen and (min-width: rem(1024)) {
      flex-direction: column;
      & > :first-child {
        width: 100%;
        margin-right: 0;
      }
      & > :last-child {
        width: 100%;
        margin-left: 0;
      }

      &::before {
        top: 0;
        left: 0;
        height: 100%;
        transform: translate(0, 0);
      }
    }
  }
}

.item-activity {
  @media screen and (min-width: rem(1024)) {
    border-radius: rem(4);
  }

  &:nth-child(even) {
    background: #222222;
  }
}

.skeleton {
  height: rem(16);
  width: 100%;
  &1,
  &2 {
    width: 100%;
    height: rem(16);
  }
  &:not(:last-child) {
    margin-bottom: rem(16);
  }
  @media screen and (min-width: rem(1024)) {
    width: 85%;
    &1,
    &2 {
      height: rem(12);
    }
    &2 {
      width: 68%;
    }
  }
}

.text-gray9b {
  color: #9b9b9b;
}

.imageRestriction {
  @apply w-[44px] h-[44px];
}
.activeKidRestriction {
  @apply py-2 lg:py-4;
  @apply rounded-none lg:rounded-l rounded-r-none border-none lg:border-solid border-[#333] border border-r-0 bg-[#22222233];
  @apply shadow-[5px_5px_10px_0px_#111,-5px_-5px_12px_0px_#191919,10px_10px_20px_0px_rgba(0,0,0,0.25)_inset];
  @apply flex-col lg:flex-row min-w-[auto] lg:min-w-[204px] w-[25%] lg:w-auto;
}
.disable {
  @apply opacity-30  py-2 lg:py-4;
  @apply border-none border border-r-0 border-transparent lg:border-solid;
  @apply hover:rounded-r-none;
  @apply flex-col lg:flex-row min-w-[auto] lg:min-w-[204px] w-[25%] lg:w-auto;
}
.overFlow {
  @apply overflow-auto lg:overflow-visible p-0 lg:pb-6;
}

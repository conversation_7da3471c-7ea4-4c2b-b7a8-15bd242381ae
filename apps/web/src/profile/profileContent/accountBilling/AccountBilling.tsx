import { getDataTriggerPoint } from '@actions/trigger';
import TriggerTouchPoint from '@components/home/<USER>';
import { ID, POSITION_TRIGGER, TYPE_TRIGGER_ALWAYS } from '@constants/constants';
import { createTimeout } from '@helpers/common';
import ManagementUser from '@profile/profileContent/accountBilling/ManagementUser';
import ReferralProgProfile from '@profile/profileContent/accountBilling/ReferralProgProfile';
import isEmpty from 'lodash/isEmpty';
import React, { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AccountInfo from './AccountInfo';
import Setting from './Setting';
import SocialLinked from './SocialLinked';
import AccountInvoice from './AccountInvoice';
const AccountBilling = React.memo(({ tabId, profile, onHandleAction, updateAllowPush }: any) => {
  const dispatch = useDispatch();
  const clickTimerRef = useRef<any>(null);
  const { dataTriggerProfile } = useSelector((state: any) => state?.Trigger);
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile);
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const { referralProg } = useSelector((state: any) => state?.App?.webConfig) || {};
  const { referralProgCode } = useSelector((state: any) => state?.User) || {};
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { referralCode, applyTimes } = referralProgCode || {};
  const { profileNote, enableReferral } = referralProg || {};
  let itemClass = 'tabs-panel';
  if (tabId === ID.ACCOUNT_BILLING) {
    itemClass += ' active';
  }
  useEffect(() => {
    if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    clickTimerRef.current = createTimeout(() => {
      dispatch(getDataTriggerPoint({ type: TYPE_TRIGGER_ALWAYS.PROFILE }));
    }, 400);
    return () => {
      if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    };
  }, []);
  return (
    <div className={itemClass}>
      {!isGlobal && !isEmpty(dataTriggerProfile) && !currentProfile?.isKid && (
        <TriggerTouchPoint
          image={dataTriggerProfile?.image}
          imageMobile={dataTriggerProfile?.image}
          url={dataTriggerProfile?.navigateUrl}
          positionTrigger={POSITION_TRIGGER.PROFILE}
          className={!isMobile && 'size-mw-50per'}
        />
      )}
      <AccountInfo
        isKid={isKid}
        isGlobal={isGlobal}
        profile={profile}
        onHandleAction={onHandleAction}
      />
      {!isKid && (
        <AccountInvoice
          isKid={isKid}
          isGlobal={isGlobal}
          profile={profile}
          onHandleAction={onHandleAction}
        />
      )}
      {!isKid && <SocialLinked isKid={isKid} profile={profile} onHandleAction={onHandleAction} />}
      {!isKid && <ManagementUser />}
      {!isEmpty(referralCode) && enableReferral && (
        <ReferralProgProfile
          referralCode={referralCode}
          applyTimes={applyTimes}
          profileNote={profileNote}
          enableReferral={enableReferral}
        />
      )}
      <Setting
        profile={profile}
        isKid={isKid}
        onHandleAction={onHandleAction}
        updateAllowPush={updateAllowPush}
      />
    </div>
  );
});

export default AccountBilling;

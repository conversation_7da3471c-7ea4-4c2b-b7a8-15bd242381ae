import React, { useRef } from 'react';
import { ID } from '@constants/constants';
import EmptyWatchLater from '@components/empty/EmptyWatchLater';
import Card from '@components/basic/Card/Card';
import CardList from '@components/basic/Card/CardList';

const ProfileFavourite = (props: any) => {
  const { tabId, profile, favoriteData, onScrollDown } = props;
  const favoriteDataLength = favoriteData?.items?.length || 0;
  let itemClass = 'tabs-panel';
  if (tabId === ID.FAVORITE) {
    itemClass += ' active';
  }
  const items = favoriteData?.items || [];

  const ref = useRef([]);
  const renderCardList = () =>
    (items || []).map((item: any, i: any) => (
      <Card
        randomID={`${item?.id}_${i}`}
        ref={ref}
        index={i + 1}
        cardData={item}
        profile={profile}
        lazyImg
        className="item item-ribbon item-ribbon-poster"
        key={item.id + i || i}
      />
    ));

  return (
    <div className={itemClass}>
      <div className="rocopa">
        <div className="rocopa__body p-t4">
          {favoriteDataLength > 0 && (
            <CardList
              renderContent={renderCardList}
              heightCheckScroll={320}
              onScrollDown={onScrollDown}
            />
          )}
          {favoriteDataLength === 0 && <EmptyWatchLater />}
        </div>
      </div>
    </div>
  );
};

export default ProfileFavourite;

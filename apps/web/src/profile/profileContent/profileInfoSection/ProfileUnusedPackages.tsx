import React from 'react';

const ProfileUnusedPackages = (props: any) => {
  const { title, description, renderContent, action } = props || {};
  return (
    <div className="grid-x">
      <div className="cell medium-2">
        <div className="block__header">
          <h4 className="block__title title-white">
            {title || ''}
            <span className="icon icon--tiny">
              <i className="vie vie-info-o-c-script" />
            </span>
          </h4>
          <p className="text">{description}</p>
        </div>
      </div>
      <div className="cell medium-auto">
        <div className="block__body">
          <div className="grid-x grid-margin-x grid-margin-y">
            {renderContent && renderContent()}
          </div>
        </div>
      </div>
      {action && (
        <div className="cell shrink">
          <button
            className="button link button--action"
            data-open="userProfileUpdateBirthdayModal"
            onClick={action?.func || (() => {})}
          >
            {action?.name}
          </button>
        </div>
      )}
    </div>
  );
};
export default ProfileUnusedPackages;

import React, { useState } from 'react';
import Toggle from '@/components/ToggleCheckbox';
import PaymentApi from '@apis/Payment';
import PopupCancelRecurring from '@components/popup/PopupCancelRecurring';
import { useDispatch } from 'react-redux';
import { setToast } from '@actions/app';
import { getPurchased } from '@actions/user';
import { PAYMENT_METHOD, SERVICE_NAME } from '@constants/constants';
import { TEXT } from '@constants/text';
import TrackingPayment from '@/tracking/functions/payment';

const trackingPayment = new TrackingPayment();

const ProfilePaymentCardItem = (props: any) => {
  const dispatch = useDispatch();
  const {
    label,
    expiredDate,
    recurring,
    nextRecurringDate,
    description,
    payment_method,
    service_name,
    userStatusRecurring
  } = props || {};
  const [updateParams, setParams] = useState<any>({});
  const [isAutoRenewal, setRenewal] = useState(props?.user_status_recurring === 1);
  const [openPopup, setOpenPopup] = useState(false);

  const onRenewal = (checked: any) => {
    const params: any = { sub_id: props?.sub_id, recurring: checked ? 1 : 0 };
    setParams(params);
    if (checked) {
      updateRecurring(params);
    } else {
      setOpenPopup(true);
    }
    trackingPayment.recurringButtonSelected({
      packageId: props?.sub_id,
      packageName: label,
      isRecurring: checked
    });
  };

  const updateRecurring = (params?: any) => {
    const temp = params || updateParams;
    if (temp) {
      PaymentApi.setRecurring(temp).then((res) => {
        dispatch(getPurchased());
        setOpenPopup(false);
        const success = res?.success;
        let message = TEXT.MSG_ERROR;
        if (success) {
          message = TEXT.UPDATE_SUCCESS;
          setRenewal(temp?.recurring === 1);
        }
        dispatch(setToast({ message }));
      });
    }
  };

  const handleTrackingCancelRecurring = () => {
    const temp = updateParams;
    trackingPayment.cancelRecurringButtonSelected({
      packageId: temp?.sub_id,
      packageName: label
    });
  };

  const handleButtonBack = () => {
    trackingPayment.backButtonSelected({
      packageId: props?.sub_id,
      packageName: label
    });
    setOpenPopup(false);
  };

  const handleAcceptCancelRecurring = () => {
    updateRecurring();
    handleTrackingCancelRecurring();
  };

  const expiryContent =
    isAutoRenewal && recurring ? 'Đến khi bạn hủy gia hạn' : nextRecurringDate || expiredDate;
  let expiredDateLabel = `Hạn sử dụng: ${expiryContent}`;

  if (recurring === 1 && userStatusRecurring === 0) {
    expiredDateLabel = `Hạn sử dụng: ${expiredDate}`;
  }
  return (
    <div className="cell medium-shrink">
      <div className="card card--package-history using md:pb-4 pb-3 !h-full">
        <h4 className="card__title title-white">{label || ''}</h4>
        {(payment_method === PAYMENT_METHOD.IAP ||
          (service_name || '').toLowerCase() === SERVICE_NAME.MOMO) &&
          description && (
            <div className="auto-payment">
              <span className="text-white">{description}</span>
            </div>
          )}
        <div className="card__section">
          <p className="text text-white">{expiredDateLabel || ''}</p>
          {isAutoRenewal && recurring === 1 && (
            <p className="text text-white">
              {`Kỳ thanh toán tiếp theo: ${nextRecurringDate || expiredDate}`}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {payment_method !== PAYMENT_METHOD.IAP &&
            (service_name || '').toLowerCase() !== SERVICE_NAME.MOMO &&
            recurring === 1 && (
              <>
                <div className="text-white">{TEXT.AUTO_RENEWAL}</div>
                <Toggle
                  title={TEXT.AUTO_RENEWAL}
                  ariaLabel={TEXT.AUTO_RENEWAL}
                  checked={isAutoRenewal}
                  onChange={onRenewal}
                />
              </>
            )}
        </div>
      </div>
      {openPopup && (
        <PopupCancelRecurring
          closePopup={() => {
            handleButtonBack();
          }}
          onAccept={handleAcceptCancelRecurring}
        />
      )}
    </div>
  );
};
export default ProfilePaymentCardItem;

import React from 'react';
import ConfigGTM from '@config/ConfigGTM';
import LocalStorage from '@config/LocalStorage';
import { GTM_ID, ENABLE_SDK_GTM } from '@config/ConfigEnv';
import TrackingApp from './functions/TrackingApp';

declare const window: any;

export const GTMVieONScript = () => {
  if (!GTM_ID || !ENABLE_SDK_GTM || ENABLE_SDK_GTM === 'false') return;
  return (
    <>
      <script
        id="GTMVieONScript"
        dangerouslySetInnerHTML={{
          __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','${GTM_ID}');`
        }}
      />
      {GTMVieONNoScript()}
    </>
  );
};

export const GTMVieONNoScript = () => {
  if (!GTM_ID && (!ENABLE_SDK_GTM || ENABLE_SDK_GTM === 'false')) return null;
  return (
    <noscript>
      <iframe
        src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}`}
        height="0"
        width="0"
        style={{
          display: 'none',
          visibility: 'hidden'
        }}
        title="\"
      />
    </noscript>
  );
};

export const handleGTMEvent = ({ eventName, transactionStatus, params, query }: any) => {
  let utmParams: any = window.localStorage.getItem(LocalStorage.UTM_PARAMS);
  utmParams = utmParams ? JSON.parse(utmParams) : query;
  const eventCategory = eventName;
  const eventSource = utmParams?.utm_source || ConfigGTM.ORGANIC;
  const eventMedium = utmParams?.utm_medium || ConfigGTM.ORGANIC;
  let eventLabel = '';
  let eventAction = '';

  const eventParams = {
    [ConfigGTM.EVENT]: eventName,
    [ConfigGTM.EVENT_CATEGORY]: eventCategory
  };

  eventParams[ConfigGTM.EVENT_SOURCE] = eventSource;
  eventParams[ConfigGTM.EVENT_MEDIUM] = eventMedium;

  switch (eventName) {
    case ConfigGTM.PAYMENT_FORM:
      eventAction = ConfigGTM.CHECK_OUT;
      break;
    case ConfigGTM.PAYMENT_SUCCESSFUL:
      eventAction = transactionStatus;
      break;
    case ConfigGTM.PAYMENT_RESULT:
      eventAction = ConfigGTM.CONFIRM_ACTION;
      eventLabel = ConfigGTM.PAYMENT_TRANSACTION;
      break;
    default:
      break;
  }
  eventParams[ConfigGTM.EVENT_LABEL] = eventLabel || '';
  eventParams[ConfigGTM.EVENT_ACTION] = eventAction || '';
  eventParams[ConfigGTM.PARAMETERS] = params || {};

  gtmShootEvent(eventParams);
};

export const shootLoadDeepLink = ({ token, router }: any) => {
  const { utm_source, utm_medium, utm_campaign, utm_content } = router?.query || {};
  if (utm_source || utm_medium || utm_campaign || utm_content) {
    if (!token) return;
    const metadata = parseJwt(token);
    const { sub, di, dt } = metadata || {};
    const userIdString = `${sub || ''}&&${di || ''}&&${dt || ''}`;
    gtmShootEvent({
      [ConfigGTM.EVENT]: ConfigGTM.LOAD_DEEP_LINK,
      [ConfigGTM.USER_ID]: userIdString
    });
    TrackingApp.loadDeepLink({});
  }
};

export function parseJwt(token: any) {
  const base64Url = (token || '').split('.')[1];
  if (!base64Url) return null;
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const jsonPayload = decodeURIComponent(
    atob(base64)
      .split('')
      .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
      .join('')
  );

  return JSON.parse(jsonPayload);
}

export const gtmShootEvent = (eventParams: any) => {
  if (typeof window === 'undefined') return;
  if (!window?.dataLayer) window.dataLayer = [];
  window.dataLayer.push(eventParams);
};

export const gtmSignIn = ({ profile, query }: any) => {
  const utmCampaign = query?.utm_campaign;
  if (utmCampaign !== 'sandbox' || !profile?.id) return;

  const eventParams = {
    event: ConfigGTM.SIGN_IN,
    user_id: profile?.id,
    username: profile?.givenName,
    utm_campaign: utmCampaign
  };
  gtmShootEvent(eventParams);
};

export const gtmSignUp = ({ profile, query }: any) => {
  const utmCampaign = query?.utm_campaign;
  if (utmCampaign !== 'sandbox' || !profile?.id) return;

  const eventParams = {
    event: ConfigGTM.SIGN_UP,
    user_id: profile?.id,
    username: profile?.givenName,
    utm_campaign: utmCampaign
  };
  gtmShootEvent(eventParams);
};

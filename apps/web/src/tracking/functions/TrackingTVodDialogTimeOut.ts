import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '@tracking/TrackingSegment';

const dialogTimeOutSaleLoaded = ({ flowName }: any) => {
  segmentEvent(NAME.POPUP.DIALOG_TIME_OUT_SALE_LOADED, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};

const dialogTimeOutSaleHomePage = ({ flowName }: any) => {
  segmentEvent(NAME.POPUP.DIALOG_TIME_OUT_SALE_HOMEPAGE, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};

const dialogTimeOutSaleSelect = ({ flowName }: any) => {
  segmentEvent(NAME.POPUP.DIALOG_TIME_OUT_SALE_SELECT, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};

export { dialogTimeOutSaleLoaded, dialogTimeOutSaleHomePage, dialogTimeOutSaleSelect };

import { NAME, PROPERTY } from '@/config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

const fastTrackEpisodeSelected = (data: any) => {
  if (!data) return null;
  return segmentEvent(NAME.FAST_TRACK_EPISODE_SELECTED, {
    [PROPERTY.CONTENT_ID]: data?.id || '',
    [PROPERTY.CONTENT_TITLE]: data?.title || ''
  });
};
const fastTrackRegisterAtVideoDetail = (data: any) => {
  if (!data) return null;
  /*
    Nếu ở video detail:
      -content_id là id của series
      -content_title là tên của series
    Nếu ở danh sách tập:
      -content_id là id của tập
      -content_title là tên của tập
    */
  return segmentEvent(NAME.FAST_TRACK_REGISTER_AT_VIDEO_DETAIL, {
    [PROPERTY.CONTENT_ID]: data?.id || '',
    [PROPERTY.CONTENT_TITLE]: data?.title || ''
  });
};
const fastTrackRegisterAtDialog = (data: any) => {
  if (!data) return null;
  /*
    Nếu ở video detail:
      -content_id là id của series
      -content_title là tên của series
    Nếu ở danh sách tập:
      -content_id là id của tập
      -content_title là tên của tập
    */
  return segmentEvent(NAME.FAST_TRACK_REGISTER_AT_DIALOG, {
    [PROPERTY.CONTENT_ID]: data?.id || '',
    [PROPERTY.CONTENT_TITLE]: data?.title || ''
  });
};
const fastTrackRegisterAtEndScreen = (data: any) => {
  if (!data) return null;
  /*
    Nếu ở video detail:
      -content_id là id của series
      -content_title là tên của series
    Nếu ở danh sách tập:
      -content_id là id của tập
      -content_title là tên của tập
    */
  return segmentEvent(NAME.FAST_TRACK_REGISTER_AT_END_SCREEN, {
    [PROPERTY.CONTENT_ID]: data?.id || '',
    [PROPERTY.CONTENT_TITLE]: data?.title || ''
  });
};

export {
  fastTrackEpisodeSelected,
  fastTrackRegisterAtVideoDetail,
  fastTrackRegisterAtDialog,
  fastTrackRegisterAtEndScreen
};

import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

const loginBindAccountPhone = ({ isBanner, userType }: any) => {
  const flowName = isBanner
    ? VALUE.FLOW_NAME_BIND_ACCOUNT_BANNER
    : VALUE.FLOW_NAME_BIND_ACCOUNT_LOGIN;
  segmentEvent(NAME.LOGIN_BIND_ACCOUNT_PHONE, {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.POPUP_NAME]: VALUE.POPUP_NAME.INPUT_PHONE,
    [PROPERTY.USER_TYPE]: userType
  });
};
const loginBindAccountOtp = ({ isBanner, userType }: any) => {
  const flowName = isBanner
    ? VALUE.FLOW_NAME_BIND_ACCOUNT_BANNER
    : VALUE.FLOW_NAME_BIND_ACCOUNT_LOGIN;
  segmentEvent(NAME.LOGIN_BIND_ACCOUNT_OTP, {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.POPUP_NAME]: VALUE.POPUP_NAME.INPUT_OTP,
    [PROPERTY.USER_TYPE]: userType
  });
};
const loginBindAccountPass = ({ isBanner, userType }: any) => {
  const flowName = isBanner
    ? VALUE.FLOW_NAME_BIND_ACCOUNT_BANNER
    : VALUE.FLOW_NAME_BIND_ACCOUNT_LOGIN;
  segmentEvent(NAME.LOGIN_BIND_ACCOUNT_PASS, {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.POPUP_NAME]: VALUE.POPUP_NAME.INPUT_PASS,
    [PROPERTY.USER_TYPE]: userType
  });
};
const confirmationButtonSelected = ({ isBanner, popupName }: any) => {
  const flowName = isBanner
    ? VALUE.FLOW_NAME_BIND_ACCOUNT_BANNER
    : VALUE.FLOW_NAME_BIND_ACCOUNT_LOGIN;
  segmentEvent(NAME.CONFIRMATION_BUTTON_SELECTED, {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.POPUP_NAME]: popupName
  });
};

const resendOtpButtonSelected = ({ isBanner }: any) => {
  const flowName = isBanner
    ? VALUE.FLOW_NAME_BIND_ACCOUNT_BANNER
    : VALUE.FLOW_NAME_BIND_ACCOUNT_LOGIN;
  segmentEvent(NAME.RESEND_OTP_BUTTON_SELECTED, {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.POPUP_NAME]: VALUE.POPUP_NAME.INPUT_OTP
  });
};
const skipButtonSelected = ({ isBanner, popupName }: any) => {
  const flowName = isBanner
    ? VALUE.FLOW_NAME_BIND_ACCOUNT_BANNER
    : VALUE.FLOW_NAME_BIND_ACCOUNT_LOGIN;
  segmentEvent(NAME.SKIP_BUTTON_SELECTED, {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.POPUP_NAME]: popupName
  });
};
const loginBindAccountSuccessful = ({ isBanner }: any) => {
  const flowName = isBanner
    ? VALUE.FLOW_NAME_BIND_ACCOUNT_BANNER
    : VALUE.FLOW_NAME_BIND_ACCOUNT_LOGIN;
  segmentEvent(NAME.LOGIN_BIND_ACCOUNT_SUCCESSFUL, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const bindAccountBanner = ({ buttonStatus }: any) => {
  segmentEvent(NAME.BIND_ACCOUNT_BANNER, {
    [PROPERTY.FLOW_NAME]: VALUE.FLOW_NAME_BIND_ACCOUNT_BANNER,
    [PROPERTY.BANNER_ID]: VALUE.BIND_ACCOUNT_BANNER,
    [PROPERTY.BUTTON_STATUS]: buttonStatus || ''
  });
};

export {
  loginBindAccountPhone,
  loginBindAccountOtp,
  loginBindAccountPass,
  confirmationButtonSelected,
  resendOtpButtonSelected,
  skipButtonSelected,
  loginBindAccountSuccessful,
  bindAccountBanner
};

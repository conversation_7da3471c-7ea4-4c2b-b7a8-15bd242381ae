import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import { CONTENT_TYPE } from '@constants/constants';
import { segmentEvent } from '../TrackingSegment';

export const segmentRegistrationEventsPopupAuth = ({
  contentType,
  contentId,
  contentTitle,
  triggerFrom,
  is30SecondsWatchTrial,
  cancel,
  flowNameCustom
}: any) => {
  let flowName = '';
  let triggerFormCustom = VALUE.FORCE_LOGIN_NOTIFICATION;

  if (contentType === CONTENT_TYPE.LIVE_TV) {
    flowName = VALUE.REGISTRATION_FOR_LIVETV;
  } else if (contentType === CONTENT_TYPE.LIVESTREAM || contentType === 2) {
    flowName = VALUE.REGISTRATION_FOR_LIVESTREAM;
  } else if (is30SecondsWatchTrial) {
    triggerFormCustom = VALUE.FORCE_LOGIN_BUTTON;
    flowName = VALUE.REGISTRATION_TRIAL;
  } else {
    flowName = VALUE.REGISTRATION_FOR_VOD;
  }

  const params: any = !cancel
    ? {
        [PROPERTY.FLOW_NAME]: flowNameCustom || flowName,
        [PROPERTY.TRIGGER_FROM]: triggerFrom || triggerFormCustom,
        [PROPERTY.CONTENT_ID]: contentId || null,
        [PROPERTY.CONTENT_TITLE]: contentTitle || null,
        [PROPERTY.CONTENT_TYPE]: contentType || null
      }
    : {
        [PROPERTY.FLOW_NAME]: flowName
      };

  let eventName = VALUE.REGISTRATION_TRIGGER_LOADED;
  if (cancel) eventName = NAME.SIGN_UP_CANCEL;
  segmentEvent(eventName, params);
};

export const segmentLoginPopupLoaded = ({ content }: any) => {
  segmentEvent(NAME.REGISTRATION_TRIGGER.LOGIN_POPUP_LOADED, {
    [PROPERTY.FLOW_NAME]: VALUE.FORCE_LOGIN,
    [PROPERTY.CONTENT_ID]: content?.id || null,
    [PROPERTY.CONTENT_TITLE]: content?.title || null,
    [PROPERTY.CONTENT_TYPE]: content?.type
  });
};

export const segmentLoginPopupClosed = ({ content }: any) => {
  segmentEvent(NAME.REGISTRATION_TRIGGER.LOGIN_POPUP_CLOSE_BUTTON_SELECTED, {
    [PROPERTY.FLOW_NAME]: VALUE.FORCE_LOGIN,
    [PROPERTY.CONTENT_ID]: content?.id || null,
    [PROPERTY.CONTENT_TITLE]: content?.title || null,
    [PROPERTY.CONTENT_TYPE]: content?.type
  });
};

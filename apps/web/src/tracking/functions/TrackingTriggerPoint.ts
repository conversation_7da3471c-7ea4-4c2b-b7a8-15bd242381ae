import { NAME, PROPERTY } from '@config/ConfigSegment';
import { POSITION_TRIGGER } from '@constants/constants';
import { segmentEvent } from '../TrackingSegment';

const alwaysOnTriggerSelected = (data: any) => {
  const { positionTrigger, content } = data || {};
  segmentEvent(NAME.ALWAYS_ON_TRIGGER_SELECTED, {
    [PROPERTY.BANNER_NAME]: null,
    [PROPERTY.BANNER_POSITION]: positionTrigger,
    [PROPERTY.CONTENT_ID]: positionTrigger === POSITION_TRIGGER.VIDEO_INTRO ? content?.id : null,
    [PROPERTY.CONTENT_NAME]:
      positionTrigger === POSITION_TRIGGER.VIDEO_INTRO ? content?.title : null,
    [PROPERTY.RIBBON_ID]: null,
    [PROPERTY.RIBBON_NAME]: null,
    [PROPERTY.RIBBON_ORDER]: null
  });
};
const alwaysOnTriggerClosed = (data: any) => {
  const { positionTrigger, content } = data;
  segmentEvent(NAME.ALWAYS_ON_TRIGGER_CLOSED, {
    [PROPERTY.BANNER_ID]: null,
    [PROPERTY.BANNER_NAME]: null,
    [PROPERTY.BANNER_POSITION]: positionTrigger,
    [PROPERTY.CONTENT_ID]: positionTrigger === POSITION_TRIGGER.VIDEO_INTRO ? content?.id : null,
    [PROPERTY.CONTENT_NAME]:
      positionTrigger === POSITION_TRIGGER.VIDEO_INTRO ? content?.title : null,
    [PROPERTY.RIBBON_ID]: null,
    [PROPERTY.RIBBON_NAME]: null,
    [PROPERTY.RIBBON_ORDER]: null
  });
};

const alwaysOnTriggerLoaded = (data: any) => {
  const { positionTrigger, content } = data || {};
  segmentEvent(NAME.ALWAYS_ON_TRIGGER_LOADED, {
    [PROPERTY.BANNER_NAME]: null,
    [PROPERTY.BANNER_POSITION]: positionTrigger,
    [PROPERTY.CONTENT_ID]: content?.id || null,
    [PROPERTY.CONTENT_NAME]: content?.title || null,
    [PROPERTY.RIBBON_ID]: null,
    [PROPERTY.RIBBON_NAME]: null,
    [PROPERTY.RIBBON_ORDER]: null
  });
};

export { alwaysOnTriggerSelected, alwaysOnTriggerClosed, alwaysOnTriggerLoaded };

import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

const dialogRestoreAccountLoaded = ({ flowName }: any) => {
  segmentEvent(NAME.DIALOG_RESTORE_ACCOUNT_LOADED, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const dialogRestoreAccountClose = ({ flowName }: any) => {
  segmentEvent(NAME.DIALOG_RESTORE_ACCOUNT_CLOSE, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const dialogRestoreAccountAccept = ({ flowName }: any) => {
  segmentEvent(NAME.DIALOG_RESTORE_ACCOUNT_ACCEPT, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const dialogRestoreAccountAnother = ({ flowName }: any) => {
  segmentEvent(NAME.DIALOG_RESTORE_ACCOUNT_ANOTHER, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const dialogRestoreAccountSuccess = ({ flowName }: any) => {
  segmentEvent(NAME.DIALOG_RESTORE_ACCOUNT_SUCCESS, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const lockedAccountDialogLoaded = () => {
  segmentEvent(NAME.LOCKED_ACCOUNT_DIALOG_LOADED);
};
const lockedAccountAccept = () => {
  segmentEvent(NAME.LOCKED_ACCOUNT_ACCEPT, {}, true);
};

export {
  dialogRestoreAccountLoaded,
  dialogRestoreAccountClose,
  dialogRestoreAccountAccept,
  dialogRestoreAccountAnother,
  dialogRestoreAccountSuccess,
  lockedAccountAccept,
  lockedAccountDialogLoaded
};

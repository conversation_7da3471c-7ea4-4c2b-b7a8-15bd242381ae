import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import { LAYOUT_MENU, PAGE, SENTRY, TVOD } from '@constants/constants';
import { sentryCaptureEvent } from '@tracking/sentry';
import { handleGTMEvent } from '@tracking/TrackingGTM';
import { dmpEvent } from '@tracking/TrackingDMP';
import { DMP_PROPERTY } from '@config/ConfigDMP';
import ConfigCookie from '@config/ConfigCookie';
import { parseVODType, segmentEvent, segmentPageView } from '../TrackingSegment';

export const pageView = (seoData: any) => {
  segmentPageView(seoData);
};

export const login = ({ loginMethod, flowName, query }: any) => {
  // Segment Event
  segmentEvent(NAME.LOGIN, {
    [PROPERTY.LOGIN_METHOD]: loginMethod || '',
    [PROPERTY.FLOW_NAME]: flowName || ''
  });

  // GTM Event
  handleGTMEvent({
    eventName: NAME.LOGIN,
    params: {
      [PROPERTY.LOGIN_METHOD]: loginMethod || ''
    },
    query
  });
};

export const signUp = ({ query }: any) => {
  const eventName = NAME.SIGN_UP;
  // Segment Event
  segmentEvent(eventName);

  // GTM Event
  handleGTMEvent({
    eventName,
    params: {
      [DMP_PROPERTY.SIGN_UP_METHOD]: VALUE.PHONE
    },
    query
  });
};

export const signUpSuccessfully = ({ registrationTrigger, pathname, query }: any) => {
  const eventName = NAME.SIGN_UP_SUCCESSFULLY;
  let flowName = registrationTrigger
    ? registrationTrigger?.textButtonComment
      ? VALUE.FLOW_NAME_TEXT_BUTTON_COMMENT
      : registrationTrigger?.recommendation
      ? VALUE.FLOW_NAME_REGISTER_RECOMMENDATION
      : VALUE.FLOW_NAME_BANNER_COMMENT
    : '';
  if (pathname === PAGE.RENTAL_CONTENT) {
    flowName = TVOD.TVOD_TYPE;
  }

  // Segment Event
  segmentEvent(eventName, { [PROPERTY.FLOW_NAME]: flowName }, true);

  // GTM Event
  handleGTMEvent({
    eventName,
    query
  });

  // DMP Event
  dmpEvent(eventName);
};

export const webVisited = ({
  loginMethod,
  isAuto,
  flowName,
  query,
  userId,
  mobile,
  email
}: any) => {
  const fullUrl = window.location.href;
  const codeMatch = fullUrl.match(/code=([^&]*)/);
  const code = codeMatch ? codeMatch[1] : null;
  handleGTMEvent({
    eventName: NAME.WEB_VISITED,
    params: {
      [PROPERTY.USER_ID]: userId || '',
      [PROPERTY.MOBILE]: mobile || '',
      [PROPERTY.EMAIL]: email || '',
      [PROPERTY.LOGIN_METHOD]: loginMethod || '',
      [PROPERTY.IS_AUTO]: !!isAuto
    },
    query
  });
  segmentEvent(NAME.WEB_VISITED, {
    [PROPERTY.CURRENT_PAGE]: window.location.href,
    [PROPERTY.CODE]: code,
    [PROPERTY.FLOW_NAME]: code ? 'tv_registration' : flowName || ''
  });
  segmentEvent(NAME.WEB_VISITED_SEO, {
    [PROPERTY.SEARCH_ENGINE]: document.referrer
  });
};
export const loadDeepLink = (params: any) => {
  segmentEvent(NAME.LOAD_DEEP_LINK, {
    [PROPERTY.CURRENT_PAGE]: window.location.href,
    ...params
  });
};

export const contentSelected = ({
  data,
  masterBannerData,
  clickType,
  playNow,
  isLiveTv,
  category,
  flowName
}: any) => {
  const { menuId, menuName, menuOrder, subMenuId, subMenuName, subMenuOrder } = getMenuInfo() || {};
  let ribbonName = data?.ribbonName || '';
  let ribbonId = data?.ribbonId || '';
  let contentPlayType = 'svod';
  let contentSelectedButton = null;
  if (data?.isMasterBanner) {
    ribbonId = VALUE.MASTER_BANNER.ID;
    ribbonName = VALUE.MASTER_BANNER.NAME;
  }
  if (data?.isPremiumTVod) {
    contentPlayType = 'tvod';
    contentSelectedButton = data?.tvod?.benefitType;
  } else if (data?.isVip) contentPlayType = 'avod';

  const ribbonOrder = data?.isMasterBanner
    ? 0
    : masterBannerData
    ? (data?.ribbonOrder || 0) + 1
    : data?.ribbonOrder || 0;

  const properties = {
    [PROPERTY.CURRENT_PAGE]: window.location.href,
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_NAME]: data?.title || '',
    [PROPERTY.CONTENT_TYPE]: parseVODType({ content_type: data?.type }),
    [PROPERTY.CONTENT_ORDER]: data?.index || 0,
    [PROPERTY.CONTENT_SELECT_BUTTON]: contentSelectedButton,
    [PROPERTY.CONTENT_PLAY_TYPE]: contentPlayType || '',
    [PROPERTY.RIBBON_ID]: ribbonId,
    [PROPERTY.RIBBON_ORDER]: ribbonOrder,
    [PROPERTY.RIBBON_NAME]: ribbonName,
    [PROPERTY.CATEGORY_NAME]: isLiveTv ? category?.name : data?.categoryText || '',
    [PROPERTY.CLICK_TYPE]: clickType || '',
    [PROPERTY.GENRE]: data?.genreText || '',
    [PROPERTY.PLAY_NOW]: !!playNow,
    [PROPERTY.MENU_ID]: menuId,
    [PROPERTY.MENU_NAME]: menuName || data?.menuNameProfile,
    [PROPERTY.MENU_ORDER]: menuOrder,
    [PROPERTY.SUB_MENU_ID]: isLiveTv ? category?.idTracking || '' : subMenuId,
    [PROPERTY.SUB_MENU_NAME]: isLiveTv ? category?.name || '' : subMenuName,
    [PROPERTY.SUB_MENU_ORDER]: isLiveTv ? category?.order || 0 : subMenuOrder,
    [PROPERTY.FLOW_NAME]: flowName || '',
    [PROPERTY.IS_INDEXING]: data?.hasObjectDetection,
    [PROPERTY.IS_BLOCK_VIP]: data?.trialDuration
      ? false
      : data?.isPremium ||
        data?.isPremiumPVod ||
        data?.isPremiumPVodHaveSVod ||
        data?.isPremiumPVodNotSVod ||
        data?.isPremiumTVod ||
        data?.isSearchPage ||
        data?.isSvodTvod,

    // Only for MoEngage: start with "ext_" prefix
    [PROPERTY.VIDEO_GENRE]: data?.seasonGenre || '',
    [PROPERTY.SHORT_DESCRIPTION]: data?.shortDescription || '',
    [PROPERTY.THUMBNAIL]: data?.seasonThumb || ''
  };

  /**
   * Filter Extend params for MoEngage with "ext_" prefix
   * @type {{}}
   */
  const extendMoeParams = Object.keys(properties).reduce((acc: any, key: any) => {
    if (key.startsWith('ext_')) {
      const newKey = key.replace('ext_', '');
      acc[newKey] = properties[key];
    }
    return acc;
  }, {});

  /**
   * Remove params with "ext_" prefix for Segment
   */
  Object.keys(properties).forEach((key: any) => {
    if (key.startsWith('ext_')) {
      delete properties[key];
    }
  });

  // Segment Event
  segmentEvent(NAME.CONTENT_SELECT, properties, true, null, extendMoeParams);
};

export const bannerSelected = ({ data, index }: any) => {
  const { menuId, menuName, menuOrder, subMenuId, subMenuName, subMenuOrder } = getMenuInfo() || {};

  const properties = {
    [PROPERTY.CURRENT_PAGE]: window?.location?.href || '',
    [PROPERTY.BANNER_ID]: data?.id || '',
    [PROPERTY.BANNER_NAME]: data?.title || '',
    [PROPERTY.BANNER_ORDER]: data?.ribbonOrder || 0,
    [PROPERTY.BANNER_ORDER_IN_CAROUSEL]: index || 0,
    [PROPERTY.MENU_ID]: menuId,
    [PROPERTY.MENU_NAME]: menuName,
    [PROPERTY.MENU_ORDER]: menuOrder,
    [PROPERTY.SUB_MENU_ID]: subMenuId,
    [PROPERTY.SUB_MENU_NAME]: subMenuName,
    [PROPERTY.SUB_MENU_ORDER]: subMenuOrder
  };
  segmentEvent(NAME.BANNER_SELECTED, properties);
};

export const categorySelected = ({ data, isLiveTv, isCategoryProfile }: any) => {
  const isViewMoreMenu = data?.isViewMoreMenu;
  const { menuId, menuName, menuOrder, subMenuId, subMenuName, subMenuOrder } =
    getMenuInfo({ isViewMoreMenu }) || {};
  const properties = {
    [PROPERTY.CATEGORY_NAME]: isCategoryProfile ? '' : data?.name,
    [PROPERTY.MENU_ID]: menuId || '',
    [PROPERTY.MENU_NAME]: isCategoryProfile ? VALUE.PROFILE_PAGE : menuName,
    [PROPERTY.MENU_ORDER]: menuOrder || '',
    [PROPERTY.SUB_MENU_ID]: isLiveTv ? data?.idTracking : data?.id || subMenuId || '',
    [PROPERTY.SUB_MENU_NAME]: data?.name || subMenuName || '',
    [PROPERTY.SUB_MENU_ORDER]:
      (data ? data?.order : subMenuOrder || 0) + (isViewMoreMenu ? 1 : 0) || 0
  };
  segmentEvent(NAME.CATEGORY_SELECTED, properties);
};

export const menuSelected = (menuItem: any) => {
  segmentEvent(NAME.MENU.MENU_SELECTED, {
    [PROPERTY.MENU_ID]: menuItem?.id || '',
    [PROPERTY.MENU_NAME]: menuItem?.name || '',
    [PROPERTY.MENU_ORDER]: menuItem?.order || 0,
    [PROPERTY.MENU_SLUG]: menuItem?.slug || ''
  });
};

export const ribbonSelected = ({ data, title, ribbonOrder, isLiveTv, category }: any) => {
  const { menuId, menuName, menuOrder, subMenuId, subMenuName, subMenuOrder } = getMenuInfo() || {};
  const properties = {
    [PROPERTY.CURRENT_PAGE]: window.location.href,
    [PROPERTY.RIBBON_NAME]: title || '',
    [PROPERTY.RIBBON_ORDER]: ribbonOrder || 0,
    [PROPERTY.RIBBON_ID]: data?.id || '',
    [PROPERTY.MENU_ID]: menuId,
    [PROPERTY.MENU_NAME]: menuName,
    [PROPERTY.MENU_ORDER]: menuOrder,
    [PROPERTY.SUB_MENU_ID]: isLiveTv ? category?.idTracking : subMenuId,
    [PROPERTY.SUB_MENU_NAME]: isLiveTv ? category?.name : subMenuName,
    [PROPERTY.SUB_MENU_ORDER]: isLiveTv ? category?.order : subMenuOrder
  };
  segmentEvent(NAME.RIBBON_SELECT, properties);
};

export const getMenuInfo = (props?: any) => {
  const { isViewMoreMenu } = props || {};
  const store = window.__NEXT_REDUX_STORE__;
  const state = store.getState();
  const { activeMenu, activeSubMenu } = state?.Menu || {};
  const viewMoreMenu = (state?.Menu?.menuList || []).find(
    (item: any) => item?.layoutType === LAYOUT_MENU.MORE
  );

  const menuId = isViewMoreMenu ? viewMoreMenu?.id : activeMenu?.id;
  const menuName = isViewMoreMenu ? viewMoreMenu?.name : activeMenu?.name;
  const menuOrder = isViewMoreMenu ? viewMoreMenu?.order : activeMenu?.order;

  const subMenuId = activeSubMenu?.order === -1 ? '' : activeSubMenu?.id || '';
  const subMenuName = activeSubMenu?.order === -1 ? '' : activeSubMenu?.name || '';
  const subMenuOrder = activeSubMenu?.order === -1 ? null : activeSubMenu?.order || 0;
  return {
    menuId,
    menuName,
    menuOrder,
    subMenuId,
    subMenuName,
    subMenuOrder
  };
};

export const apiError = (response: any) => {
  const data = response?.data || {};
  const token = response?.config?.headers?.Authorization || '';
  const errorCode = response?.httpCode;
  const errorType = SENTRY.API;
  const errorMessage = response?.message || response?.data?.message || '';
  const message = `[API]`;
  const tags = {
    errorType,
    errorCode
  };
  const extra = {
    errorCode,
    errorMessage,
    data,
    token
  };
  sentryCaptureEvent({
    extra,
    message,
    tags
  });
};

export const getProfileError = (data: any) => {
  const { httpCode, token } = data || {};
  const errorType = SENTRY.PROFILE;
  const message = `[API] ${SENTRY.PROFILE}`;
  const tags = {
    errorType,
    errorCode: httpCode
  };
  const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
  const extra = {
    errorCode: httpCode,
    data,
    token: token || accessToken
  };
  sentryCaptureEvent({
    extra,
    message,
    tags
  });
};

export const createTransactionError = (data: any) => {
  const { httpCode, token, payMethod, errorCode } = data || {};
  const errorType = SENTRY.CREATE_TRANSACTION;
  const message = `[API] ${SENTRY.CREATE_TRANSACTION}`;
  const tags = {
    errorType,
    errorCode: httpCode
  };
  const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE);
  const extra = {
    payMethod,
    errorCode: errorCode || httpCode,
    data,
    token: token || accessToken
  };
  sentryCaptureEvent({
    extra,
    message,
    tags
  });
};

const acceptSignInQrcode = (param: any) => {
  // Segment Event
  segmentEvent(NAME.ACCEPT_SIGN_IN_QRCODE, param, true);
};

const ignoreSignInQrcode = (code: any) => {
  // Segment Event
  segmentEvent(NAME.IGNORE_SIGN_IN_QRCODE, code, true);
};

const acceptSignInSmartTv = (param: any) => {
  const { currentPage, buttonStatus, code } = param;
  const data = {
    current_page: currentPage,
    button_name: 'Xác nhận đăng nhập SmartTV',
    button_status: buttonStatus,
    code: code,
    flow_name: 'tv_registration'
  };
  segmentEvent(NAME.CONFIRM_BUTTON_SELECTED, data, true);
};

const offlineDetect = (param: any) => {
  const { currentPage, contentId, contentTitle, contentType } = param;
  const data = {
    current_page: currentPage || null,
    content_id: contentId || null,
    content_title: contentTitle || null,
    content_type: contentType || null
  };
  segmentEvent(NAME.LOST_NETWORK_CONNECTION, data, true);
};

export default {
  login,
  signUp,
  acceptSignInQrcode,
  ignoreSignInQrcode,
  webVisited,
  signUpSuccessfully,
  loadDeepLink,
  contentSelected,
  bannerSelected,
  categorySelected,
  menuSelected,
  apiError,
  ribbonSelected,
  getProfileError,
  createTransactionError,
  pageView,
  offlineDetect,
  acceptSignInSmartTv
};

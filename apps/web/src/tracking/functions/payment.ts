/* eslint-disable class-methods-use-this */
import ConfigG<PERSON> from '@config/ConfigGTM';
import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import { CURRENCY, PAYMENT_METHOD, PAYMENT_TYPE, SENTRY, TVOD } from '@constants/constants';
import { sentryCaptureEvent } from '@tracking/sentry';
import { handleGTMEvent } from '../TrackingGTM';
import { segmentEvent } from '../TrackingSegment';

export default class TrackingPayment {
  createTransaction({
    transactionData,
    selectedTerm,
    selectedMethod,
    promotionData,
    bankCode,
    isSegmentedUser
  }: any) {
    const eventName = NAME.PAYMENT.PAY_BUTTON_SELECTED;
    segmentEvent(eventName, {
      [PROPERTY.FLOW_NAME]: isSegmentedUser ? VALUE.OFFER_FREE_TO_SUB : null,
      [PROPERTY.PRODUCT]: selectedTerm?.id,
      [PROPERTY.PRICE]: selectedTerm?.old_price || '',
      [PROPERTY.PAID_PRICE]: selectedTerm?.price || '',
      [PROPERTY.PROMOTION]: promotionData?.promotionCode || '',
      [PROPERTY.PAYMENT_METHOD]: selectedMethod?.id || '',
      [PROPERTY.BANK]: selectedMethod?.id === PAYMENT_METHOD.VN_PAY ? bankCode || '' : '',
      [PROPERTY.RECURRING]: !!selectedTerm?.recurring,
      [PROPERTY.TRANSACTION_ID]:
        transactionData?.orderId ||
        transactionData?.orderID ||
        transactionData?.txnRef ||
        transactionData?.tnxID,
      [PROPERTY.CAUSE_FOR_FAILURE]: transactionData?.message || ''
    });
  }

  promotionSelected() {
    segmentEvent(NAME.PAYMENT.PROMOTION_SELECTED, {
      [PROPERTY.PAYMENT_METHOD]: 'cake'
    });
  }

  payButtonSelected({
    selectedTerm,
    promotionData,
    selectedMethod,
    bankCode,
    transaction,
    isSegmentedUser,
    trackingError
  }: any) {
    segmentEvent(NAME.PAYMENT.PAY_BUTTON_SELECTED, {
      [PROPERTY.FLOW_NAME]: isSegmentedUser ? VALUE.OFFER_FREE_TO_SUB : null,
      [PROPERTY.PRODUCT]: selectedTerm?.id || null,
      [PROPERTY.PRICE]: selectedTerm?.oldPrice || null,
      [PROPERTY.PAID_PRICE]: selectedTerm?.price || '',
      [PROPERTY.PROMOTION]: promotionData?.promotionCode || null,
      [PROPERTY.PAYMENT_METHOD]: selectedMethod?.id || null,
      [PROPERTY.BANK]: selectedMethod?.id === PAYMENT_METHOD.VN_PAY ? bankCode : null,
      [PROPERTY.RECURRING]: !!selectedTerm?.recurring,
      [PROPERTY.TRANSACTION_ID]:
        transaction?.orderId || transaction?.orderID || transaction?.txnRef || null,
      [PROPERTY.CAUSE_FOR_FAILURE]: transaction?.message || trackingError || null
    });
  }

  selectTerm({ selectedTerm }: any) {
    segmentEvent(NAME.PAYMENT.PACKAGE_DURATION_SELECTED, {
      [PROPERTY.PACKAGE_DURATION]: selectedTerm?.name,
      [PROPERTY.RECURRING]: selectedTerm?.recurring ? 'True' : 'False',
      [PROPERTY.PACKAGE_ID]: selectedTerm?.id,
      [PROPERTY.PACKAGE_NAME]: selectedTerm?.name,
      [PROPERTY.PACKAGE_PRICE]: selectedTerm?.price
    });
  }

  selectMethod({ selectedMethod }: any) {
    segmentEvent(NAME.PAYMENT.METHOD_SELECTED, {
      [PROPERTY.PAYMENT_METHOD]: selectedMethod?.id
    });
  }

  selectPackage({ selectedPackage }: any) {
    segmentEvent(NAME.PAYMENT.PACKAGE_SELECTED, {
      [PROPERTY.REFERRAL]: window.location.href,
      [PROPERTY.PACKAGE_NAME]: selectedPackage?.name,
      [PROPERTY.PACKAGE_ID_SURVEY]: selectedPackage?.id
    });
  }

  paymentPageLoaded() {
    segmentEvent(NAME.PAYMENT.PAGE_LOADED, { [PROPERTY.CURRENT_PAGE]: window?.location?.href });
  }

  pageLoaded() {
    const eventName = NAME.PAYMENT.PAGE_LOADED;
    // Segment Event
    segmentEvent(eventName, { [PROPERTY.CURRENT_PAGE]: window?.location?.href });
  }

  selectBank({ bank }: any) {
    segmentEvent(NAME.SELECT_BANK, {
      [PROPERTY.BANK]: bank?.code
    });
  }

  updateInfo(contactMethod: any) {
    segmentEvent(NAME.UPDATE_INFORMATION, {
      [PROPERTY.CONTACT_METHOD]: contactMethod
    });
  }

  orderFailed({ selectedTerm, selectedMethod, bankCode, transaction, profile, query }: any) {
    const referralCode = query?.referralCode;
    const isSegmentedUser = selectedTerm?.id === parseInt(query?.termId);
    const paymentTypeTvod = transaction?.paymentType === PAYMENT_TYPE.TVOD;
    const transId =
      transaction?.orderId || transaction?.orderID || transaction?.txnRef || transaction?.txnID;
    const pageLoadedParams = {
      [PROPERTY.TRANSACTION_ID]: transaction?.orderId || transaction?.data?.txnRef,
      [PROPERTY.CHECKOUT_RESULT]: 'failed',
      [PROPERTY.CAUSE_FOR_FAILURE]: transaction?.message || '',
      [PROPERTY.FLOW_NAME]: isSegmentedUser ? VALUE.OFFER_FREE_TO_SUB : null,
      [PROPERTY.PAYMENT_METHOD]: selectedMethod?.id || ''
    };

    if (referralCode) {
      pageLoadedParams[PROPERTY.REFERRAL] = VALUE.REFERRAL_CODE?.[referralCode] || '';
    }

    if (paymentTypeTvod) {
      pageLoadedParams[PROPERTY.REFERRAL] = TVOD.TVOD_TYPE;
      pageLoadedParams[PROPERTY.CAUSE_FOR_FAILURE] =
        transaction?.errorCode || transaction?.errorCodeMsg || transaction?.message;
    }
    segmentEvent(NAME.ORDER_FAIL, {
      [PROPERTY.PRODUCT]: selectedTerm?.name || '',
      [PROPERTY.PRICE]: selectedTerm?.oldPrice || 0,
      [PROPERTY.PAID_PRICE]: selectedTerm?.price || 0,
      [PROPERTY.PROMOTION]: selectedTerm?.percentDiscount || 0,
      [PROPERTY.PAYMENT_METHOD]: selectedMethod?.id || '',
      [PROPERTY.BANK]: bankCode || '',
      [PROPERTY.RECURRING]: !!selectedTerm?.recurring,
      [PROPERTY.CAUSE_FOR_FAILURE]: transaction?.message || ''
    });

    handleGTMEvent({
      eventName: ConfigGTM.ORDER_FAILED,
      userProfile: profile,
      transactionStatus: ConfigGTM.FAIL,
      query,
      params: {
        m_Status: ConfigGTM.FAIL,
        m_TransactionID: transId || '',
        m_PackageName: transaction?.packageName || '',
        m_Duration: transaction?.displayDuration || '',
        m_Amount: transaction?.amount || '',
        m_PaymentMethod: selectedMethod?.id || '',
        m_Message: transaction?.errorString || transaction?.message,
        m_Currency: CURRENCY.VND
      }
    });
    segmentEvent(NAME.PAYMENT.CHECK_OUT_RESULT_PAGE_LOADED, pageLoadedParams);

    const errorType = SENTRY.PAYMENT;
    const errorCode = transaction?.errorCode;
    const method = selectedMethod?.name;
    const errorMessage = transaction?.errorString || transaction?.message;
    const tags = { errorType, errorCode, method };
    const extra = { errorCode, errorMessage, method };

    sentryCaptureEvent({
      message: '[PAYMENT]',
      extra,
      tags
    });
  }

  orderCompleted({ selectedTerm, selectedMethod, bankCode, transaction, profile, query }: any) {
    const referralCode = query?.referralCode;
    const paymentTypeTvod = transaction?.paymentType === PAYMENT_TYPE.TVOD;
    const isSegmentedUser = selectedTerm?.id === parseInt(query?.termId);

    const transId =
      transaction?.orderId || transaction?.orderID || transaction?.txnRef || transaction?.txnID;
    const pageLoadedParams = {
      [PROPERTY.TRANSACTION_ID]: transId,
      [PROPERTY.CHECKOUT_RESULT]: 'succeed',
      [PROPERTY.CAUSE_FOR_FAILURE]: '',
      [PROPERTY.FLOW_NAME]: isSegmentedUser ? VALUE.OFFER_FREE_TO_SUB : null,
      [PROPERTY.PAYMENT_METHOD]: selectedMethod?.id || ''
    };

    if (referralCode) {
      pageLoadedParams[PROPERTY.REFERRAL] = VALUE.REFERRAL_CODE?.[referralCode] || '';
    }

    if (paymentTypeTvod) {
      pageLoadedParams[PROPERTY.REFERRAL] = TVOD.TVOD_TYPE;
      pageLoadedParams[PROPERTY.CAUSE_FOR_FAILURE] =
        transaction?.errorCode || transaction?.errorCodeMsg || transaction?.message;
    }
    segmentEvent(NAME.ORDER_COMPLETED, {
      [PROPERTY.PRODUCT]: selectedTerm?.name || '',
      [PROPERTY.PRICE]: selectedTerm?.oldPrice || 0,
      [PROPERTY.PAID_PRICE]: selectedTerm?.price || 0,
      [PROPERTY.PROMOTION]: selectedTerm?.percentDiscount || 0,
      [PROPERTY.PAYMENT_METHOD]: selectedMethod?.id || '',
      [PROPERTY.BANK]: bankCode || '',
      [PROPERTY.RECURRING]: !!selectedTerm?.recurring,
      [PROPERTY.FLOW_NAME]: isSegmentedUser ? VALUE.OFFER_FREE_TO_SUB : null,
      [PROPERTY.PAYMENT_METHOD]: selectedMethod?.id || '',
      [PROPERTY.PACKAGE_DURATION]: transaction?.displayDuration || ''
    });

    handleGTMEvent({
      eventName: ConfigGTM.ORDER_COMPLETED,
      userProfile: profile,
      transactionStatus: ConfigGTM.SUCCESSFULLY,
      query,
      params: {
        m_Status: ConfigGTM.STATUS_SUCCESSFULLY,
        m_TransactionID: transId || '',
        m_PackageName: transaction?.packageName || '',
        m_Duration: transaction?.displayDuration || '',
        m_Amount: transaction?.amount || '',
        m_PaymentMethod: selectedMethod?.id || '',
        m_Message: transaction?.errorString || transaction?.message,
        m_Currency: CURRENCY.VND
      }
    });
    segmentEvent(NAME.PAYMENT.CHECK_OUT_RESULT_PAGE_LOADED, pageLoadedParams);
  }

  globalPaymentButtonSelected({ currentPage, flowName, contentId, contentTitle }: any) {
    segmentEvent(NAME.PAYMENT.PAYMENT_BUTTON_SELECTED, {
      [PROPERTY.CURRENT_PAGE]:
        currentPage || typeof window !== 'undefined' ? window.location.href : '',
      [PROPERTY.FLOW_NAME]: flowName || null,
      [PROPERTY.CONTENT_ID]: contentId || null,
      [PROPERTY.CONTENT_TITLE]: contentTitle || null
    });
  }

  globalCheckoutButtonSelected({ currentPage }: any) {
    segmentEvent(NAME.PAYMENT.CHECKOUT_BUTTON_SELECTED, {
      [PROPERTY.CURRENT_PAGE]: currentPage || null
    });
  }

  globalShowQrCodePayment({ currentPage }: any) {
    segmentEvent(NAME.PAYMENT.SHOW_QRCODE_PAYMENT, {
      [PROPERTY.CURRENT_PAGE]: currentPage || null
    });
  }

  globalAsiaPayCheckoutButtonSelected({ currentPage }: any) {
    segmentEvent(NAME.PAYMENT.ASIAPAY_CHECKOUT_BUTTON_SELECTED, {
      [PROPERTY.CURRENT_PAGE]: currentPage || null
    });
  }

  laterButtonSelect() {
    segmentEvent(NAME.PAYMENT.LATER_BUTTON_SELECTED);
  }

  signUpButtonSelectedInPaymentScreen() {
    segmentEvent(NAME.PAYMENT.SIGN_UP_BUTTON_SELECTED_IN_PAYMENT_SCREEN);
  }

  advertisementLoaded() {
    segmentEvent(NAME.PAYMENT.DIALOG_ADVERTISEMENT_LOADED);
  }

  userHideAdvertisement() {
    segmentEvent(NAME.PAYMENT.USER_HIDE_ADVETISEMENT_SELECTED);
  }

  userOpenAdvertisement() {
    segmentEvent(NAME.PAYMENT.USER_OPEN_ADVETISEMENT_SELECTED);
  }

  userSeePackageList() {
    segmentEvent(NAME.PAYMENT.USER_SEE_PACKAGE_LIST);
  }

  userBackButtonSelected() {
    segmentEvent(NAME.PAYMENT.USER_BACK_BUTTON_SELECTED_IN_PACKAGE_SCREEN);
  }

  diaglogSurveyLoad() {
    segmentEvent(NAME.PAYMENT.DIALOG_SURVEY_LOAD);
  }

  closeSurvey() {
    segmentEvent(NAME.PAYMENT.CLOSE_SURVEY);
  }

  helpButtonSelectedSurvey() {
    segmentEvent(NAME.PAYMENT.HELP_BUTTON_SELECTED);
  }

  chatWidgetSelectedSurvey({ buttonType }: any) {
    segmentEvent(NAME.PAYMENT.CHAT_WIDGET_SELECTED, {
      [PROPERTY.BUTTON_TYPE]: buttonType || ''
    });
  }

  chatWidgetClosedSurvey() {
    segmentEvent(NAME.PAYMENT.CHAT_WIDGET_CLOSED);
  }

  sendLeavingSurvey({ options, other }: any) {
    segmentEvent(NAME.PAYMENT.USER_SEND_LEAVING_SURVEY_SELECTED, {
      [PROPERTY.PROPERTIES_LEAVING_REASONS]: options,
      [PROPERTY.PROPERTIES_OTHERS_LEAVING_REASONS]: other
    });
  }

  promotionButtonSelected() {
    segmentEvent(NAME.PAYMENT.PROMOTION_BUTTON_SELECTED, {
      [PROPERTY.CURRENT_PAGE]: typeof window !== 'undefined' ? window.location.href : ''
    });
  }

  codeInputPageLoaded() {
    segmentEvent(NAME.PAYMENT.CODE_INPUT_PAGE_LOADED, {
      [PROPERTY.CURRENT_PAGE]: typeof window !== 'undefined' ? window.location.href : ''
    });
  }

  voucherResultPageLoaded({ data }: any) {
    segmentEvent(NAME.VOUCHER_RESULT_PAGE_LOADED, {
      [PROPERTY.TRANSACTION_ID]: null,
      [PROPERTY.CHECKOUT_RESULT]: 'success',
      [PROPERTY.PACKAGE_NAME]: data?.groupName || '',
      [PROPERTY.PACKAGE_DURATION]: data?.name || '',
      [PROPERTY.CAUSE_FOR_FAILURE]: null,
      [PROPERTY.TEXT_ERROR]: null
    });
  }

  recurringToggleSelected({ newTermSelected, selectedMethodName, isRecurring }: any) {
    segmentEvent(NAME.PAYMENT.RECURRING_TOGGLE_SELECTED, {
      [PROPERTY.CURRENT_PAGE]: window.location.href,
      [PROPERTY.PACKAGE_ID]: newTermSelected?.id || '',
      [PROPERTY.PACKAGE_NAME]: newTermSelected?.name || '',
      [PROPERTY.PAID_PRICE]: newTermSelected?.price || '',
      [PROPERTY.PACKAGE_DURATION]: newTermSelected?.duration || '',
      [PROPERTY.BUTTON_STATUS]: isRecurring ? 'on' : 'off',
      [PROPERTY.METHOD]: selectedMethodName || ''
    });
  }

  recurringButtonSelected({ packageId, packageName, isRecurring }: any) {
    segmentEvent(NAME.PAYMENT.RECURRING_BUTTON_SELECTED, {
      [PROPERTY.PACKAGE_ID]: packageId || '',
      [PROPERTY.PACKAGE_NAME]: packageName || '',
      [PROPERTY.RECURRING]: isRecurring ? 'True' : 'False'
    });
  }

  cancelRecurringButtonSelected({ packageId, packageName }: any) {
    segmentEvent(NAME.PAYMENT.CANCEL_RECURRING_BUTTON_SELECTED, {
      [PROPERTY.PACKAGE_ID]: packageId || '',
      [PROPERTY.PACKAGE_NAME]: packageName || ''
    });
  }

  backButtonSelected({ packageId, packageName }: any) {
    segmentEvent(NAME.PAYMENT.BACK_BUTTON_SELECTED, {
      [PROPERTY.PACKAGE_ID]: packageId || '',
      [PROPERTY.PACKAGE_NAME]: packageName || ''
    });
  }
  remindButtonSelected({ contentId, contentName, contentType, isBlockVip, userType, status }: any) {
    segmentEvent(NAME.PAYMENT.REMIND_BUTTON_SELECTED, {
      [PROPERTY.CONTENT_ID]: contentId || '',
      [PROPERTY.CONTENT_NAME]: contentName || '',
      [PROPERTY.CONTENT_TYPE]: contentType || '',
      [PROPERTY.IS_BLOCK_VIP]: isBlockVip || false,
      [PROPERTY.USER_TYPE]: userType || 'guest',
      [PROPERTY.STATUS]: status
    });
  }

  //  END
}

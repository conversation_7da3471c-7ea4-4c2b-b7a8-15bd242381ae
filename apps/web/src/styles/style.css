.ohYeah-enter {
  opacity: 0;
  transform: scale(0.5);
}
.ohYeah-enter-active {
  opacity: 1;
  transition: opacity 300ms, transform 300ms;
}

.scaleUp {
  animation: 1s forwards 0.6s scales;
}

@keyframes scales {
  0% {
    max-width: 32px;
    transform: scale(1);
  }
  100% {
    transform: scale(1);
    max-width: 48px;
  }
}
@-webkit-keyframes scales {
  0% {
    max-width: 32px;
    transform: scale(1);
  }
  100% {
    max-width: 48px;
    transform: scale(1);
  }
}

.ohYeah-expand {
  opacity: 0 !important;
  transform: scale(0) !important;
  width: 0px !important;
  height: 0px !important;
}

.ohYeah-expand-active {
  opacity: 1 !important;
  /*width: 960px !important;*/
  height: auto !important;
  /*top: 2rem !important;*/
  transition: opacity 300ms, transform 300ms;
  transition: width 1s, height 0.5s;
  transform-origin: center top;

  /*left: calc(50% - 480px) !important;*/
  transition-duration: 0.2s;
  /*transition-delay: 500ms;*/
  transition-timing-function: linear;
}

.grid-vertical {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-flow: row dense;

  /* extra styles */
  grid-gap: 0.5rem;
}

.grid-item {
  grid-column-start: 1;

  /* extra styles */
  background-color: #def;
  padding: 0.5rem;
}

.second-half {
  grid-column-start: 2;

  /* extra styles */
  background-color: #abc;
}

.card-section.partner-viettel::before {
  background-color: #ee0033;
}

.modal.modal--notify-login .mask-inner::before {
  background: #222;
}

.swiper-button-disabled {
  display: none;
}

.font-size-18 {
  font-size: 1.125rem;
}

.recurring-note p {
  color: #646464;
}
.recurring-note span {
  color: #3ac882;
  font-weight: bold;
}
.pt-0 {
  padding-top: 0 !important;
}
/* ============== Image ============== */
.animate-fade-in.duration {
  -webkit-animation-duration: 2.5s;
  animation-duration: 2.5s;
}

.imag-lazy-load.skeleton {
  position: relative;
  background-color: #201f1f;
  overflow: hidden;
}

.imag-lazy-load.skeleton.pulse {
  height: 100%;
  width: 100%;
  background: linear-gradient(-90deg, #2f2e2e 0%, #484747 50%, #2e2b2b 100%);
  background-size: 400% 400%;
  -webkit-animation: pulse 1.2s ease-in-out infinite;
  animation: pulse 1.2s ease-in-out infinite;
  position: absolute;
}
.disabled {
  opacity: 0.3;
  pointer-events: none;
}
@-webkit-keyframes pulse {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: -135% 0%;
  }
}
@keyframes pulse {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: -135% 0%;
  }
}
/* ============== End Image ============== */

@media screen and (min-width: 45rem) {
  .intro--preview-vod.intro--preview-vod--modal .tags-group.child-spacing-x .tags {
    margin-right: 0.75rem;
    margin-left: 0.75rem;
  }
  .intro--preview-vod.intro--preview-vod--modal .tags-group.child-spacing-x .tags.watching-soon {
    margin-right: 0;
  }

  .tags-group.child-spacing-x .tags:not(.tags--variant):not(.tags--gold).watching-soon {
    padding: 0 0.5rem;
  }
  .intro--preview-vod.intro--preview-vod--mini .tags-group.child-spacing-x .tags.watching-soon {
    margin-right: 0;
  }

  .intro--preview-vod.intro--preview-vod--modal
    .tags-group.child-spacing-x
    .tags.tags--gold:first-child {
    margin-right: 0;
    margin-left: 0.625rem;
  }
  .intro--preview-vod.intro--preview-vod--modal .tags-group.child-spacing-x .tags:last-child {
    margin-right: 0;
  }

  .intro--preview-vod.intro--preview-vod--modal
    .tags-group.child-spacing-x
    .tags:not(.tags--variant).tags--outline-v::before {
    right: -0.75rem;
  }

  /**/
  .intro--preview-vod.intro--preview-vod--mini .tags-group.child-spacing-x .tags {
    margin-right: 0.5rem;
    margin-left: 0.5rem;
  }
  .intro--preview-vod.intro--preview-vod--mini
    .tags-group.child-spacing-x
    .tags.tags--gold:first-child {
    margin-right: 0;
    margin-left: 0.625rem;
  }
  .intro--preview-vod.intro--preview-vod--mini .tags-group.child-spacing-x .tags:last-child {
    margin-right: 0;
  }

  .intro--preview-vod.intro--preview-vod--mini
    .tags-group.child-spacing-x
    .tags:not(.tags--variant).tags--outline-v::before {
    right: -0.5rem;
  }
}

.lds-ring {
  display: inline-block;
  position: relative;
  width: 40px;
  height: 40px;
}
.lds-ring div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 40px;
  height: 40px;
  margin: 8px;
  border: 4px solid #fff;
  border-radius: 50%;
  animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: #fff transparent transparent transparent;
}
.lds-ring div:nth-child(1) {
  animation-delay: -0.45s;
}
.lds-ring div:nth-child(2) {
  animation-delay: -0.3s;
}
.lds-ring div:nth-child(3) {
  animation-delay: -0.15s;
}
@keyframes lds-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

export const MOE_NAME = {
  COMING_SOON_CONTENT: 'coming_soon_content'
};

export const MOE_PROPERTY = {
  CONTENT_ID: 'content_id',
  CONTENT_TYPE: 'content_type',
  IS_SUB: 'is_sub'
};

export const LIST_NAME_MOE = {
  CONTENT_SELECT: 'content selected',
  SEARCH_ICON_SELECTED: 'search icon selected',
  SEARCH: 'search',
  VIDEO_STARTED: 'video started',
  VIDEO_PROGRESS: 'video progress',
  VIDEO_COMPLETED: 'video completed',
  LOGIN_SUCCESSFULLY: 'login successfully',
  VIEW_VIDEO_DETAIL_BUTTON_SELECTED: 'view_video_detail_button_selected',
  RIBBON_SELECT: 'ribbon selected',
  MENU_SELECTED: 'menu selected',
  CHECK_OUT_STARTED: 'check_out_started',
  CATEGORY_SELECTED: 'category selected',
  PLAY_VIDEO: 'play video',
  SKIP_ADS: 'skipad button selected',
  LIVETV_STARTED: 'livetv started',
  BUTTON_LOGIN: 'login button selected',
  REMIND_NOTIFICATION_PERMISSION_DIALOG_LOADED: 'remind notification permission dialog loaded',
  VIEW_VIDEO_DETAIL_OPEN_CONTENT: 'view video detail open content',
  LOGIN: 'login',
  LIVETV_COMPLETED: 'livetv completed',
  OS_NOTIFICATION_PERMISSION_DIALOG_LOADED: 'os notification permission dialog loaded',
  PAYMENT_PAGE_LOADED: 'payment page loaded',
  PAYMENT_EXIT: 'payment_exit',
  BUTTON_REGISTRATION: 'registration button selected',
  SIGN_UP: 'sign up',
  CONFIRMATION_BUTTON_SELECTED: 'confirmation_button_selected',
  NOTIFICATION_ICON_SELECTED: 'notification icon selected',
  SIGN_UP_SUCCESSFULLY: 'sign up successfully',
  SIGN_UP_CANCEL: 'sign_up_cancel',
  SIGNUP_BUTTON_SELECTED: 'sign_up_button_selected',
  DIALOG_REGISTRATION_TRIGGER_LOADED: 'dialog_registration_trigger_loaded',
  PAY_BUTTON_SELECTED: 'pay button selected',
  LOGIN_BIND_ACCOUNT_PHONE: 'login_bind_account_phone',
  FORGOT_PASS_STARTED: 'forgot password flow started',
  NOTIFICATION_PERMISSION_DIALOG_LOADED: 'notification_permission_dialog_loaded',
  SELECT_PRODUCT: 'select product',
  LOGIN_BIND_ACCOUNT_PASS: 'login_bind_account_pass',
  LOGIN_BIND_ACCOUNT_SUCCESSFUL: 'login_bind_account_successful',
  EXPIRE_FULLSCREEN_LOADED: 'expired_fullscreen_loaded',
  EXPIRE_BANNER_LOADED: 'expired_banner_loaded',
  EXPIRE_BUTTON_SELECTED: 'expired_button_selected',
  CHECK_OUT_RESULT_PAGE_LOADED: 'checkout result page loaded',
  BANNER_SELECTED: 'banner selected',
  ORDER_COMPLETED: 'order completed',
  REMOVE_ADS_SUBSCRIBE_PACKAGE_BUTTON_SELECTED: 'remove_ads_subscribe_package_button_selected',
  BANNER_COMMENT_SELECTED: 'banner_register_for_comment_selected',
  NEARLY_EXPIRE_BANNER_LOADED: 'nearly_expire_banner_loaded',
  VIEW_VIDEO_DETAIL: 'view_video_detail',
  EXPIRE_BANNER_SELECTED: 'expired_banner_selected',
  NEARLY_EXPIRE_FULLSCREEN_LOADED: 'nearly_expire_fullscreen_loaded',
  NEARLY_EXPIRE_FULLSCREEN_BUTTON_SELECTED: 'nearly_expire_fullscreen_button_selected',
  COMPLETED_PURCHASE: 'completed purchase',
  BUTTON_TRIAL: 'trial button selected',
  NEARLY_EXPIRE_BANNER_SELECTED: 'nearly_expire_banner_selected',
  START_PLAYING_ADS: 'start playing ads'
};

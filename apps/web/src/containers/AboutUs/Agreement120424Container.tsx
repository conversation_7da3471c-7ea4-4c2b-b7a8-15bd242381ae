import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAgreementConfig120424, setLoadedData } from '@actions/app';
import { FOOTER } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';

const AgreementContainer120424 = () => {
  const dispatch = useDispatch();
  const htmlConfig = useSelector((state: any) => state?.App?.agreement120424);

  const seo = {
    url: FOOTER.NAV.AGREEMENT_120424.PATH,
    title: FOOTER.NAV.AGREEMENT_120424.TITLE,
    description: FOOTER.NAV.AGREEMENT_120424.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.AGREEMENT_120424.PATH,
    deeplink: 'vieonapp://vieon.vn/aboutus/chinh-sach-va-quy-dinh-chung-120424'
  };

  useEffect(() => {
    if (!htmlConfig) {
      dispatch(getAgreementConfig120424({}));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

AgreementContainer120424.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    await store.dispatch(getAgreementConfig120424({ ssr: true }));
    return { ssr: true };
  }
  return {};
};

export default AgreementContainer120424;

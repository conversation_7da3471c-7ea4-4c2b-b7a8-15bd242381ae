import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getPrivacyConfig120424, setLoadedData } from '@actions/app';
import { FOOTER } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';

const PrivacyContainer120424 = () => {
  const dispatch = useDispatch();
  const htmlConfig = useSelector((state: any) => state?.App?.privacy120424);

  const seo = {
    url: FOOTER.NAV.PRIVATE_POLICY_120424.PATH,
    title: FOOTER.NAV.PRIVATE_POLICY_120424.TITLE,
    description: FOOTER.NAV.PRIVATE_POLICY_120424.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.PRIVATE_POLICY_120424.PATH,
    deeplink: 'vieonapp://vieon.vn/aboutus/chinh-sach-va-quy-dinh-chung-120424'
  };

  useEffect(() => {
    if (!htmlConfig) {
      dispatch(getPrivacyConfig120424({}));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

PrivacyContainer120424.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    await store.dispatch(getPrivacyConfig120424({ ssr: true }));
    return { ssr: true };
  }
  return {};
};

export default PrivacyContainer120424;

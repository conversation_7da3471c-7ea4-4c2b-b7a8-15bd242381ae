#!/bin/bash

# VieON Web Monorepo Migration Script
# This script moves files from the current structure to the new modular structure

set -e

echo "Starting VieON Web Monorepo Migration..."

# Create backup
echo "Creating backup..."
cp -r apps/web/src apps/web/src_backup


echo "Migrating core module..."

if [ -d "apps/web/src/config" ]; then
    mkdir -p "$(dirname "packages/core/src/config")"
    mv "apps/web/src/config" "packages/core/src/config"
    echo "  ✅ Moved apps/web/src/config -> packages/core/src/config"
fi

if [ -d "apps/web/src/constants" ]; then
    mkdir -p "$(dirname "packages/core/src/constants")"
    mv "apps/web/src/constants" "packages/core/src/constants"
    echo "  ✅ Moved apps/web/src/constants -> packages/core/src/constants"
fi

if [ -d "apps/web/src/helpers" ]; then
    mkdir -p "$(dirname "packages/core/src/helpers")"
    mv "apps/web/src/helpers" "packages/core/src/helpers"
    echo "  ✅ Moved apps/web/src/helpers -> packages/core/src/helpers"
fi

if [ -d "apps/web/src/functions" ]; then
    mkdir -p "$(dirname "packages/core/src/functions")"
    mv "apps/web/src/functions" "packages/core/src/functions"
    echo "  ✅ Moved apps/web/src/functions -> packages/core/src/functions"
fi

if [ -d "apps/web/src/store" ]; then
    mkdir -p "$(dirname "packages/core/src/store")"
    mv "apps/web/src/store" "packages/core/src/store"
    echo "  ✅ Moved apps/web/src/store -> packages/core/src/store"
fi

if [ -d "apps/web/src/provider" ]; then
    mkdir -p "$(dirname "packages/core/src/provider")"
    mv "apps/web/src/provider" "packages/core/src/provider"
    echo "  ✅ Moved apps/web/src/provider -> packages/core/src/provider"
fi

if [ -f "apps/web/src/customHook.ts" ]; then
    mkdir -p "$(dirname "packages/core/src/utils/customHook.ts")"
    mv "apps/web/src/customHook.ts" "packages/core/src/utils/customHook.ts"
    echo "  ✅ Moved apps/web/src/customHook.ts -> packages/core/src/utils/customHook.ts"
fi

if [ -f "apps/web/src/customRequest.ts" ]; then
    mkdir -p "$(dirname "packages/core/src/utils/customRequest.ts")"
    mv "apps/web/src/customRequest.ts" "packages/core/src/utils/customRequest.ts"
    echo "  ✅ Moved apps/web/src/customRequest.ts -> packages/core/src/utils/customRequest.ts"
fi

echo "Migrating ui-kits module..."

if [ -d "apps/web/src/components/basic" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/basic")"
    mv "apps/web/src/components/basic" "packages/ui-kits/src/components/basic"
    echo "  ✅ Moved apps/web/src/components/basic -> packages/ui-kits/src/components/basic"
fi

if [ -d "apps/web/src/components/common" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/common")"
    mv "apps/web/src/components/common" "packages/ui-kits/src/components/common"
    echo "  ✅ Moved apps/web/src/components/common -> packages/ui-kits/src/components/common"
fi

if [ -d "apps/web/src/components/Animation" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/Animation")"
    mv "apps/web/src/components/Animation" "packages/ui-kits/src/components/Animation"
    echo "  ✅ Moved apps/web/src/components/Animation -> packages/ui-kits/src/components/Animation"
fi

if [ -d "apps/web/src/components/Button" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/Button")"
    mv "apps/web/src/components/Button" "packages/ui-kits/src/components/Button"
    echo "  ✅ Moved apps/web/src/components/Button -> packages/ui-kits/src/components/Button"
fi

if [ -d "apps/web/src/components/Card" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/Card")"
    mv "apps/web/src/components/Card" "packages/ui-kits/src/components/Card"
    echo "  ✅ Moved apps/web/src/components/Card -> packages/ui-kits/src/components/Card"
fi

if [ -d "apps/web/src/components/Checkbox" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/Checkbox")"
    mv "apps/web/src/components/Checkbox" "packages/ui-kits/src/components/Checkbox"
    echo "  ✅ Moved apps/web/src/components/Checkbox -> packages/ui-kits/src/components/Checkbox"
fi

if [ -d "apps/web/src/components/Input" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/Input")"
    mv "apps/web/src/components/Input" "packages/ui-kits/src/components/Input"
    echo "  ✅ Moved apps/web/src/components/Input -> packages/ui-kits/src/components/Input"
fi

if [ -d "apps/web/src/components/Modal" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/Modal")"
    mv "apps/web/src/components/Modal" "packages/ui-kits/src/components/Modal"
    echo "  ✅ Moved apps/web/src/components/Modal -> packages/ui-kits/src/components/Modal"
fi

if [ -d "apps/web/src/components/Icons" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/Icons")"
    mv "apps/web/src/components/Icons" "packages/ui-kits/src/components/Icons"
    echo "  ✅ Moved apps/web/src/components/Icons -> packages/ui-kits/src/components/Icons"
fi

if [ -d "apps/web/src/components/Dropdown" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/Dropdown")"
    mv "apps/web/src/components/Dropdown" "packages/ui-kits/src/components/Dropdown"
    echo "  ✅ Moved apps/web/src/components/Dropdown -> packages/ui-kits/src/components/Dropdown"
fi

if [ -d "apps/web/src/components/Tooltip" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/Tooltip")"
    mv "apps/web/src/components/Tooltip" "packages/ui-kits/src/components/Tooltip"
    echo "  ✅ Moved apps/web/src/components/Tooltip -> packages/ui-kits/src/components/Tooltip"
fi

if [ -d "apps/web/src/components/DatePicker" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/DatePicker")"
    mv "apps/web/src/components/DatePicker" "packages/ui-kits/src/components/DatePicker"
    echo "  ✅ Moved apps/web/src/components/DatePicker -> packages/ui-kits/src/components/DatePicker"
fi

if [ -d "apps/web/src/components/CopyText" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/CopyText")"
    mv "apps/web/src/components/CopyText" "packages/ui-kits/src/components/CopyText"
    echo "  ✅ Moved apps/web/src/components/CopyText -> packages/ui-kits/src/components/CopyText"
fi

if [ -d "apps/web/src/components/ToggleCheckbox" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/components/ToggleCheckbox")"
    mv "apps/web/src/components/ToggleCheckbox" "packages/ui-kits/src/components/ToggleCheckbox"
    echo "  ✅ Moved apps/web/src/components/ToggleCheckbox -> packages/ui-kits/src/components/ToggleCheckbox"
fi

if [ -d "apps/web/src/styles" ]; then
    mkdir -p "$(dirname "packages/ui-kits/src/styles")"
    mv "apps/web/src/styles" "packages/ui-kits/src/styles"
    echo "  ✅ Moved apps/web/src/styles -> packages/ui-kits/src/styles"
fi

echo "Migrating auth module..."

if [ -d "apps/web/src/components/Authentication" ]; then
    mkdir -p "$(dirname "packages/auth/src/components/Authentication")"
    mv "apps/web/src/components/Authentication" "packages/auth/src/components/Authentication"
    echo "  ✅ Moved apps/web/src/components/Authentication -> packages/auth/src/components/Authentication"
fi

if [ -d "apps/web/src/components/LobbyProfile" ]; then
    mkdir -p "$(dirname "packages/auth/src/components/LobbyProfile")"
    mv "apps/web/src/components/LobbyProfile" "packages/auth/src/components/LobbyProfile"
    echo "  ✅ Moved apps/web/src/components/LobbyProfile -> packages/auth/src/components/LobbyProfile"
fi

if [ -f "apps/web/src/actions/globalAuth.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/stores/globalAuth.ts")"
    mv "apps/web/src/actions/globalAuth.ts" "packages/auth/src/stores/globalAuth.ts"
    echo "  ✅ Moved apps/web/src/actions/globalAuth.ts -> packages/auth/src/stores/globalAuth.ts"
fi

if [ -f "apps/web/src/actions/register.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/stores/register.ts")"
    mv "apps/web/src/actions/register.ts" "packages/auth/src/stores/register.ts"
    echo "  ✅ Moved apps/web/src/actions/register.ts -> packages/auth/src/stores/register.ts"
fi

if [ -f "apps/web/src/actions/user.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/stores/user.ts")"
    mv "apps/web/src/actions/user.ts" "packages/auth/src/stores/user.ts"
    echo "  ✅ Moved apps/web/src/actions/user.ts -> packages/auth/src/stores/user.ts"
fi

if [ -f "apps/web/src/reducers/globalAuth.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/stores/globalAuth.ts")"
    mv "apps/web/src/reducers/globalAuth.ts" "packages/auth/src/stores/globalAuth.ts"
    echo "  ✅ Moved apps/web/src/reducers/globalAuth.ts -> packages/auth/src/stores/globalAuth.ts"
fi

if [ -f "apps/web/src/reducers/register.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/stores/register.ts")"
    mv "apps/web/src/reducers/register.ts" "packages/auth/src/stores/register.ts"
    echo "  ✅ Moved apps/web/src/reducers/register.ts -> packages/auth/src/stores/register.ts"
fi

if [ -f "apps/web/src/reducers/user.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/stores/user.ts")"
    mv "apps/web/src/reducers/user.ts" "packages/auth/src/stores/user.ts"
    echo "  ✅ Moved apps/web/src/reducers/user.ts -> packages/auth/src/stores/user.ts"
fi

if [ -f "apps/web/src/apis/userApi.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/services/userApi.ts")"
    mv "apps/web/src/apis/userApi.ts" "packages/auth/src/services/userApi.ts"
    echo "  ✅ Moved apps/web/src/apis/userApi.ts -> packages/auth/src/services/userApi.ts"
fi

if [ -f "apps/web/src/services/userServices.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/services/userServices.ts")"
    mv "apps/web/src/services/userServices.ts" "packages/auth/src/services/userServices.ts"
    echo "  ✅ Moved apps/web/src/services/userServices.ts -> packages/auth/src/services/userServices.ts"
fi

if [ -f "apps/web/src/models/login.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/types/login.ts")"
    mv "apps/web/src/models/login.ts" "packages/auth/src/types/login.ts"
    echo "  ✅ Moved apps/web/src/models/login.ts -> packages/auth/src/types/login.ts"
fi

if [ -f "apps/web/src/models/register.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/types/register.ts")"
    mv "apps/web/src/models/register.ts" "packages/auth/src/types/register.ts"
    echo "  ✅ Moved apps/web/src/models/register.ts -> packages/auth/src/types/register.ts"
fi

if [ -f "apps/web/src/models/LoginResponse.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/types/LoginResponse.ts")"
    mv "apps/web/src/models/LoginResponse.ts" "packages/auth/src/types/LoginResponse.ts"
    echo "  ✅ Moved apps/web/src/models/LoginResponse.ts -> packages/auth/src/types/LoginResponse.ts"
fi

if [ -f "apps/web/src/models/Profile.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/types/Profile.ts")"
    mv "apps/web/src/models/Profile.ts" "packages/auth/src/types/Profile.ts"
    echo "  ✅ Moved apps/web/src/models/Profile.ts -> packages/auth/src/types/Profile.ts"
fi

if [ -f "apps/web/src/models/userType.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/types/userType.ts")"
    mv "apps/web/src/models/userType.ts" "packages/auth/src/types/userType.ts"
    echo "  ✅ Moved apps/web/src/models/userType.ts -> packages/auth/src/types/userType.ts"
fi

if [ -f "apps/web/src/models/forgetPassword.ts" ]; then
    mkdir -p "$(dirname "packages/auth/src/types/forgetPassword.ts")"
    mv "apps/web/src/models/forgetPassword.ts" "packages/auth/src/types/forgetPassword.ts"
    echo "  ✅ Moved apps/web/src/models/forgetPassword.ts -> packages/auth/src/types/forgetPassword.ts"
fi

echo "Migrating player module..."

if [ -d "apps/web/src/components/detail" ]; then
    mkdir -p "$(dirname "packages/player/src/components/detail")"
    mv "apps/web/src/components/detail" "packages/player/src/components/detail"
    echo "  ✅ Moved apps/web/src/components/detail -> packages/player/src/components/detail"
fi

if [ -d "apps/web/src/containers/Detail" ]; then
    mkdir -p "$(dirname "packages/player/src/components/Detail")"
    mv "apps/web/src/containers/Detail" "packages/player/src/components/Detail"
    echo "  ✅ Moved apps/web/src/containers/Detail -> packages/player/src/components/Detail"
fi

if [ -f "apps/web/src/actions/player.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/stores/player.ts")"
    mv "apps/web/src/actions/player.ts" "packages/player/src/stores/player.ts"
    echo "  ✅ Moved apps/web/src/actions/player.ts -> packages/player/src/stores/player.ts"
fi

if [ -f "apps/web/src/actions/detail.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/stores/detail.ts")"
    mv "apps/web/src/actions/detail.ts" "packages/player/src/stores/detail.ts"
    echo "  ✅ Moved apps/web/src/actions/detail.ts -> packages/player/src/stores/detail.ts"
fi

if [ -f "apps/web/src/actions/episode.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/stores/episode.ts")"
    mv "apps/web/src/actions/episode.ts" "packages/player/src/stores/episode.ts"
    echo "  ✅ Moved apps/web/src/actions/episode.ts -> packages/player/src/stores/episode.ts"
fi

if [ -f "apps/web/src/reducers/player.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/stores/player.ts")"
    mv "apps/web/src/reducers/player.ts" "packages/player/src/stores/player.ts"
    echo "  ✅ Moved apps/web/src/reducers/player.ts -> packages/player/src/stores/player.ts"
fi

if [ -f "apps/web/src/reducers/detail.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/stores/detail.ts")"
    mv "apps/web/src/reducers/detail.ts" "packages/player/src/stores/detail.ts"
    echo "  ✅ Moved apps/web/src/reducers/detail.ts -> packages/player/src/stores/detail.ts"
fi

if [ -f "apps/web/src/reducers/episode.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/stores/episode.ts")"
    mv "apps/web/src/reducers/episode.ts" "packages/player/src/stores/episode.ts"
    echo "  ✅ Moved apps/web/src/reducers/episode.ts -> packages/player/src/stores/episode.ts"
fi

if [ -f "apps/web/src/services/playerServices.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/services/playerServices.ts")"
    mv "apps/web/src/services/playerServices.ts" "packages/player/src/services/playerServices.ts"
    echo "  ✅ Moved apps/web/src/services/playerServices.ts -> packages/player/src/services/playerServices.ts"
fi

if [ -f "apps/web/src/services/detailServices.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/services/detailServices.ts")"
    mv "apps/web/src/services/detailServices.ts" "packages/player/src/services/detailServices.ts"
    echo "  ✅ Moved apps/web/src/services/detailServices.ts -> packages/player/src/services/detailServices.ts"
fi

if [ -f "apps/web/src/constants/player.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/utils/player.ts")"
    mv "apps/web/src/constants/player.ts" "packages/player/src/utils/player.ts"
    echo "  ✅ Moved apps/web/src/constants/player.ts -> packages/player/src/utils/player.ts"
fi

if [ -f "apps/web/src/config/ConfigErrorPlayer.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/utils/ConfigErrorPlayer.ts")"
    mv "apps/web/src/config/ConfigErrorPlayer.ts" "packages/player/src/utils/ConfigErrorPlayer.ts"
    echo "  ✅ Moved apps/web/src/config/ConfigErrorPlayer.ts -> packages/player/src/utils/ConfigErrorPlayer.ts"
fi

if [ -f "apps/web/src/models/contentDetail.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/types/contentDetail.ts")"
    mv "apps/web/src/models/contentDetail.ts" "packages/player/src/types/contentDetail.ts"
    echo "  ✅ Moved apps/web/src/models/contentDetail.ts -> packages/player/src/types/contentDetail.ts"
fi

if [ -f "apps/web/src/models/episodeItem.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/types/episodeItem.ts")"
    mv "apps/web/src/models/episodeItem.ts" "packages/player/src/types/episodeItem.ts"
    echo "  ✅ Moved apps/web/src/models/episodeItem.ts -> packages/player/src/types/episodeItem.ts"
fi

if [ -f "apps/web/src/apis/detailApi.ts" ]; then
    mkdir -p "$(dirname "packages/player/src/services/detailApi.ts")"
    mv "apps/web/src/apis/detailApi.ts" "packages/player/src/services/detailApi.ts"
    echo "  ✅ Moved apps/web/src/apis/detailApi.ts -> packages/player/src/services/detailApi.ts"
fi

echo "Migrating payment module..."

if [ -d "apps/web/src/components/payment" ]; then
    mkdir -p "$(dirname "packages/payment/src/components/payment")"
    mv "apps/web/src/components/payment" "packages/payment/src/components/payment"
    echo "  ✅ Moved apps/web/src/components/payment -> packages/payment/src/components/payment"
fi

if [ -d "apps/web/src/components/billing" ]; then
    mkdir -p "$(dirname "packages/payment/src/components/billing")"
    mv "apps/web/src/components/billing" "packages/payment/src/components/billing"
    echo "  ✅ Moved apps/web/src/components/billing -> packages/payment/src/components/billing"
fi

if [ -d "apps/web/src/containers/Payment" ]; then
    mkdir -p "$(dirname "packages/payment/src/components/Payment")"
    mv "apps/web/src/containers/Payment" "packages/payment/src/components/Payment"
    echo "  ✅ Moved apps/web/src/containers/Payment -> packages/payment/src/components/Payment"
fi

if [ -d "apps/web/src/components/tpbank" ]; then
    mkdir -p "$(dirname "packages/payment/src/components/tpbank")"
    mv "apps/web/src/components/tpbank" "packages/payment/src/components/tpbank"
    echo "  ✅ Moved apps/web/src/components/tpbank -> packages/payment/src/components/tpbank"
fi

if [ -d "apps/web/src/containers/tpbank" ]; then
    mkdir -p "$(dirname "packages/payment/src/components/tpbank")"
    mv "apps/web/src/containers/tpbank" "packages/payment/src/components/tpbank"
    echo "  ✅ Moved apps/web/src/containers/tpbank -> packages/payment/src/components/tpbank"
fi

if [ -d "apps/web/src/apis/billing" ]; then
    mkdir -p "$(dirname "packages/payment/src/components/billing")"
    mv "apps/web/src/apis/billing" "packages/payment/src/components/billing"
    echo "  ✅ Moved apps/web/src/apis/billing -> packages/payment/src/components/billing"
fi

if [ -d "apps/web/src/apis/tpbank" ]; then
    mkdir -p "$(dirname "packages/payment/src/components/tpbank")"
    mv "apps/web/src/apis/tpbank" "packages/payment/src/components/tpbank"
    echo "  ✅ Moved apps/web/src/apis/tpbank -> packages/payment/src/components/tpbank"
fi

if [ -f "apps/web/src/actions/payment.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/stores/payment.ts")"
    mv "apps/web/src/actions/payment.ts" "packages/payment/src/stores/payment.ts"
    echo "  ✅ Moved apps/web/src/actions/payment.ts -> packages/payment/src/stores/payment.ts"
fi

if [ -f "apps/web/src/actions/billing.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/stores/billing.ts")"
    mv "apps/web/src/actions/billing.ts" "packages/payment/src/stores/billing.ts"
    echo "  ✅ Moved apps/web/src/actions/billing.ts -> packages/payment/src/stores/billing.ts"
fi

if [ -f "apps/web/src/actions/momo.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/stores/momo.ts")"
    mv "apps/web/src/actions/momo.ts" "packages/payment/src/stores/momo.ts"
    echo "  ✅ Moved apps/web/src/actions/momo.ts -> packages/payment/src/stores/momo.ts"
fi

if [ -f "apps/web/src/actions/napas.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/stores/napas.ts")"
    mv "apps/web/src/actions/napas.ts" "packages/payment/src/stores/napas.ts"
    echo "  ✅ Moved apps/web/src/actions/napas.ts -> packages/payment/src/stores/napas.ts"
fi

if [ -f "apps/web/src/actions/shopeepay.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/stores/shopeepay.ts")"
    mv "apps/web/src/actions/shopeepay.ts" "packages/payment/src/stores/shopeepay.ts"
    echo "  ✅ Moved apps/web/src/actions/shopeepay.ts -> packages/payment/src/stores/shopeepay.ts"
fi

if [ -f "apps/web/src/actions/viettelPay.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/stores/viettelPay.ts")"
    mv "apps/web/src/actions/viettelPay.ts" "packages/payment/src/stores/viettelPay.ts"
    echo "  ✅ Moved apps/web/src/actions/viettelPay.ts -> packages/payment/src/stores/viettelPay.ts"
fi

if [ -f "apps/web/src/actions/tpbank.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/stores/tpbank.ts")"
    mv "apps/web/src/actions/tpbank.ts" "packages/payment/src/stores/tpbank.ts"
    echo "  ✅ Moved apps/web/src/actions/tpbank.ts -> packages/payment/src/stores/tpbank.ts"
fi

if [ -f "apps/web/src/reducers/payment.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/stores/payment.ts")"
    mv "apps/web/src/reducers/payment.ts" "packages/payment/src/stores/payment.ts"
    echo "  ✅ Moved apps/web/src/reducers/payment.ts -> packages/payment/src/stores/payment.ts"
fi

if [ -f "apps/web/src/reducers/billing.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/stores/billing.ts")"
    mv "apps/web/src/reducers/billing.ts" "packages/payment/src/stores/billing.ts"
    echo "  ✅ Moved apps/web/src/reducers/billing.ts -> packages/payment/src/stores/billing.ts"
fi

if [ -f "apps/web/src/reducers/tpbank.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/stores/tpbank.ts")"
    mv "apps/web/src/reducers/tpbank.ts" "packages/payment/src/stores/tpbank.ts"
    echo "  ✅ Moved apps/web/src/reducers/tpbank.ts -> packages/payment/src/stores/tpbank.ts"
fi

if [ -f "apps/web/src/apis/Payment.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/services/Payment.ts")"
    mv "apps/web/src/apis/Payment.ts" "packages/payment/src/services/Payment.ts"
    echo "  ✅ Moved apps/web/src/apis/Payment.ts -> packages/payment/src/services/Payment.ts"
fi

if [ -f "apps/web/src/apis/PaymentV2.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/services/PaymentV2.ts")"
    mv "apps/web/src/apis/PaymentV2.ts" "packages/payment/src/services/PaymentV2.ts"
    echo "  ✅ Moved apps/web/src/apis/PaymentV2.ts -> packages/payment/src/services/PaymentV2.ts"
fi

if [ -f "apps/web/src/services/paymentServices.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/services/paymentServices.ts")"
    mv "apps/web/src/services/paymentServices.ts" "packages/payment/src/services/paymentServices.ts"
    echo "  ✅ Moved apps/web/src/services/paymentServices.ts -> packages/payment/src/services/paymentServices.ts"
fi

if [ -f "apps/web/src/models/payment.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/types/payment.ts")"
    mv "apps/web/src/models/payment.ts" "packages/payment/src/types/payment.ts"
    echo "  ✅ Moved apps/web/src/models/payment.ts -> packages/payment/src/types/payment.ts"
fi

if [ -f "apps/web/src/models/packageItem.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/types/packageItem.ts")"
    mv "apps/web/src/models/packageItem.ts" "packages/payment/src/types/packageItem.ts"
    echo "  ✅ Moved apps/web/src/models/packageItem.ts -> packages/payment/src/types/packageItem.ts"
fi

if [ -f "apps/web/src/models/transactionItem.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/types/transactionItem.ts")"
    mv "apps/web/src/models/transactionItem.ts" "packages/payment/src/types/transactionItem.ts"
    echo "  ✅ Moved apps/web/src/models/transactionItem.ts -> packages/payment/src/types/transactionItem.ts"
fi

if [ -f "apps/web/src/models/tpbank.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/types/tpbank.ts")"
    mv "apps/web/src/models/tpbank.ts" "packages/payment/src/types/tpbank.ts"
    echo "  ✅ Moved apps/web/src/models/tpbank.ts -> packages/payment/src/types/tpbank.ts"
fi

if [ -f "apps/web/src/config/ConfigPayment.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/utils/ConfigPayment.ts")"
    mv "apps/web/src/config/ConfigPayment.ts" "packages/payment/src/utils/ConfigPayment.ts"
    echo "  ✅ Moved apps/web/src/config/ConfigPayment.ts -> packages/payment/src/utils/ConfigPayment.ts"
fi

if [ -f "apps/web/src/config/ConfigGAPayment.ts" ]; then
    mkdir -p "$(dirname "packages/payment/src/utils/ConfigGAPayment.ts")"
    mv "apps/web/src/config/ConfigGAPayment.ts" "packages/payment/src/utils/ConfigGAPayment.ts"
    echo "  ✅ Moved apps/web/src/config/ConfigGAPayment.ts -> packages/payment/src/utils/ConfigGAPayment.ts"
fi

echo "Migrating ads module..."

if [ -d "apps/web/src/components/MastheadAiAds" ]; then
    mkdir -p "$(dirname "packages/ads/src/components/MastheadAiAds")"
    mv "apps/web/src/components/MastheadAiAds" "packages/ads/src/components/MastheadAiAds"
    echo "  ✅ Moved apps/web/src/components/MastheadAiAds -> packages/ads/src/components/MastheadAiAds"
fi

if [ -d "apps/web/src/components/OutstreamAds" ]; then
    mkdir -p "$(dirname "packages/ads/src/components/OutstreamAds")"
    mv "apps/web/src/components/OutstreamAds" "packages/ads/src/components/OutstreamAds"
    echo "  ✅ Moved apps/web/src/components/OutstreamAds -> packages/ads/src/components/OutstreamAds"
fi

if [ -f "apps/web/src/services/adsServices.ts" ]; then
    mkdir -p "$(dirname "packages/ads/src/services/adsServices.ts")"
    mv "apps/web/src/services/adsServices.ts" "packages/ads/src/services/adsServices.ts"
    echo "  ✅ Moved apps/web/src/services/adsServices.ts -> packages/ads/src/services/adsServices.ts"
fi

if [ -f "apps/web/src/script/AAdsNetwork.ts" ]; then
    mkdir -p "$(dirname "packages/ads/src/utils/AAdsNetwork.ts")"
    mv "apps/web/src/script/AAdsNetwork.ts" "packages/ads/src/utils/AAdsNetwork.ts"
    echo "  ✅ Moved apps/web/src/script/AAdsNetwork.ts -> packages/ads/src/utils/AAdsNetwork.ts"
fi

if [ -f "apps/web/src/script/GGAdsense.ts" ]; then
    mkdir -p "$(dirname "packages/ads/src/utils/GGAdsense.ts")"
    mv "apps/web/src/script/GGAdsense.ts" "packages/ads/src/utils/GGAdsense.ts"
    echo "  ✅ Moved apps/web/src/script/GGAdsense.ts -> packages/ads/src/utils/GGAdsense.ts"
fi

if [ -f "apps/web/src/tracking/GGAdsense.tsx" ]; then
    mkdir -p "$(dirname "packages/ads/src/tracking/GGAdsense.tsx")"
    mv "apps/web/src/tracking/GGAdsense.tsx" "packages/ads/src/tracking/GGAdsense.tsx"
    echo "  ✅ Moved apps/web/src/tracking/GGAdsense.tsx -> packages/ads/src/tracking/GGAdsense.tsx"
fi

echo "Migrating live-tv module..."

if [ -d "apps/web/src/components/liveTV" ]; then
    mkdir -p "$(dirname "packages/live-tv/src/components/liveTV")"
    mv "apps/web/src/components/liveTV" "packages/live-tv/src/components/liveTV"
    echo "  ✅ Moved apps/web/src/components/liveTV -> packages/live-tv/src/components/liveTV"
fi

if [ -d "apps/web/src/containers/LiveTV" ]; then
    mkdir -p "$(dirname "packages/live-tv/src/components/LiveTV")"
    mv "apps/web/src/containers/LiveTV" "packages/live-tv/src/components/LiveTV"
    echo "  ✅ Moved apps/web/src/containers/LiveTV -> packages/live-tv/src/components/LiveTV"
fi

if [ -f "apps/web/src/actions/liveTV.ts" ]; then
    mkdir -p "$(dirname "packages/live-tv/src/stores/liveTV.ts")"
    mv "apps/web/src/actions/liveTV.ts" "packages/live-tv/src/stores/liveTV.ts"
    echo "  ✅ Moved apps/web/src/actions/liveTV.ts -> packages/live-tv/src/stores/liveTV.ts"
fi

if [ -f "apps/web/src/reducers/liveTV.ts" ]; then
    mkdir -p "$(dirname "packages/live-tv/src/stores/liveTV.ts")"
    mv "apps/web/src/reducers/liveTV.ts" "packages/live-tv/src/stores/liveTV.ts"
    echo "  ✅ Moved apps/web/src/reducers/liveTV.ts -> packages/live-tv/src/stores/liveTV.ts"
fi

if [ -f "apps/web/src/apis/liveTVApi.ts" ]; then
    mkdir -p "$(dirname "packages/live-tv/src/services/liveTVApi.ts")"
    mv "apps/web/src/apis/liveTVApi.ts" "packages/live-tv/src/services/liveTVApi.ts"
    echo "  ✅ Moved apps/web/src/apis/liveTVApi.ts -> packages/live-tv/src/services/liveTVApi.ts"
fi

if [ -f "apps/web/src/services/liveTVServices.ts" ]; then
    mkdir -p "$(dirname "packages/live-tv/src/services/liveTVServices.ts")"
    mv "apps/web/src/services/liveTVServices.ts" "packages/live-tv/src/services/liveTVServices.ts"
    echo "  ✅ Moved apps/web/src/services/liveTVServices.ts -> packages/live-tv/src/services/liveTVServices.ts"
fi

if [ -f "apps/web/src/models/channelItem.ts" ]; then
    mkdir -p "$(dirname "packages/live-tv/src/types/channelItem.ts")"
    mv "apps/web/src/models/channelItem.ts" "packages/live-tv/src/types/channelItem.ts"
    echo "  ✅ Moved apps/web/src/models/channelItem.ts -> packages/live-tv/src/types/channelItem.ts"
fi

if [ -f "apps/web/src/models/epgItem.ts" ]; then
    mkdir -p "$(dirname "packages/live-tv/src/types/epgItem.ts")"
    mv "apps/web/src/models/epgItem.ts" "packages/live-tv/src/types/epgItem.ts"
    echo "  ✅ Moved apps/web/src/models/epgItem.ts -> packages/live-tv/src/types/epgItem.ts"
fi

echo "Migrating sport module..."

if [ -d "apps/web/src/components/sport" ]; then
    mkdir -p "$(dirname "packages/sport/src/components/sport")"
    mv "apps/web/src/components/sport" "packages/sport/src/components/sport"
    echo "  ✅ Moved apps/web/src/components/sport -> packages/sport/src/components/sport"
fi

if [ -d "apps/web/src/containers/Sport" ]; then
    mkdir -p "$(dirname "packages/sport/src/components/Sport")"
    mv "apps/web/src/containers/Sport" "packages/sport/src/components/Sport"
    echo "  ✅ Moved apps/web/src/containers/Sport -> packages/sport/src/components/Sport"
fi

if [ -f "apps/web/src/actions/sport.ts" ]; then
    mkdir -p "$(dirname "packages/sport/src/stores/sport.ts")"
    mv "apps/web/src/actions/sport.ts" "packages/sport/src/stores/sport.ts"
    echo "  ✅ Moved apps/web/src/actions/sport.ts -> packages/sport/src/stores/sport.ts"
fi

if [ -f "apps/web/src/reducers/sport.ts" ]; then
    mkdir -p "$(dirname "packages/sport/src/stores/sport.ts")"
    mv "apps/web/src/reducers/sport.ts" "packages/sport/src/stores/sport.ts"
    echo "  ✅ Moved apps/web/src/reducers/sport.ts -> packages/sport/src/stores/sport.ts"
fi

if [ -f "apps/web/src/apis/sportApi.ts" ]; then
    mkdir -p "$(dirname "packages/sport/src/services/sportApi.ts")"
    mv "apps/web/src/apis/sportApi.ts" "packages/sport/src/services/sportApi.ts"
    echo "  ✅ Moved apps/web/src/apis/sportApi.ts -> packages/sport/src/services/sportApi.ts"
fi

if [ -f "apps/web/src/models/CardItemSport.ts" ]; then
    mkdir -p "$(dirname "packages/sport/src/types/CardItemSport.ts")"
    mv "apps/web/src/models/CardItemSport.ts" "packages/sport/src/types/CardItemSport.ts"
    echo "  ✅ Moved apps/web/src/models/CardItemSport.ts -> packages/sport/src/types/CardItemSport.ts"
fi

echo "Migrating search module..."

if [ -d "apps/web/src/components/search" ]; then
    mkdir -p "$(dirname "packages/search/src/components/search")"
    mv "apps/web/src/components/search" "packages/search/src/components/search"
    echo "  ✅ Moved apps/web/src/components/search -> packages/search/src/components/search"
fi

if [ -d "apps/web/src/containers/Search" ]; then
    mkdir -p "$(dirname "packages/search/src/components/Search")"
    mv "apps/web/src/containers/Search" "packages/search/src/components/Search"
    echo "  ✅ Moved apps/web/src/containers/Search -> packages/search/src/components/Search"
fi

if [ -f "apps/web/src/actions/search.ts" ]; then
    mkdir -p "$(dirname "packages/search/src/stores/search.ts")"
    mv "apps/web/src/actions/search.ts" "packages/search/src/stores/search.ts"
    echo "  ✅ Moved apps/web/src/actions/search.ts -> packages/search/src/stores/search.ts"
fi

if [ -f "apps/web/src/reducers/search.ts" ]; then
    mkdir -p "$(dirname "packages/search/src/stores/search.ts")"
    mv "apps/web/src/reducers/search.ts" "packages/search/src/stores/search.ts"
    echo "  ✅ Moved apps/web/src/reducers/search.ts -> packages/search/src/stores/search.ts"
fi

echo "Migrating profile module..."

if [ -d "apps/web/src/components/profile" ]; then
    mkdir -p "$(dirname "packages/profile/src/components/profile")"
    mv "apps/web/src/components/profile" "packages/profile/src/components/profile"
    echo "  ✅ Moved apps/web/src/components/profile -> packages/profile/src/components/profile"
fi

if [ -d "apps/web/src/containers/Profile" ]; then
    mkdir -p "$(dirname "packages/profile/src/components/Profile")"
    mv "apps/web/src/containers/Profile" "packages/profile/src/components/Profile"
    echo "  ✅ Moved apps/web/src/containers/Profile -> packages/profile/src/components/Profile"
fi

if [ -d "apps/web/src/containers/MultiProfile" ]; then
    mkdir -p "$(dirname "packages/profile/src/components/MultiProfile")"
    mv "apps/web/src/containers/MultiProfile" "packages/profile/src/components/MultiProfile"
    echo "  ✅ Moved apps/web/src/containers/MultiProfile -> packages/profile/src/components/MultiProfile"
fi

if [ -d "apps/web/src/apis/MultiProfile" ]; then
    mkdir -p "$(dirname "packages/profile/src/components/MultiProfile")"
    mv "apps/web/src/apis/MultiProfile" "packages/profile/src/components/MultiProfile"
    echo "  ✅ Moved apps/web/src/apis/MultiProfile -> packages/profile/src/components/MultiProfile"
fi

if [ -d "apps/web/src/profile" ]; then
    mkdir -p "$(dirname "packages/profile/src/profile")"
    mv "apps/web/src/profile" "packages/profile/src/profile"
    echo "  ✅ Moved apps/web/src/profile -> packages/profile/src/profile"
fi

if [ -f "apps/web/src/actions/profile.ts" ]; then
    mkdir -p "$(dirname "packages/profile/src/stores/profile.ts")"
    mv "apps/web/src/actions/profile.ts" "packages/profile/src/stores/profile.ts"
    echo "  ✅ Moved apps/web/src/actions/profile.ts -> packages/profile/src/stores/profile.ts"
fi

if [ -f "apps/web/src/actions/multiProfile.ts" ]; then
    mkdir -p "$(dirname "packages/profile/src/stores/multiProfile.ts")"
    mv "apps/web/src/actions/multiProfile.ts" "packages/profile/src/stores/multiProfile.ts"
    echo "  ✅ Moved apps/web/src/actions/multiProfile.ts -> packages/profile/src/stores/multiProfile.ts"
fi

if [ -f "apps/web/src/reducers/profile.ts" ]; then
    mkdir -p "$(dirname "packages/profile/src/stores/profile.ts")"
    mv "apps/web/src/reducers/profile.ts" "packages/profile/src/stores/profile.ts"
    echo "  ✅ Moved apps/web/src/reducers/profile.ts -> packages/profile/src/stores/profile.ts"
fi

if [ -f "apps/web/src/reducers/multiProfile.ts" ]; then
    mkdir -p "$(dirname "packages/profile/src/stores/multiProfile.ts")"
    mv "apps/web/src/reducers/multiProfile.ts" "packages/profile/src/stores/multiProfile.ts"
    echo "  ✅ Moved apps/web/src/reducers/multiProfile.ts -> packages/profile/src/stores/multiProfile.ts"
fi

if [ -f "apps/web/src/services/multiProfileServices.ts" ]; then
    mkdir -p "$(dirname "packages/profile/src/services/multiProfileServices.ts")"
    mv "apps/web/src/services/multiProfileServices.ts" "packages/profile/src/services/multiProfileServices.ts"
    echo "  ✅ Moved apps/web/src/services/multiProfileServices.ts -> packages/profile/src/services/multiProfileServices.ts"
fi

echo "Migration completed!"
echo "Next steps:"
echo "1. Update import statements in the remaining files"
echo "2. Install dependencies: pnpm install"
echo "3. Build all packages: pnpm build"
echo "4. Test the application: pnpm dev"

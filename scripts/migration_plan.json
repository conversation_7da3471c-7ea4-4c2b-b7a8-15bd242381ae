{"root_setup": {"create_root_package_json": true, "create_pnpm_workspace": true, "create_base_tsconfig": true}, "modules_to_create": {"core": {"package_json": true, "structure": {"components": [], "hooks": [], "services": [], "stores": [], "types": [], "utils": [], "constants": [], "tracking": []}}, "ui-kits": {"package_json": true, "structure": {"components": [], "hooks": [], "services": [], "stores": [], "types": [], "utils": [], "constants": [], "tracking": []}}, "auth": {"package_json": true, "structure": {"components": [], "hooks": [], "services": [], "stores": [], "types": [], "utils": [], "constants": [], "tracking": []}}, "player": {"package_json": true, "structure": {"components": [], "hooks": [], "services": [], "stores": [], "types": [], "utils": [], "constants": [], "tracking": []}}, "payment": {"package_json": true, "structure": {"components": [], "hooks": [], "services": [], "stores": [], "types": [], "utils": [], "constants": [], "tracking": []}}, "ads": {"package_json": true, "structure": {"components": [], "hooks": [], "services": [], "stores": [], "types": [], "utils": [], "constants": [], "tracking": []}}, "live-tv": {"package_json": true, "structure": {"components": [], "hooks": [], "services": [], "stores": [], "types": [], "utils": [], "constants": [], "tracking": []}}, "sport": {"package_json": true, "structure": {"components": [], "hooks": [], "services": [], "stores": [], "types": [], "utils": [], "constants": [], "tracking": []}}, "search": {"package_json": true, "structure": {"components": [], "hooks": [], "services": [], "stores": [], "types": [], "utils": [], "constants": [], "tracking": []}}, "profile": {"package_json": true, "structure": {"components": [], "hooks": [], "services": [], "stores": [], "types": [], "utils": [], "constants": [], "tracking": []}}}, "files_to_move": {"core": ["config", "constants", "helpers", "functions", "store", "provider"], "ui-kits": ["components/basic", "components/common", "components/Animation", "components/Button", "components/Card", "components/Checkbox", "components/Input", "components/Modal", "components/Icons", "components/Dropdown", "components/Tooltip", "styles"], "auth": ["components/Authentication", "components/LobbyProfile", "actions/globalAuth.ts", "actions/register.ts", "actions/user.ts", "reducers/globalAuth.ts", "reducers/register.ts", "reducers/user.ts", "apis/userApi.ts", "services/userServices.ts", "models/login.ts", "models/register.ts", "models/LoginResponse.ts", "models/Profile.ts", "models/userType.ts"], "player": ["actions/player.ts", "reducers/player.ts", "services/playerServices.ts", "constants/player.ts", "config/ConfigErrorPlayer.ts", "components/detail", "containers/Detail"], "payment": ["components/payment", "components/billing", "containers/Payment", "actions/payment.ts", "actions/billing.ts", "actions/momo.ts", "actions/napas.ts", "actions/shopeepay.ts", "actions/viettelPay.ts", "actions/tpbank.ts", "reducers/payment.ts", "reducers/billing.ts", "reducers/tpbank.ts", "apis/Payment.ts", "apis/PaymentV2.ts", "apis/billing", "apis/tpbank", "services/paymentServices.ts", "models/payment.ts", "models/packageItem.ts", "models/transactionItem.ts", "models/tpbank.ts", "config/ConfigPayment.ts", "config/ConfigGAPayment.ts"], "ads": ["components/MastheadAiAds", "components/OutstreamAds", "services/adsServices.ts", "script/AAdsNetwork.ts", "script/GGAdsense.ts", "tracking/GGAdsense.tsx"], "live-tv": ["components/liveTV", "containers/LiveTV", "actions/liveTV.ts", "reducers/liveTV.ts", "apis/liveTVApi.ts", "services/liveTVServices.ts", "models/channelItem.ts", "models/epgItem.ts"], "sport": ["components/sport", "containers/Sport", "actions/sport.ts", "reducers/sport.ts", "apis/sportApi.ts", "models/CardItemSport.ts"], "search": ["components/search", "containers/Search", "actions/search.ts", "reducers/search.ts"], "profile": ["components/profile", "containers/Profile", "containers/MultiProfile", "actions/profile.ts", "actions/multiProfile.ts", "reducers/profile.ts", "reducers/multiProfile.ts", "apis/MultiProfile", "services/multiProfileServices.ts", "models/Profile.ts", "profile"]}, "dependencies_to_extract": {}}
#!/usr/bin/env python3

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List

class MonorepoMigrator:
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.web_app_path = self.root_path / "apps" / "web"
        self.src_path = self.web_app_path / "src"
        self.packages_path = self.root_path / "packages"
        
        # Module definitions with their file mappings
        self.modules = {
            "core": {
                "directories": ["config", "constants", "helpers", "functions", "store", "provider"],
                "files": ["customHook.ts", "customRequest.ts"]
            },
            "ui-kits": {
                "directories": [
                    "components/basic", "components/common", "components/Animation",
                    "components/Button", "components/Card", "components/Checkbox",
                    "components/Input", "components/Modal", "components/Icons",
                    "components/Dropdown", "components/Tooltip", "components/DatePicker",
                    "components/CopyText", "components/ToggleCheckbox", "styles"
                ],
                "files": []
            },
            "auth": {
                "directories": ["components/Authentication", "components/LobbyProfile"],
                "files": [
                    "actions/globalAuth.ts", "actions/register.ts", "actions/user.ts",
                    "reducers/globalAuth.ts", "reducers/register.ts", "reducers/user.ts",
                    "apis/userApi.ts", "services/userServices.ts",
                    "models/login.ts", "models/register.ts", "models/LoginResponse.ts",
                    "models/Profile.ts", "models/userType.ts", "models/forgetPassword.ts"
                ]
            },
            "player": {
                "directories": ["components/detail", "containers/Detail"],
                "files": [
                    "actions/player.ts", "actions/detail.ts", "actions/episode.ts",
                    "reducers/player.ts", "reducers/detail.ts", "reducers/episode.ts",
                    "services/playerServices.ts", "services/detailServices.ts",
                    "constants/player.ts", "config/ConfigErrorPlayer.ts",
                    "models/contentDetail.ts", "models/episodeItem.ts",
                    "apis/detailApi.ts"
                ]
            },
            "payment": {
                "directories": [
                    "components/payment", "components/billing", "containers/Payment",
                    "components/tpbank", "containers/tpbank", "apis/billing", "apis/tpbank"
                ],
                "files": [
                    "actions/payment.ts", "actions/billing.ts", "actions/momo.ts",
                    "actions/napas.ts", "actions/shopeepay.ts", "actions/viettelPay.ts",
                    "actions/tpbank.ts", "reducers/payment.ts", "reducers/billing.ts",
                    "reducers/tpbank.ts", "apis/Payment.ts", "apis/PaymentV2.ts",
                    "services/paymentServices.ts", "models/payment.ts",
                    "models/packageItem.ts", "models/transactionItem.ts",
                    "models/tpbank.ts", "config/ConfigPayment.ts", "config/ConfigGAPayment.ts"
                ]
            },
            "ads": {
                "directories": ["components/MastheadAiAds", "components/OutstreamAds"],
                "files": [
                    "services/adsServices.ts", "script/AAdsNetwork.ts",
                    "script/GGAdsense.ts", "tracking/GGAdsense.tsx"
                ]
            },
            "live-tv": {
                "directories": ["components/liveTV", "containers/LiveTV"],
                "files": [
                    "actions/liveTV.ts", "reducers/liveTV.ts", "apis/liveTVApi.ts",
                    "services/liveTVServices.ts", "models/channelItem.ts", "models/epgItem.ts"
                ]
            },
            "sport": {
                "directories": ["components/sport", "containers/Sport"],
                "files": [
                    "actions/sport.ts", "reducers/sport.ts", "apis/sportApi.ts",
                    "models/CardItemSport.ts"
                ]
            },
            "search": {
                "directories": ["components/search", "containers/Search"],
                "files": ["actions/search.ts", "reducers/search.ts"]
            },
            "profile": {
                "directories": [
                    "components/profile", "containers/Profile", "containers/MultiProfile",
                    "apis/MultiProfile", "profile"
                ],
                "files": [
                    "actions/profile.ts", "actions/multiProfile.ts",
                    "reducers/profile.ts", "reducers/multiProfile.ts",
                    "services/multiProfileServices.ts"
                ]
            }
        }
    
    def create_root_structure(self):
        """Create root monorepo structure"""
        print("Creating root monorepo structure...")
        
        # Create packages directory
        self.packages_path.mkdir(exist_ok=True)
        
        # Create root package.json
        root_package = {
            "name": "vieon-web-monorepo",
            "version": "1.0.0",
            "private": True,
            "workspaces": ["apps/*", "packages/*"],
            "packageManager": "pnpm@10.11.0",
            "scripts": {
                "build": "pnpm -r build",
                "dev": "pnpm -r dev",
                "test": "pnpm -r test",
                "lint": "pnpm -r lint",
                "clean": "pnpm -r clean"
            },
            "devDependencies": {
                "typescript": "^5.8.3",
                "@types/node": "^22.15.24",
                "prettier": "^2.8.8",
                "eslint": "^8.55.0"
            }
        }
        
        with open(self.root_path / "package.json", "w") as f:
            json.dump(root_package, f, indent=2)
        
        # Create pnpm-workspace.yaml
        workspace_config = """packages:
  - 'apps/*'
  - 'packages/*'
"""
        with open(self.root_path / "pnpm-workspace.yaml", "w") as f:
            f.write(workspace_config)
        
        # Create base tsconfig.json
        base_tsconfig = {
            "compilerOptions": {
                "target": "es5",
                "lib": ["dom", "dom.iterable", "es6"],
                "allowJs": True,
                "skipLibCheck": True,
                "strict": True,
                "forceConsistentCasingInFileNames": True,
                "noEmit": True,
                "esModuleInterop": True,
                "module": "esnext",
                "moduleResolution": "node",
                "resolveJsonModule": True,
                "isolatedModules": True,
                "jsx": "preserve",
                "incremental": True,
                "baseUrl": ".",
                "paths": {
                    "@vieon/core/*": ["packages/core/src/*"],
                    "@vieon/ui-kits/*": ["packages/ui-kits/src/*"],
                    "@vieon/auth/*": ["packages/auth/src/*"],
                    "@vieon/player/*": ["packages/player/src/*"],
                    "@vieon/payment/*": ["packages/payment/src/*"],
                    "@vieon/ads/*": ["packages/ads/src/*"],
                    "@vieon/live-tv/*": ["packages/live-tv/src/*"],
                    "@vieon/sport/*": ["packages/sport/src/*"],
                    "@vieon/search/*": ["packages/search/src/*"],
                    "@vieon/profile/*": ["packages/profile/src/*"]
                }
            },
            "include": ["**/*.ts", "**/*.tsx"],
            "exclude": ["node_modules", "build", "_next"]
        }
        
        with open(self.root_path / "tsconfig.json", "w") as f:
            json.dump(base_tsconfig, f, indent=2)
        
        print("✅ Root structure created")
    
    def create_module_structure(self, module_name: str):
        """Create the structure for a specific module"""
        module_path = self.packages_path / module_name
        module_path.mkdir(exist_ok=True)
        
        # Create module directories
        directories = ["src/components", "src/hooks", "src/services", "src/stores", 
                      "src/types", "src/utils", "src/tracking"]
        
        for dir_name in directories:
            (module_path / dir_name).mkdir(parents=True, exist_ok=True)
            # Create index.ts files
            index_file = module_path / dir_name / "index.ts"
            if not index_file.exists():
                index_file.write_text("// Export all from this module\n")
        
        # Create main index.ts
        main_index = module_path / "src" / "index.ts"
        if not main_index.exists():
            main_index.write_text(f"""// Main exports for {module_name} module
export * from './components';
export * from './hooks';
export * from './services';
export * from './stores';
export * from './types';
export * from './utils';
export * from './tracking';
""")
        
        # Create package.json for module
        module_package = {
            "name": f"@vieon/{module_name}",
            "version": "1.0.0",
            "main": "src/index.ts",
            "types": "src/index.ts",
            "scripts": {
                "build": "tsc",
                "dev": "tsc --watch",
                "test": "jest",
                "lint": "eslint src/**/*.{ts,tsx}"
            },
            "dependencies": {},
            "devDependencies": {
                "typescript": "^5.8.3",
                "@types/react": "^19.1.6",
                "@types/react-dom": "^19.1.5"
            },
            "peerDependencies": {
                "react": "^18.2.0",
                "react-dom": "^18.2.0"
            }
        }
        
        with open(module_path / "package.json", "w") as f:
            json.dump(module_package, f, indent=2)
        
        # Create README.md
        readme_content = f"""# @vieon/{module_name}

{module_name.title()} module for VieON web application.

## Installation

```bash
pnpm add @vieon/{module_name}
```

## Usage

```typescript
import {{ /* components */ }} from '@vieon/{module_name}';
```

## Development

```bash
pnpm dev
```
"""
        
        with open(module_path / "README.md", "w") as f:
            f.write(readme_content)
        
        print(f"✅ Module structure created for {module_name}")
    
    def create_all_modules(self):
        """Create all module structures"""
        print("Creating all module structures...")
        for module_name in self.modules.keys():
            self.create_module_structure(module_name)
        print("✅ All module structures created")
    
    def generate_migration_script(self):
        """Generate the actual file migration script"""
        script_content = f"""#!/bin/bash

# VieON Web Monorepo Migration Script
# This script moves files from the current structure to the new modular structure

set -e

echo "Starting VieON Web Monorepo Migration..."

# Create backup
echo "Creating backup..."
cp -r apps/web/src apps/web/src_backup

"""
        
        for module_name, config in self.modules.items():
            script_content += f"""
echo "Migrating {module_name} module..."
"""
            
            # Move directories
            for directory in config["directories"]:
                src_dir = f"apps/web/src/{directory}"
                if "/" in directory:
                    # Handle nested directories
                    parts = directory.split("/")
                    if len(parts) == 2:
                        dest_dir = f"packages/{module_name}/src/components/{parts[1]}"
                    else:
                        dest_dir = f"packages/{module_name}/src/{parts[0]}"
                else:
                    dest_dir = f"packages/{module_name}/src/{directory}"
                
                script_content += f"""
if [ -d "{src_dir}" ]; then
    mkdir -p "$(dirname "{dest_dir}")"
    mv "{src_dir}" "{dest_dir}"
    echo "  ✅ Moved {src_dir} -> {dest_dir}"
fi
"""
            
            # Move individual files
            for file_path in config["files"]:
                src_file = f"apps/web/src/{file_path}"
                # Determine destination based on file type
                if file_path.startswith("actions/") or file_path.startswith("reducers/"):
                    dest_file = f"packages/{module_name}/src/stores/{os.path.basename(file_path)}"
                elif file_path.startswith("services/"):
                    dest_file = f"packages/{module_name}/src/services/{os.path.basename(file_path)}"
                elif file_path.startswith("apis/"):
                    dest_file = f"packages/{module_name}/src/services/{os.path.basename(file_path)}"
                elif file_path.startswith("models/"):
                    dest_file = f"packages/{module_name}/src/types/{os.path.basename(file_path)}"
                elif file_path.startswith("config/"):
                    dest_file = f"packages/{module_name}/src/utils/{os.path.basename(file_path)}"
                elif file_path.startswith("constants/"):
                    dest_file = f"packages/{module_name}/src/utils/{os.path.basename(file_path)}"
                elif file_path.startswith("tracking/"):
                    dest_file = f"packages/{module_name}/src/tracking/{os.path.basename(file_path)}"
                else:
                    dest_file = f"packages/{module_name}/src/utils/{os.path.basename(file_path)}"
                
                script_content += f"""
if [ -f "{src_file}" ]; then
    mkdir -p "$(dirname "{dest_file}")"
    mv "{src_file}" "{dest_file}"
    echo "  ✅ Moved {src_file} -> {dest_file}"
fi
"""
        
        script_content += """
echo "Migration completed!"
echo "Next steps:"
echo "1. Update import statements in the remaining files"
echo "2. Install dependencies: pnpm install"
echo "3. Build all packages: pnpm build"
echo "4. Test the application: pnpm dev"
"""
        
        with open(self.root_path / "scripts" / "migrate_files.sh", "w") as f:
            f.write(script_content)
        
        # Make script executable
        os.chmod(self.root_path / "scripts" / "migrate_files.sh", 0o755)
        
        print("✅ Migration script generated at scripts/migrate_files.sh")

if __name__ == "__main__":
    migrator = MonorepoMigrator("/Users/<USER>/Desktop/Project/VieON/web-mono-repo")
    
    print("=== VieON Web Monorepo Migration ===\n")
    
    # Step 1: Create root structure
    migrator.create_root_structure()
    
    # Step 2: Create all module structures
    migrator.create_all_modules()
    
    # Step 3: Generate migration script
    migrator.generate_migration_script()
    
    print("\n=== Migration Setup Complete ===")
    print("To complete the migration, run:")
    print("  chmod +x scripts/migrate_files.sh")
    print("  ./scripts/migrate_files.sh")

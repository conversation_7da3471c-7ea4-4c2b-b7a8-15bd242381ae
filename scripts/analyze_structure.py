#!/usr/bin/env python3

import os
import json
from pathlib import Path
from typing import Dict, List, Set

class ProjectAnalyzer:
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.web_app_path = self.root_path / "apps" / "web"
        self.src_path = self.web_app_path / "src"
        
    def analyze_current_structure(self) -> Dict:
        """Analyze the current project structure"""
        structure = {
            "components": self._analyze_directory(self.src_path / "components"),
            "services": self._analyze_directory(self.src_path / "services"),
            "apis": self._analyze_directory(self.src_path / "apis"),
            "actions": self._analyze_directory(self.src_path / "actions"),
            "reducers": self._analyze_directory(self.src_path / "reducers"),
            "hooks": self._analyze_directory(self.src_path / "hooks"),
            "models": self._analyze_directory(self.src_path / "models"),
            "config": self._analyze_directory(self.src_path / "config"),
            "constants": self._analyze_directory(self.src_path / "constants"),
            "helpers": self._analyze_directory(self.src_path / "helpers"),
            "tracking": self._analyze_directory(self.src_path / "tracking"),
            "containers": self._analyze_directory(self.src_path / "containers"),
            "functions": self._analyze_directory(self.src_path / "functions"),
            "script": self._analyze_directory(self.src_path / "script"),
            "styles": self._analyze_directory(self.src_path / "styles"),
            "store": self._analyze_directory(self.src_path / "store"),
            "provider": self._analyze_directory(self.src_path / "provider"),
        }
        return structure
    
    def _analyze_directory(self, dir_path: Path) -> Dict:
        """Analyze a specific directory"""
        if not dir_path.exists():
            return {"files": [], "subdirs": []}
            
        files = []
        subdirs = []
        
        for item in dir_path.iterdir():
            if item.is_file() and item.suffix in ['.ts', '.tsx', '.js', '.jsx', '.scss', '.css']:
                files.append(item.name)
            elif item.is_dir() and not item.name.startswith('.'):
                subdirs.append({
                    "name": item.name,
                    "files": [f.name for f in item.iterdir() if f.is_file() and f.suffix in ['.ts', '.tsx', '.js', '.jsx', '.scss', '.css']]
                })
        
        return {"files": files, "subdirs": subdirs}
    
    def identify_modules(self) -> Dict[str, List[str]]:
        """Identify potential modules based on current structure"""
        modules = {
            "core": [
                "config", "constants", "helpers", "functions", "store", "provider"
            ],
            "ui-kits": [
                "components/basic", "components/common", "components/Animation", 
                "components/Button", "components/Card", "components/Checkbox",
                "components/Input", "components/Modal", "components/Icons",
                "components/Dropdown", "components/Tooltip", "styles"
            ],
            "auth": [
                "components/Authentication", "components/LobbyProfile",
                "actions/globalAuth.ts", "actions/register.ts", "actions/user.ts",
                "reducers/globalAuth.ts", "reducers/register.ts", "reducers/user.ts",
                "apis/userApi.ts", "services/userServices.ts",
                "models/login.ts", "models/register.ts", "models/LoginResponse.ts",
                "models/Profile.ts", "models/userType.ts"
            ],
            "player": [
                "actions/player.ts", "reducers/player.ts", "services/playerServices.ts",
                "constants/player.ts", "config/ConfigErrorPlayer.ts",
                "components/detail", "containers/Detail"
            ],
            "payment": [
                "components/payment", "components/billing", "containers/Payment",
                "actions/payment.ts", "actions/billing.ts", "actions/momo.ts",
                "actions/napas.ts", "actions/shopeepay.ts", "actions/viettelPay.ts",
                "actions/tpbank.ts", "reducers/payment.ts", "reducers/billing.ts",
                "reducers/tpbank.ts", "apis/Payment.ts", "apis/PaymentV2.ts",
                "apis/billing", "apis/tpbank", "services/paymentServices.ts",
                "models/payment.ts", "models/packageItem.ts", "models/transactionItem.ts",
                "models/tpbank.ts", "config/ConfigPayment.ts", "config/ConfigGAPayment.ts"
            ],
            "ads": [
                "components/MastheadAiAds", "components/OutstreamAds",
                "services/adsServices.ts", "script/AAdsNetwork.ts",
                "script/GGAdsense.ts", "tracking/GGAdsense.tsx"
            ],
            "live-tv": [
                "components/liveTV", "containers/LiveTV", "actions/liveTV.ts",
                "reducers/liveTV.ts", "apis/liveTVApi.ts", "services/liveTVServices.ts",
                "models/channelItem.ts", "models/epgItem.ts"
            ],
            "sport": [
                "components/sport", "containers/Sport", "actions/sport.ts",
                "reducers/sport.ts", "apis/sportApi.ts", "models/CardItemSport.ts"
            ],
            "search": [
                "components/search", "containers/Search", "actions/search.ts",
                "reducers/search.ts"
            ],
            "profile": [
                "components/profile", "containers/Profile", "containers/MultiProfile",
                "actions/profile.ts", "actions/multiProfile.ts", "reducers/profile.ts",
                "reducers/multiProfile.ts", "apis/MultiProfile", "services/multiProfileServices.ts",
                "models/Profile.ts", "profile"
            ]
        }
        return modules
    
    def generate_migration_plan(self) -> Dict:
        """Generate a detailed migration plan"""
        current_structure = self.analyze_current_structure()
        modules = self.identify_modules()
        
        migration_plan = {
            "root_setup": {
                "create_root_package_json": True,
                "create_pnpm_workspace": True,
                "create_base_tsconfig": True
            },
            "modules_to_create": {},
            "files_to_move": {},
            "dependencies_to_extract": {}
        }
        
        for module_name, paths in modules.items():
            migration_plan["modules_to_create"][module_name] = {
                "package_json": True,
                "structure": {
                    "components": [],
                    "hooks": [],
                    "services": [],
                    "stores": [],
                    "types": [],
                    "utils": [],
                    "constants": [],
                    "tracking": []
                }
            }
            
            migration_plan["files_to_move"][module_name] = paths
        
        return migration_plan
    
    def print_analysis(self):
        """Print the analysis results"""
        print("=== VieON Web Project Structure Analysis ===\n")
        
        structure = self.analyze_current_structure()
        print("Current Structure:")
        for category, data in structure.items():
            print(f"\n{category.upper()}:")
            print(f"  Files: {len(data['files'])}")
            print(f"  Subdirectories: {len(data['subdirs'])}")
            for subdir in data['subdirs'][:3]:  # Show first 3 subdirs
                print(f"    - {subdir['name']} ({len(subdir['files'])} files)")
            if len(data['subdirs']) > 3:
                print(f"    ... and {len(data['subdirs']) - 3} more")
        
        print("\n=== Proposed Module Structure ===")
        modules = self.identify_modules()
        for module_name, paths in modules.items():
            print(f"\n{module_name.upper()} MODULE:")
            print(f"  Will contain {len(paths)} components/files")
            for path in paths[:5]:  # Show first 5 paths
                print(f"    - {path}")
            if len(paths) > 5:
                print(f"    ... and {len(paths) - 5} more")

if __name__ == "__main__":
    analyzer = ProjectAnalyzer("/Users/<USER>/Desktop/Project/VieON/web-mono-repo")
    analyzer.print_analysis()
    
    # Save migration plan to JSON
    migration_plan = analyzer.generate_migration_plan()
    with open("scripts/migration_plan.json", "w") as f:
        json.dump(migration_plan, f, indent=2)
    
    print(f"\n=== Migration plan saved to scripts/migration_plan.json ===")

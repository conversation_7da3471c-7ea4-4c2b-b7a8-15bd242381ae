# VieON Web Monorepo Migration Plan

## Overview

This document outlines the migration plan from the current VieON web application structure to a Modular Monorepo Architecture. The migration will improve code organization, reusability, and maintainability.

## Current Structure Analysis

### Current Issues
- All code is in a single `apps/web/src` directory
- 62 component subdirectories mixed together
- Business logic scattered across actions, reducers, and services
- No clear module boundaries
- Difficult to reuse code across different parts of the application

### Current Statistics
- **Components**: 62 subdirectories + 1 file
- **Actions**: 32 files (Redux actions)
- **Reducers**: 28 files (Redux reducers)
- **Services**: 19 files + 2 subdirectories
- **APIs**: 13 files + 5 subdirectories
- **Models**: 32 TypeScript type files
- **Config**: 18 configuration files

## Target Modular Structure

### Root Structure
```
workspace/
├── apps/
│   └── web/                 # Main Next.js application
├── packages/                # Monorepo packages
│   ├── core/               # Core utilities and shared code
│   ├── ui-kits/            # Shared UI components
│   ├── auth/               # Authentication module
│   ├── player/             # Video player module
│   ├── payment/            # Payment processing module
│   ├── ads/                # Advertisement module
│   ├── live-tv/            # Live TV module
│   ├── sport/              # Sports content module
│   ├── search/             # Search functionality module
│   └── profile/            # User profile module
├── package.json            # Root package.json
├── pnpm-workspace.yaml     # Workspace definition
└── tsconfig.json           # Base TypeScript config
```

### Module Structure Template
Each module follows this standardized structure:
```
packages/[module-name]/
├── src/
│   ├── components/         # React components
│   ├── hooks/             # Custom React hooks
│   ├── services/          # API and business logic
│   ├── stores/            # State management (Redux)
│   ├── types/             # TypeScript definitions
│   ├── utils/             # Utility functions
│   ├── tracking/          # Analytics and tracking
│   └── index.ts           # Main module exports
├── package.json           # Module dependencies
└── README.md              # Module documentation
```

## Module Breakdown

### 1. Core Module (`@vieon/core`)
**Purpose**: Shared utilities, configuration, and foundational code

**Contents**:
- Configuration files (18 files from `src/config/`)
- Constants (4 files from `src/constants/`)
- Helper functions (3 files from `src/helpers/`)
- Utility functions (1 file from `src/functions/`)
- Store setup (1 file from `src/store/`)
- Providers (1 file from `src/provider/`)

### 2. UI-Kits Module (`@vieon/ui-kits`)
**Purpose**: Reusable UI components and styling

**Contents**:
- Basic components (`components/basic/`, `components/common/`)
- Form components (`components/Input/`, `components/Checkbox/`, `components/DatePicker/`)
- UI elements (`components/Button/`, `components/Card/`, `components/Modal/`)
- Icons and animations (`components/Icons/`, `components/Animation/`)
- Styling (`src/styles/`)

### 3. Auth Module (`@vieon/auth`)
**Purpose**: Authentication and user management

**Contents**:
- Authentication components (`components/Authentication/`, `components/LobbyProfile/`)
- Auth-related actions and reducers
- User API services
- User type definitions
- Login/register models

### 4. Player Module (`@vieon/player`)
**Purpose**: Video player functionality and content details

**Contents**:
- Detail components (`components/detail/`, `containers/Detail/`)
- Player actions and reducers
- Player services and APIs
- Content and episode models
- Player configuration

### 5. Payment Module (`@vieon/payment`)
**Purpose**: Payment processing and billing

**Contents**:
- Payment components (`components/payment/`, `components/billing/`)
- Payment gateway integrations (Momo, TPBank, ShopeePay, etc.)
- Billing services and APIs
- Payment models and types
- Payment configuration

### 6. Ads Module (`@vieon/ads`)
**Purpose**: Advertisement management

**Contents**:
- Ad components (`components/MastheadAiAds/`, `components/OutstreamAds/`)
- Ad services and tracking
- Google AdSense integration
- Ad network scripts

### 7. Live-TV Module (`@vieon/live-tv`)
**Purpose**: Live television functionality

**Contents**:
- Live TV components and containers
- Live TV actions and reducers
- Live TV API services
- Channel and EPG models

### 8. Sport Module (`@vieon/sport`)
**Purpose**: Sports content features

**Contents**:
- Sport components and containers
- Sport actions and reducers
- Sport API services
- Sport-specific models

### 9. Search Module (`@vieon/search`)
**Purpose**: Search functionality

**Contents**:
- Search components and containers
- Search actions and reducers
- Search services

### 10. Profile Module (`@vieon/profile`)
**Purpose**: User profile management

**Contents**:
- Profile components and containers
- Multi-profile functionality
- Profile actions and reducers
- Profile services and APIs

## Migration Steps

### Phase 1: Setup (✅ Completed)
1. ✅ Create root monorepo structure
2. ✅ Create `package.json` with workspace configuration
3. ✅ Create `pnpm-workspace.yaml`
4. ✅ Create base `tsconfig.json` with path mappings
5. ✅ Create all module directories with proper structure
6. ✅ Generate migration script

### Phase 2: File Migration (Next Steps)
1. **Run the migration script**:
   ```bash
   chmod +x scripts/migrate_files.sh
   ./scripts/migrate_files.sh
   ```

2. **Verify file movements**:
   - Check that files are moved to correct modules
   - Ensure no files are lost during migration

### Phase 3: Import Updates (Manual Work Required)
1. **Update import statements** in remaining files to use new module paths:
   ```typescript
   // Old
   import { SomeComponent } from '../components/SomeComponent';
   
   // New
   import { SomeComponent } from '@vieon/ui-kits';
   ```

2. **Update path mappings** in `apps/web/tsconfig.json`

### Phase 4: Dependencies and Build
1. **Install dependencies**:
   ```bash
   pnpm install
   ```

2. **Build all packages**:
   ```bash
   pnpm build
   ```

3. **Test the application**:
   ```bash
   cd apps/web
   pnpm dev
   ```

### Phase 5: Optimization
1. **Review and optimize module exports**
2. **Add proper TypeScript types**
3. **Update documentation**
4. **Add module-specific tests**

## Benefits After Migration

1. **Better Code Organization**: Clear separation of concerns by business domain
2. **Improved Reusability**: Modules can be easily shared and reused
3. **Independent Development**: Teams can work on different modules independently
4. **Better Testing**: Each module can be tested in isolation
5. **Easier Maintenance**: Smaller, focused codebases are easier to maintain
6. **Scalability**: New features can be added as new modules

## Next Steps

1. **Review this migration plan** and approve the structure
2. **Run the migration script** to move files
3. **Update import statements** throughout the codebase
4. **Test and validate** the migrated structure
5. **Update CI/CD pipelines** to work with the new structure

## Rollback Plan

If issues arise during migration:
1. The original `src` directory is backed up as `src_backup`
2. Simply restore from backup: `rm -rf src && mv src_backup src`
3. Remove the packages directory: `rm -rf packages`
4. Remove root configuration files if needed

{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@vieon/core/*": ["packages/core/src/*"], "@vieon/ui-kits/*": ["packages/ui-kits/src/*"], "@vieon/auth/*": ["packages/auth/src/*"], "@vieon/player/*": ["packages/player/src/*"], "@vieon/payment/*": ["packages/payment/src/*"], "@vieon/ads/*": ["packages/ads/src/*"], "@vieon/live-tv/*": ["packages/live-tv/src/*"], "@vieon/sport/*": ["packages/sport/src/*"], "@vieon/search/*": ["packages/search/src/*"], "@vieon/profile/*": ["packages/profile/src/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "build", "_next"]}